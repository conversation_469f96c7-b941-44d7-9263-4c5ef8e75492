# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.local/homebrew/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.local/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/ltst

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/ltst/build

# Include any dependencies generated for this target.
include tests/CMakeFiles/game_tests.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include tests/CMakeFiles/game_tests.dir/compiler_depend.make

# Include the progress variables for this target.
include tests/CMakeFiles/game_tests.dir/progress.make

# Include the compile flags for this target's objects.
include tests/CMakeFiles/game_tests.dir/flags.make

tests/CMakeFiles/game_tests.dir/codegen:
.PHONY : tests/CMakeFiles/game_tests.dir/codegen

tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.o: tests/CMakeFiles/game_tests.dir/flags.make
tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.o: /Users/<USER>/Desktop/ltst/tests/test_asset_management.cpp
tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.o: tests/CMakeFiles/game_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.o"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.o -MF CMakeFiles/game_tests.dir/test_asset_management.cpp.o.d -o CMakeFiles/game_tests.dir/test_asset_management.cpp.o -c /Users/<USER>/Desktop/ltst/tests/test_asset_management.cpp

tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/game_tests.dir/test_asset_management.cpp.i"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/tests/test_asset_management.cpp > CMakeFiles/game_tests.dir/test_asset_management.cpp.i

tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/game_tests.dir/test_asset_management.cpp.s"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/tests/test_asset_management.cpp -o CMakeFiles/game_tests.dir/test_asset_management.cpp.s

tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o: tests/CMakeFiles/game_tests.dir/flags.make
tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o: /Users/<USER>/Desktop/ltst/src/assets/AssetManager.cpp
tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o: tests/CMakeFiles/game_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o -MF CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o.d -o CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o -c /Users/<USER>/Desktop/ltst/src/assets/AssetManager.cpp

tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.i"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/assets/AssetManager.cpp > CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.i

tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.s"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/assets/AssetManager.cpp -o CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.s

tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o: tests/CMakeFiles/game_tests.dir/flags.make
tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o: /Users/<USER>/Desktop/ltst/src/assets/TextureAsset.cpp
tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o: tests/CMakeFiles/game_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o -MF CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o.d -o CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o -c /Users/<USER>/Desktop/ltst/src/assets/TextureAsset.cpp

tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.i"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/assets/TextureAsset.cpp > CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.i

tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.s"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/assets/TextureAsset.cpp -o CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.s

tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o: tests/CMakeFiles/game_tests.dir/flags.make
tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o: /Users/<USER>/Desktop/ltst/src/assets/ModelAsset.cpp
tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o: tests/CMakeFiles/game_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o -MF CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o.d -o CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o -c /Users/<USER>/Desktop/ltst/src/assets/ModelAsset.cpp

tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.i"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/assets/ModelAsset.cpp > CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.i

tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.s"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/assets/ModelAsset.cpp -o CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.s

tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o: tests/CMakeFiles/game_tests.dir/flags.make
tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o: /Users/<USER>/Desktop/ltst/src/input/InputSystem.cpp
tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o: tests/CMakeFiles/game_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o -MF CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o.d -o CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o -c /Users/<USER>/Desktop/ltst/src/input/InputSystem.cpp

tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.i"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/input/InputSystem.cpp > CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.i

tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.s"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/input/InputSystem.cpp -o CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.s

tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o: tests/CMakeFiles/game_tests.dir/flags.make
tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o: /Users/<USER>/Desktop/ltst/src/input/CocoaInputHandler.mm
tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o: tests/CMakeFiles/game_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building OBJCXX object tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(OBJCXX_DEFINES) $(OBJCXX_INCLUDES) -x objective-c++ $(OBJCXX_FLAGS) -MD -MT tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o -MF CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o.d -o CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o -c /Users/<USER>/Desktop/ltst/src/input/CocoaInputHandler.mm

tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing OBJCXX source to CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.i"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(OBJCXX_DEFINES) $(OBJCXX_INCLUDES) $(OBJCXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/input/CocoaInputHandler.mm > CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.i

tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling OBJCXX source to assembly CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.s"
	cd /Users/<USER>/Desktop/ltst/build/tests && /usr/bin/c++ $(OBJCXX_DEFINES) $(OBJCXX_INCLUDES) $(OBJCXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/input/CocoaInputHandler.mm -o CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.s

# Object files for target game_tests
game_tests_OBJECTS = \
"CMakeFiles/game_tests.dir/test_asset_management.cpp.o" \
"CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o" \
"CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o" \
"CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o" \
"CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o" \
"CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o"

# External object files for target game_tests
game_tests_EXTERNAL_OBJECTS =

tests/game_tests: tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.o
tests/game_tests: tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o
tests/game_tests: tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o
tests/game_tests: tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o
tests/game_tests: tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o
tests/game_tests: tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o
tests/game_tests: tests/CMakeFiles/game_tests.dir/build.make
tests/game_tests: tests/CMakeFiles/game_tests.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX executable game_tests"
	cd /Users/<USER>/Desktop/ltst/build/tests && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/game_tests.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
tests/CMakeFiles/game_tests.dir/build: tests/game_tests
.PHONY : tests/CMakeFiles/game_tests.dir/build

tests/CMakeFiles/game_tests.dir/clean:
	cd /Users/<USER>/Desktop/ltst/build/tests && $(CMAKE_COMMAND) -P CMakeFiles/game_tests.dir/cmake_clean.cmake
.PHONY : tests/CMakeFiles/game_tests.dir/clean

tests/CMakeFiles/game_tests.dir/depend:
	cd /Users/<USER>/Desktop/ltst/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/ltst /Users/<USER>/Desktop/ltst/tests /Users/<USER>/Desktop/ltst/build /Users/<USER>/Desktop/ltst/build/tests /Users/<USER>/Desktop/ltst/build/tests/CMakeFiles/game_tests.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : tests/CMakeFiles/game_tests.dir/depend

