# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
# compile OBJCXX with /usr/bin/c++
CXX_DEFINES = 

CXX_INCLUDES = -I/Users/<USER>/Desktop/ltst/src -I/Users/<USER>/Desktop/ltst/src/engine -I/Users/<USER>/Desktop/ltst/src/graphics -I/Users/<USER>/Desktop/ltst/src/assets -I/Users/<USER>/Desktop/ltst/src/input -I/Users/<USER>/Desktop/ltst/src/camera -I/Users/<USER>/Desktop/ltst/src/physics -I/Users/<USER>/Desktop/ltst/src/ai -I/Users/<USER>/Desktop/ltst/src/utils -I/Users/<USER>/Desktop/ltst/tests/../src -I/Users/<USER>/Desktop/ltst/tests/../src/engine -I/Users/<USER>/Desktop/ltst/tests/../src/graphics -I/Users/<USER>/Desktop/ltst/tests/../src/assets -I/Users/<USER>/Desktop/ltst/tests/../src/physics -I/Users/<USER>/Desktop/ltst/tests/../src/ai -I/Users/<USER>/Desktop/ltst/tests/../src/utils -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks

CXX_FLAGSarm64 = -std=gnu++17 -arch arm64 -mmacosx-version-min=10.15

CXX_FLAGS = -std=gnu++17 -arch arm64 -mmacosx-version-min=10.15

OBJCXX_DEFINES = 

OBJCXX_INCLUDES = -I/Users/<USER>/Desktop/ltst/src -I/Users/<USER>/Desktop/ltst/src/engine -I/Users/<USER>/Desktop/ltst/src/graphics -I/Users/<USER>/Desktop/ltst/src/assets -I/Users/<USER>/Desktop/ltst/src/input -I/Users/<USER>/Desktop/ltst/src/camera -I/Users/<USER>/Desktop/ltst/src/physics -I/Users/<USER>/Desktop/ltst/src/ai -I/Users/<USER>/Desktop/ltst/src/utils -I/Users/<USER>/Desktop/ltst/tests/../src -I/Users/<USER>/Desktop/ltst/tests/../src/engine -I/Users/<USER>/Desktop/ltst/tests/../src/graphics -I/Users/<USER>/Desktop/ltst/tests/../src/assets -I/Users/<USER>/Desktop/ltst/tests/../src/physics -I/Users/<USER>/Desktop/ltst/tests/../src/ai -I/Users/<USER>/Desktop/ltst/tests/../src/utils -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks

OBJCXX_FLAGSarm64 = -std=gnu++17 -arch arm64 -mmacosx-version-min=10.15

OBJCXX_FLAGS = -std=gnu++17 -arch arm64 -mmacosx-version-min=10.15

