/usr/bin/c++  -arch arm64 -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/game_tests.dir/test_asset_management.cpp.o CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o -o game_tests  -framework Metal -framework MetalKit -framework Foundation -framework Cocoa -framework QuartzCore
