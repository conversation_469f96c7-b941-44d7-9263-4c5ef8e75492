
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Desktop/ltst/src/assets/AssetManager.cpp" "tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o" "gcc" "tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o.d"
  "/Users/<USER>/Desktop/ltst/src/assets/ModelAsset.cpp" "tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o" "gcc" "tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o.d"
  "/Users/<USER>/Desktop/ltst/src/assets/TextureAsset.cpp" "tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o" "gcc" "tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o.d"
  "/Users/<USER>/Desktop/ltst/src/input/InputSystem.cpp" "tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o" "gcc" "tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o.d"
  "/Users/<USER>/Desktop/ltst/tests/test_asset_management.cpp" "tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.o" "gcc" "tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.o.d"
  "/Users/<USER>/Desktop/ltst/src/input/CocoaInputHandler.mm" "tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o" "gcc" "tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
