# CMake generated Testfile for 
# Source directory: /Users/<USER>/Desktop/ltst/tests
# Build directory: /Users/<USER>/Desktop/ltst/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test([=[engine_tests]=] "/Users/<USER>/Desktop/ltst/build/tests/game_tests" "--engine")
set_tests_properties([=[engine_tests]=] PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Desktop/ltst/tests/CMakeLists.txt;50;add_test;/Users/<USER>/Desktop/ltst/tests/CMakeLists.txt;0;")
add_test([=[graphics_tests]=] "/Users/<USER>/Desktop/ltst/build/tests/game_tests" "--graphics")
set_tests_properties([=[graphics_tests]=] PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Desktop/ltst/tests/CMakeLists.txt;51;add_test;/Users/<USER>/Desktop/ltst/tests/CMakeLists.txt;0;")
add_test([=[physics_tests]=] "/Users/<USER>/Desktop/ltst/build/tests/game_tests" "--physics")
set_tests_properties([=[physics_tests]=] PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Desktop/ltst/tests/CMakeLists.txt;52;add_test;/Users/<USER>/Desktop/ltst/tests/CMakeLists.txt;0;")
add_test([=[ai_tests]=] "/Users/<USER>/Desktop/ltst/build/tests/game_tests" "--ai")
set_tests_properties([=[ai_tests]=] PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Desktop/ltst/tests/CMakeLists.txt;53;add_test;/Users/<USER>/Desktop/ltst/tests/CMakeLists.txt;0;")
