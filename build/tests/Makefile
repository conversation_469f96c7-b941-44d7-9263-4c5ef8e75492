# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.local/homebrew/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.local/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/ltst

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/ltst/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Users/<USER>/.local/homebrew/bin/ctest $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Users/<USER>/.local/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Users/<USER>/.local/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Users/<USER>/.local/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Users/<USER>/.local/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Users/<USER>/.local/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Users/<USER>/.local/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Users/<USER>/.local/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Users/<USER>/.local/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Desktop/ltst/build && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles /Users/<USER>/Desktop/ltst/build/tests//CMakeFiles/progress.marks
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Desktop/ltst/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
tests/CMakeFiles/game_tests.dir/rule:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/game_tests.dir/rule
.PHONY : tests/CMakeFiles/game_tests.dir/rule

# Convenience name for target.
game_tests: tests/CMakeFiles/game_tests.dir/rule
.PHONY : game_tests

# fast build rule for target.
game_tests/fast:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/build
.PHONY : game_tests/fast

__/src/assets/AssetManager.o: __/src/assets/AssetManager.cpp.o
.PHONY : __/src/assets/AssetManager.o

# target to build an object file
__/src/assets/AssetManager.cpp.o:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.o
.PHONY : __/src/assets/AssetManager.cpp.o

__/src/assets/AssetManager.i: __/src/assets/AssetManager.cpp.i
.PHONY : __/src/assets/AssetManager.i

# target to preprocess a source file
__/src/assets/AssetManager.cpp.i:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.i
.PHONY : __/src/assets/AssetManager.cpp.i

__/src/assets/AssetManager.s: __/src/assets/AssetManager.cpp.s
.PHONY : __/src/assets/AssetManager.s

# target to generate assembly for a file
__/src/assets/AssetManager.cpp.s:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/assets/AssetManager.cpp.s
.PHONY : __/src/assets/AssetManager.cpp.s

__/src/assets/ModelAsset.o: __/src/assets/ModelAsset.cpp.o
.PHONY : __/src/assets/ModelAsset.o

# target to build an object file
__/src/assets/ModelAsset.cpp.o:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.o
.PHONY : __/src/assets/ModelAsset.cpp.o

__/src/assets/ModelAsset.i: __/src/assets/ModelAsset.cpp.i
.PHONY : __/src/assets/ModelAsset.i

# target to preprocess a source file
__/src/assets/ModelAsset.cpp.i:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.i
.PHONY : __/src/assets/ModelAsset.cpp.i

__/src/assets/ModelAsset.s: __/src/assets/ModelAsset.cpp.s
.PHONY : __/src/assets/ModelAsset.s

# target to generate assembly for a file
__/src/assets/ModelAsset.cpp.s:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/assets/ModelAsset.cpp.s
.PHONY : __/src/assets/ModelAsset.cpp.s

__/src/assets/TextureAsset.o: __/src/assets/TextureAsset.cpp.o
.PHONY : __/src/assets/TextureAsset.o

# target to build an object file
__/src/assets/TextureAsset.cpp.o:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.o
.PHONY : __/src/assets/TextureAsset.cpp.o

__/src/assets/TextureAsset.i: __/src/assets/TextureAsset.cpp.i
.PHONY : __/src/assets/TextureAsset.i

# target to preprocess a source file
__/src/assets/TextureAsset.cpp.i:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.i
.PHONY : __/src/assets/TextureAsset.cpp.i

__/src/assets/TextureAsset.s: __/src/assets/TextureAsset.cpp.s
.PHONY : __/src/assets/TextureAsset.s

# target to generate assembly for a file
__/src/assets/TextureAsset.cpp.s:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/assets/TextureAsset.cpp.s
.PHONY : __/src/assets/TextureAsset.cpp.s

__/src/input/CocoaInputHandler.o: __/src/input/CocoaInputHandler.mm.o
.PHONY : __/src/input/CocoaInputHandler.o

# target to build an object file
__/src/input/CocoaInputHandler.mm.o:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/input/CocoaInputHandler.mm.o
.PHONY : __/src/input/CocoaInputHandler.mm.o

__/src/input/InputSystem.o: __/src/input/InputSystem.cpp.o
.PHONY : __/src/input/InputSystem.o

# target to build an object file
__/src/input/InputSystem.cpp.o:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.o
.PHONY : __/src/input/InputSystem.cpp.o

__/src/input/InputSystem.i: __/src/input/InputSystem.cpp.i
.PHONY : __/src/input/InputSystem.i

# target to preprocess a source file
__/src/input/InputSystem.cpp.i:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.i
.PHONY : __/src/input/InputSystem.cpp.i

__/src/input/InputSystem.s: __/src/input/InputSystem.cpp.s
.PHONY : __/src/input/InputSystem.s

# target to generate assembly for a file
__/src/input/InputSystem.cpp.s:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/__/src/input/InputSystem.cpp.s
.PHONY : __/src/input/InputSystem.cpp.s

test_asset_management.o: test_asset_management.cpp.o
.PHONY : test_asset_management.o

# target to build an object file
test_asset_management.cpp.o:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.o
.PHONY : test_asset_management.cpp.o

test_asset_management.i: test_asset_management.cpp.i
.PHONY : test_asset_management.i

# target to preprocess a source file
test_asset_management.cpp.i:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.i
.PHONY : test_asset_management.cpp.i

test_asset_management.s: test_asset_management.cpp.s
.PHONY : test_asset_management.s

# target to generate assembly for a file
test_asset_management.cpp.s:
	cd /Users/<USER>/Desktop/ltst/build && $(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/test_asset_management.cpp.s
.PHONY : test_asset_management.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... game_tests"
	@echo "... __/src/assets/AssetManager.o"
	@echo "... __/src/assets/AssetManager.i"
	@echo "... __/src/assets/AssetManager.s"
	@echo "... __/src/assets/ModelAsset.o"
	@echo "... __/src/assets/ModelAsset.i"
	@echo "... __/src/assets/ModelAsset.s"
	@echo "... __/src/assets/TextureAsset.o"
	@echo "... __/src/assets/TextureAsset.i"
	@echo "... __/src/assets/TextureAsset.s"
	@echo "... __/src/input/CocoaInputHandler.o"
	@echo "... __/src/input/InputSystem.o"
	@echo "... __/src/input/InputSystem.i"
	@echo "... __/src/input/InputSystem.s"
	@echo "... test_asset_management.o"
	@echo "... test_asset_management.i"
	@echo "... test_asset_management.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Desktop/ltst/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

