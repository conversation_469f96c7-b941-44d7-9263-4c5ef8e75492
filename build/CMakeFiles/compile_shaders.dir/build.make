# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.local/homebrew/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.local/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/ltst

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/ltst/build

# Utility rule file for compile_shaders.

# Include any custom commands dependencies for this target.
include CMakeFiles/compile_shaders.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/compile_shaders.dir/progress.make

CMakeFiles/compile_shaders:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Skipping Metal shader compilation"
	/Users/<USER>/.local/homebrew/bin/cmake -E echo Shader\ compilation\ temporarily\ disabled

CMakeFiles/compile_shaders.dir/codegen:
.PHONY : CMakeFiles/compile_shaders.dir/codegen

compile_shaders: CMakeFiles/compile_shaders
compile_shaders: CMakeFiles/compile_shaders.dir/build.make
.PHONY : compile_shaders

# Rule to build all files generated by this target.
CMakeFiles/compile_shaders.dir/build: compile_shaders
.PHONY : CMakeFiles/compile_shaders.dir/build

CMakeFiles/compile_shaders.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/compile_shaders.dir/cmake_clean.cmake
.PHONY : CMakeFiles/compile_shaders.dir/clean

CMakeFiles/compile_shaders.dir/depend:
	cd /Users/<USER>/Desktop/ltst/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/ltst /Users/<USER>/Desktop/ltst /Users/<USER>/Desktop/ltst/build /Users/<USER>/Desktop/ltst/build /Users/<USER>/Desktop/ltst/build/CMakeFiles/compile_shaders.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/compile_shaders.dir/depend

