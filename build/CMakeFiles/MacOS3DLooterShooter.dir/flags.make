# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
# compile OBJCXX with /usr/bin/c++
CXX_DEFINES = -DOPENGL_FALLBACK_AVAILABLE

CXX_INCLUDES = -I/Users/<USER>/Desktop/ltst/src -I/Users/<USER>/Desktop/ltst/src/engine -I/Users/<USER>/Desktop/ltst/src/graphics -I/Users/<USER>/Desktop/ltst/src/assets -I/Users/<USER>/Desktop/ltst/src/input -I/Users/<USER>/Desktop/ltst/src/camera -I/Users/<USER>/Desktop/ltst/src/physics -I/Users/<USER>/Desktop/ltst/src/ai -I/Users/<USER>/Desktop/ltst/src/utils -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks

CXX_FLAGSarm64 = -std=gnu++17 -arch arm64 -mmacosx-version-min=10.15

CXX_FLAGS = -std=gnu++17 -arch arm64 -mmacosx-version-min=10.15

OBJCXX_DEFINES = -DOPENGL_FALLBACK_AVAILABLE

OBJCXX_INCLUDES = -I/Users/<USER>/Desktop/ltst/src -I/Users/<USER>/Desktop/ltst/src/engine -I/Users/<USER>/Desktop/ltst/src/graphics -I/Users/<USER>/Desktop/ltst/src/assets -I/Users/<USER>/Desktop/ltst/src/input -I/Users/<USER>/Desktop/ltst/src/camera -I/Users/<USER>/Desktop/ltst/src/physics -I/Users/<USER>/Desktop/ltst/src/ai -I/Users/<USER>/Desktop/ltst/src/utils -F/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks

OBJCXX_FLAGSarm64 = -std=gnu++17 -arch arm64 -mmacosx-version-min=10.15

OBJCXX_FLAGS = -std=gnu++17 -arch arm64 -mmacosx-version-min=10.15

