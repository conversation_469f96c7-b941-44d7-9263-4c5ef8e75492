# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.local/homebrew/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.local/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/ltst

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/ltst/build

# Utility rule file for copy_assets.

# Include any custom commands dependencies for this target.
include CMakeFiles/copy_assets.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/copy_assets.dir/progress.make

CMakeFiles/copy_assets:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Copying assets to build directory"
	/Users/<USER>/.local/homebrew/bin/cmake -E copy_directory /Users/<USER>/Desktop/ltst/assets /Users/<USER>/Desktop/ltst/build/assets

CMakeFiles/copy_assets.dir/codegen:
.PHONY : CMakeFiles/copy_assets.dir/codegen

copy_assets: CMakeFiles/copy_assets
copy_assets: CMakeFiles/copy_assets.dir/build.make
.PHONY : copy_assets

# Rule to build all files generated by this target.
CMakeFiles/copy_assets.dir/build: copy_assets
.PHONY : CMakeFiles/copy_assets.dir/build

CMakeFiles/copy_assets.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/copy_assets.dir/cmake_clean.cmake
.PHONY : CMakeFiles/copy_assets.dir/clean

CMakeFiles/copy_assets.dir/depend:
	cd /Users/<USER>/Desktop/ltst/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/ltst /Users/<USER>/Desktop/ltst /Users/<USER>/Desktop/ltst/build /Users/<USER>/Desktop/ltst/build /Users/<USER>/Desktop/ltst/build/CMakeFiles/copy_assets.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/copy_assets.dir/depend

