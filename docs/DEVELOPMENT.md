# Development Guide

## Project Structure

The macOS 3D Looter-Shooter Game is organized into modular components:

### Core Directories

- **`src/engine/`** - Core engine systems (ECS, game loop, memory management)
- **`src/graphics/`** - Metal rendering pipeline, shaders, and graphics utilities
- **`src/assets/`** - Asset loading, parsing, and management systems
- **`src/physics/`** - Physics simulation and collision detection
- **`src/ai/`** - Enemy AI and behavior systems
- **`src/game/`** - Game-specific logic and mechanics
- **`src/utils/`** - Utility functions and helper classes

### Asset Directories

- **`assets/models/`** - 3D models (.obj, .gltf)
- **`assets/textures/`** - Texture files (PNG, JPG, etc.)
- **`assets/shaders/`** - Metal shader files (.metal)
- **`assets/audio/`** - Sound effects and music

## Build System

The project uses CMake with macOS-specific configurations:

### Quick Start
```bash
# Setup environment
./scripts/setup.sh

# Build debug version
make debug

# Run the game
make run

# Build release version
make release
```

### Manual Build
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Debug
make -j$(sysctl -n hw.ncpu)
./MacOS3DLooterShooter
```

### Xcode Integration
```bash
make xcode
# Opens Xcode project in build/MacOS3DLooterShooter.xcodeproj
```

## Development Workflow

### 1. Architecture Principles
- **Entity-Component System**: Modular game object architecture
- **Manual Memory Management**: Minimal garbage collection overhead
- **Event-Driven Communication**: Loose coupling between systems
- **Performance First**: Target 60+ FPS on mid-range hardware

### 2. Code Standards
- **C++17**: Modern C++ features with macOS compatibility
- **Documentation**: Comprehensive inline documentation
- **Testing**: Unit tests for all major systems
- **Modularity**: Clean separation between engine and game code

### 3. Graphics Pipeline
- **Primary**: Metal API for optimal macOS performance
- **Fallback**: OpenGL for compatibility (if needed)
- **Rendering**: PBR shaders, LOD system, occlusion culling
- **Performance**: Pre-baked lightmaps, low-poly meshes

### 4. Platform Considerations
- **macOS 10.15+**: Minimum supported version
- **Universal Binary**: Intel and Apple Silicon support
- **Metal Support**: Required for optimal performance
- **Framework Dependencies**: Minimal external dependencies

## Next Development Steps

1. **Core Engine Architecture** - ECS, game loop, memory management
2. **Metal Graphics Pipeline** - Rendering system and shader pipeline
3. **Asset Management** - 3D model and texture loading
4. **Input Systems** - Raw input handling and camera controls
5. **Physics Engine** - Collision detection and bullet physics
6. **Combat Systems** - Weapon mechanics and damage systems
7. **AI Systems** - Enemy behavior and pathfinding
8. **Loot Systems** - Item generation and inventory management
9. **Performance Optimization** - Profiling and optimization
10. **Testing and Polish** - Comprehensive testing and bug fixes

## Performance Targets

- **Frame Rate**: 60+ FPS on mid-range Mac hardware
- **Binary Size**: <500MB total package size
- **Memory Usage**: <2GB RAM usage during gameplay
- **Loading Times**: <10 seconds on SSD storage
- **Compatibility**: Intel and Apple Silicon Macs

## Debugging and Profiling

### Debug Build
```bash
make debug
./MacOS3DLooterShooter --debug
```

### Performance Profiling
- Use Xcode Instruments for detailed profiling
- Built-in performance monitors (to be implemented)
- Memory leak detection with Address Sanitizer

### Testing
```bash
make test
```

## Contributing

1. Follow the established code structure
2. Write comprehensive tests for new features
3. Document all public APIs
4. Ensure compatibility with both Intel and Apple Silicon
5. Maintain performance targets throughout development
