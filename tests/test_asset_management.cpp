//
// test_asset_management.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Tests for Asset Management System
//

#include <iostream>
#include <cassert>
#include <memory>
#include <string>

// Include asset management headers
#include "assets/AssetManager.h"
#include "assets/TextureAsset.h"
#include "assets/ModelAsset.h"

using namespace Engine::Assets;

// Simple test framework
class TestFramework {
public:
    static void RunTest(const std::string& testName, std::function<bool()> testFunc) {
        std::cout << "🧪 Running test: " << testName << std::endl;
        
        try {
            bool result = testFunc();
            if (result) {
                std::cout << "✅ Test passed: " << testName << std::endl;
                s_passedTests++;
            } else {
                std::cout << "❌ Test failed: " << testName << std::endl;
                s_failedTests++;
            }
        } catch (const std::exception& e) {
            std::cout << "💥 Test crashed: " << testName << " - " << e.what() << std::endl;
            s_failedTests++;
        }
        
        s_totalTests++;
    }
    
    static void PrintResults() {
        std::cout << "\n📊 Test Results:" << std::endl;
        std::cout << "Total tests: " << s_totalTests << std::endl;
        std::cout << "Passed: " << s_passedTests << std::endl;
        std::cout << "Failed: " << s_failedTests << std::endl;
        
        if (s_failedTests == 0) {
            std::cout << "🎉 All tests passed!" << std::endl;
        } else {
            std::cout << "⚠️  Some tests failed." << std::endl;
        }
    }
    
    static int GetExitCode() {
        return s_failedTests > 0 ? 1 : 0;
    }

private:
    static int s_totalTests;
    static int s_passedTests;
    static int s_failedTests;
};

int TestFramework::s_totalTests = 0;
int TestFramework::s_passedTests = 0;
int TestFramework::s_failedTests = 0;

// Test functions
bool TestAssetManagerInitialization() {
    auto& assetManager = AssetManager::GetInstance();
    
    // Test initialization
    bool initResult = assetManager.Initialize("assets/");
    if (!initResult) {
        std::cout << "AssetManager initialization failed" << std::endl;
        return false;
    }
    
    // Test statistics
    auto stats = assetManager.GetStatistics();
    if (stats.loadedAssets != 0) {
        std::cout << "Expected 0 loaded assets, got " << stats.loadedAssets << std::endl;
        return false;
    }
    
    return true;
}

bool TestTextureAssetCreation() {
    // Test texture asset creation
    auto texture = std::make_shared<TextureAsset>("test_texture.png");
    
    if (!texture) {
        std::cout << "Failed to create TextureAsset" << std::endl;
        return false;
    }
    
    // Check initial state
    if (texture->IsLoaded()) {
        std::cout << "Texture should not be loaded initially" << std::endl;
        return false;
    }
    
    if (texture->GetPath() != "test_texture.png") {
        std::cout << "Texture path mismatch" << std::endl;
        return false;
    }
    
    return true;
}

bool TestModelAssetCreation() {
    // Test model asset creation
    auto model = std::make_shared<ModelAsset>("test_model.obj");
    
    if (!model) {
        std::cout << "Failed to create ModelAsset" << std::endl;
        return false;
    }
    
    // Check initial state
    if (model->IsLoaded()) {
        std::cout << "Model should not be loaded initially" << std::endl;
        return false;
    }
    
    if (model->GetPath() != "test_model.obj") {
        std::cout << "Model path mismatch" << std::endl;
        return false;
    }
    
    return true;
}

bool TestAssetLoading() {
    auto& assetManager = AssetManager::GetInstance();
    
    // Test texture loading (will create placeholder)
    auto textureHandle = assetManager.LoadAsset<TextureAsset>("test_texture.png");
    if (!textureHandle.IsValid()) {
        std::cout << "Failed to load texture asset" << std::endl;
        return false;
    }
    
    auto texture = assetManager.GetAsset<TextureAsset>(textureHandle);
    if (!texture) {
        std::cout << "Failed to get texture asset from handle" << std::endl;
        return false;
    }
    
    // Test model loading (will create placeholder)
    auto modelHandle = assetManager.LoadAsset<ModelAsset>("test_model.obj");
    if (!modelHandle.IsValid()) {
        std::cout << "Failed to load model asset" << std::endl;
        return false;
    }
    
    auto model = assetManager.GetAsset<ModelAsset>(modelHandle);
    if (!model) {
        std::cout << "Failed to get model asset from handle" << std::endl;
        return false;
    }
    
    return true;
}

bool TestAssetCaching() {
    auto& assetManager = AssetManager::GetInstance();
    
    // Load the same asset twice
    auto handle1 = assetManager.LoadAsset<TextureAsset>("cached_texture.png");
    auto handle2 = assetManager.LoadAsset<TextureAsset>("cached_texture.png");
    
    // Should return the same handle (cached)
    if (handle1.GetID() != handle2.GetID()) {
        std::cout << "Asset caching failed - different handles returned" << std::endl;
        return false;
    }
    
    // Check statistics
    auto stats = assetManager.GetStatistics();
    if (stats.cacheHits == 0) {
        std::cout << "Expected cache hits, got 0" << std::endl;
        return false;
    }
    
    return true;
}

bool TestAssetMemoryUsage() {
    auto texture = std::make_shared<TextureAsset>("memory_test.png");
    
    // Load the texture (creates placeholder)
    if (!texture->Load()) {
        std::cout << "Failed to load texture for memory test" << std::endl;
        return false;
    }
    
    // Check that memory usage is calculated
    if (texture->GetMemoryUsage() == 0) {
        std::cout << "Expected non-zero memory usage" << std::endl;
        return false;
    }
    
    return true;
}

int main(int argc, char* argv[]) {
    std::cout << "🚀 Starting Asset Management System Tests" << std::endl;

    // Run basic tests first
    TestFramework::RunTest("AssetManager Initialization", TestAssetManagerInitialization);
    TestFramework::RunTest("TextureAsset Creation", TestTextureAssetCreation);
    TestFramework::RunTest("ModelAsset Creation", TestModelAssetCreation);

    // Print results
    TestFramework::PrintResults();

    return TestFramework::GetExitCode();
}
