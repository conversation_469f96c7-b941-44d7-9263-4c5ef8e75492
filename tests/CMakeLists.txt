# Test configuration for macOS 3D Looter-Shooter Game

# Find or download a simple testing framework
# For now, we'll use a minimal custom test framework

# Test sources
file(GLOB_RECURSE TEST_SOURCES "*.cpp")

# Required source files for testing
set(REQUIRED_SOURCES
    ../src/assets/AssetManager.cpp
    ../src/assets/TextureAsset.cpp
    ../src/assets/ModelAsset.cpp
    ../src/input/InputSystem.cpp
    ../src/input/CocoaInputHandler.mm
)

# Only create test executable if we have test sources
if(TEST_SOURCES)
    # Create test executable with required sources
    add_executable(game_tests ${TEST_SOURCES} ${REQUIRED_SOURCES})
else()
    # Create a dummy test file for now
    file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/dummy_test.cpp
         "#include <iostream>\nint main() { std::cout << \"No tests implemented yet\" << std::endl; return 0; }")
    add_executable(game_tests ${CMAKE_CURRENT_BINARY_DIR}/dummy_test.cpp)
endif()

# Link with main project libraries (we'll need to refactor main CMakeLists.txt to create libraries)
target_link_libraries(game_tests
    ${METAL_FRAMEWORK}
    ${METALKIT_FRAMEWORK}
    ${FOUNDATION_FRAMEWORK}
    ${COCOA_FRAMEWORK}
    ${QUARTZCORE_FRAMEWORK}
)

# Include main source directories for testing
target_include_directories(game_tests PRIVATE
    ../src
    ../src/engine
    ../src/graphics
    ../src/assets
    ../src/physics
    ../src/ai
    ../src/utils
)

# Add tests
add_test(NAME engine_tests COMMAND game_tests --engine)
add_test(NAME graphics_tests COMMAND game_tests --graphics)
add_test(NAME physics_tests COMMAND game_tests --physics)
add_test(NAME ai_tests COMMAND game_tests --ai)
