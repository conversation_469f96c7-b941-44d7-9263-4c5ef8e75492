# macOS 3D Looter-Shooter Game

A lightweight 3D looter-shooter game built specifically for macOS using Metal API and custom engine architecture.

## Features

- **Graphics**: Metal-based rendering with PBR shaders, LOD system, and occlusion culling
- **Performance**: Targets 60+ FPS on mid-range Mac hardware
- **Compatibility**: Supports both Intel and Apple Silicon Macs
- **Gameplay**: First/third-person shooter with realistic weapon mechanics and loot system
- **AI**: Finite state machine enemy AI with advanced combat behaviors
- **Architecture**: Custom entity-component system with modular design

## Technical Specifications

- **Graphics API**: Metal (primary), OpenGL fallback
- **Target Performance**: 60+ FPS, <500MB binary, <2GB memory
- **Asset Formats**: .obj, .gltf support with custom parsers
- **Architecture**: Entity-component system with manual memory management

## Build Requirements

- macOS 10.15+ (Catalina or later)
- Xcode 12+ with Metal support
- CMake 3.20+
- C++17 compatible compiler

## Project Structure

```
src/
├── engine/          # Core engine systems
├── game/            # Game-specific logic
├── graphics/        # Rendering and Metal pipeline
├── assets/          # Asset management and loading
├── physics/         # Physics and collision systems
├── ai/              # Enemy AI and behavior systems
└── utils/           # Utility functions and helpers

assets/
├── models/          # 3D models and meshes
├── textures/        # Texture files
├── shaders/         # Metal shader files
└── audio/           # Sound effects and music

build/               # Build output directory
docs/                # Documentation
tests/               # Unit and integration tests
```

## Getting Started

1. Clone the repository
2. Run `./scripts/setup.sh` to configure the build environment
3. Build with `make` or open in Xcode
4. Run the game executable

## Development Guidelines

- Code readability and documentation over premature optimization
- Modular systems that can be easily modified or replaced
- Clean separation between engine and game code
- Comprehensive debugging tools and performance monitors
