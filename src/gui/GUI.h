//
// GUI.h
// macOS 3D Looter-Shooter Game Engine
//
// Main header file for the GUI system - includes all GUI components
//

#pragma once

// Core GUI components
#include "UIComponents.h"
#include "UITextComponents.h"
#include "UIWidgets.h"

// GUI systems
#include "UILayoutSystem.h"
#include "UIInputSystem.h"
#include "UIRenderSystem.h"

// GUI management
#include "UIManager.h"
#include "UIWidgetFactory.h"

// GUI demonstration
#include "UIDemo.h"

namespace Engine {
namespace GUI {

// ============================================================================
// GUI System Integration
// ============================================================================

/**
 * Initialize the complete GUI system
 * 
 * @param ecs Pointer to the ECS system
 * @param inputSystem Pointer to the input system
 * @param metalDevice Metal device for rendering
 * @param commandQueue Metal command queue
 * @param renderPassDescriptor Metal render pass descriptor
 * @return true if initialization was successful
 */
bool InitializeGUISystem(ECS* ecs, 
                        Input::InputSystem* inputSystem,
                        id<MTLDevice> metalDevice,
                        id<MTLCommandQueue> commandQueue,
                        MTLRenderPassDescriptor* renderPassDescriptor);

/**
 * Shutdown the complete GUI system
 */
void ShutdownGUISystem();

/**
 * Update the GUI system
 * 
 * @param deltaTime Time elapsed since last frame
 */
void UpdateGUISystem(DeltaTime deltaTime);

/**
 * Render the GUI system
 * 
 * @param commandBuffer Metal command buffer for rendering
 */
void RenderGUISystem(id<MTLCommandBuffer> commandBuffer);

/**
 * Set the screen size for the GUI system
 * 
 * @param width Screen width in pixels
 * @param height Screen height in pixels
 */
void SetGUIScreenSize(float width, float height);

/**
 * Set the canvas size for the GUI system
 * 
 * @param width Canvas width in pixels
 * @param height Canvas height in pixels
 */
void SetGUICanvasSize(float width, float height);

/**
 * Get the main UI manager instance
 * 
 * @return Pointer to the UI manager
 */
UIManager* GetUIManager();

/**
 * Get the GUI demo instance
 * 
 * @return Pointer to the GUI demo
 */
UIDemo* GetGUIDemo();

// ============================================================================
// Quick Setup Functions
// ============================================================================

/**
 * Create a basic game menu system
 */
void CreateGameMenus();

/**
 * Create a basic game HUD
 */
void CreateGameHUD();

/**
 * Show the main menu
 */
void ShowMainMenu();

/**
 * Show the game HUD
 */
void ShowGameHUD();

/**
 * Show the settings menu
 */
void ShowSettingsMenu();

/**
 * Hide all GUI elements
 */
void HideAllGUI();

// ============================================================================
// Event Handling
// ============================================================================

/**
 * Register a global GUI event handler
 * 
 * @param handler Function to handle GUI events
 */
void RegisterGUIEventHandler(std::function<void(const UIInputEvent&)> handler);

/**
 * Unregister the global GUI event handler
 */
void UnregisterGUIEventHandler();

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Convert screen coordinates to canvas coordinates
 * 
 * @param screenPos Screen position
 * @return Canvas position
 */
Vector2 ScreenToCanvas(const Vector2& screenPos);

/**
 * Convert canvas coordinates to screen coordinates
 * 
 * @param canvasPos Canvas position
 * @return Screen position
 */
Vector2 CanvasToScreen(const Vector2& canvasPos);

/**
 * Check if a point is inside a UI element
 * 
 * @param point Point to test
 * @param entity UI entity to test against
 * @return true if point is inside the entity
 */
bool IsPointInUIElement(const Vector2& point, Entity entity);

/**
 * Get the UI element at a specific position
 * 
 * @param position Position to test
 * @return Entity at the position, or INVALID_ENTITY if none
 */
Entity GetUIElementAtPosition(const Vector2& position);

// ============================================================================
// Debug and Development
// ============================================================================

/**
 * Enable or disable GUI debug mode
 * 
 * @param enabled Whether to enable debug mode
 */
void SetGUIDebugMode(bool enabled);

/**
 * Check if GUI debug mode is enabled
 * 
 * @return true if debug mode is enabled
 */
bool IsGUIDebugMode();

/**
 * Print GUI system statistics
 */
void PrintGUIStats();

/**
 * Reload GUI styles and themes
 */
void ReloadGUIStyles();

// ============================================================================
// Configuration
// ============================================================================

/**
 * Set the default GUI configuration
 * 
 * @param config GUI configuration
 */
void SetGUIConfig(const UIConfig& config);

/**
 * Get the current GUI configuration
 * 
 * @return Current GUI configuration
 */
const UIConfig& GetGUIConfig();

/**
 * Load GUI configuration from file
 * 
 * @param filename Configuration file path
 * @return true if loading was successful
 */
bool LoadGUIConfig(const std::string& filename);

/**
 * Save GUI configuration to file
 * 
 * @param filename Configuration file path
 * @return true if saving was successful
 */
bool SaveGUIConfig(const std::string& filename);

// ============================================================================
// Theme and Styling
// ============================================================================

/**
 * Load a GUI theme
 * 
 * @param themeName Name of the theme to load
 * @return true if theme was loaded successfully
 */
bool LoadGUITheme(const std::string& themeName);

/**
 * Set default colors for GUI elements
 * 
 * @param primary Primary color
 * @param secondary Secondary color
 * @param accent Accent color
 * @param background Background color
 * @param text Text color
 */
void SetGUIColors(const Color& primary, const Color& secondary, const Color& accent,
                  const Color& background, const Color& text);

/**
 * Set default font for GUI elements
 * 
 * @param fontName Font name
 * @param fontSize Default font size
 */
void SetGUIFont(const std::string& fontName, float fontSize);

// ============================================================================
// Performance and Optimization
// ============================================================================

/**
 * Enable or disable GUI batching
 * 
 * @param enabled Whether to enable batching
 */
void SetGUIBatching(bool enabled);

/**
 * Set maximum number of vertices per frame
 * 
 * @param maxVertices Maximum vertices
 */
void SetGUIMaxVertices(int maxVertices);

/**
 * Get GUI rendering statistics
 * 
 * @return Rendering statistics
 */
const UIRenderSystem::RenderStats& GetGUIRenderStats();

/**
 * Get GUI update statistics
 * 
 * @return Update statistics
 */
const UIManager::UIStats& GetGUIUpdateStats();

} // namespace GUI
} // namespace Engine

// ============================================================================
// Convenience Macros
// ============================================================================

// Quick GUI element creation macros
#define GUI_BUTTON(text, x, y, w, h, callback) \
    Engine::GUI::CreateButton(text, x, y, w, h, callback)

#define GUI_LABEL(text, x, y, size) \
    Engine::GUI::CreateLabel(text, x, y, size)

#define GUI_PANEL(x, y, w, h, color) \
    Engine::GUI::CreatePanel(x, y, w, h, color)

#define GUI_TEXTFIELD(x, y, w, h, placeholder) \
    Engine::GUI::CreateTextField(x, y, w, h, placeholder)

#define GUI_SLIDER(x, y, w, h, min, max, initial) \
    Engine::GUI::CreateSlider(x, y, w, h, min, max, initial)

// Layout macros
#define GUI_HORIZONTAL_LAYOUT(x, y, w, h, spacing) \
    Engine::GUI::CreateHorizontalLayout(x, y, w, h, spacing)

#define GUI_VERTICAL_LAYOUT(x, y, w, h, spacing) \
    Engine::GUI::CreateVerticalLayout(x, y, w, h, spacing)

#define GUI_GRID_LAYOUT(x, y, w, h, cols, rows) \
    Engine::GUI::CreateGridLayout(x, y, w, h, cols, rows)

// Color constants for easy use
#define GUI_COLOR_PRIMARY Engine::GUI::Color(0.2f, 0.6f, 1.0f, 1.0f)
#define GUI_COLOR_SECONDARY Engine::GUI::Color(0.6f, 0.6f, 0.6f, 1.0f)
#define GUI_COLOR_SUCCESS Engine::GUI::Color(0.2f, 0.8f, 0.2f, 1.0f)
#define GUI_COLOR_WARNING Engine::GUI::Color(1.0f, 0.8f, 0.2f, 1.0f)
#define GUI_COLOR_DANGER Engine::GUI::Color(1.0f, 0.3f, 0.3f, 1.0f)
#define GUI_COLOR_DARK Engine::GUI::Color(0.2f, 0.2f, 0.2f, 1.0f)
#define GUI_COLOR_LIGHT Engine::GUI::Color(0.9f, 0.9f, 0.9f, 1.0f)
