# CMakeLists.txt for GUI System
# macOS 3D Looter-Shooter Game Engine

cmake_minimum_required(VERSION 3.20)

# GUI System Library
set(GUI_SOURCES
    # Core GUI components
    UIComponents.cpp
    UITextComponents.cpp
    UIWidgets.cpp
    
    # GUI systems
    UILayoutSystem.cpp
    UIInputSystem.cpp
    UIRenderSystem.mm
    
    # GUI management
    UIManager.cpp
    UIWidgetFactory.cpp
    
    # GUI demonstration
    UIDemo.cpp
    
    # Main GUI integration
    GUI.cpp
)

set(GUI_HEADERS
    # Core GUI components
    UIComponents.h
    UITextComponents.h
    UIWidgets.h
    
    # GUI systems
    UILayoutSystem.h
    UIInputSystem.h
    UIRenderSystem.h
    
    # GUI management
    UIManager.h
    UIWidgetFactory.h
    
    # GUI demonstration
    UIDemo.h
    
    # Main GUI integration
    GUI.h
)

# Create GUI library
add_library(GUI STATIC ${GUI_SOURCES} ${GUI_HEADERS})

# Set target properties
set_target_properties(GUI PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    OBJCXX_STANDARD 17
    OBJCXX_STANDARD_REQUIRED ON
)

# Include directories
target_include_directories(GUI PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/..
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link with required frameworks (macOS)
if(APPLE)
    find_library(METAL_FRAMEWORK Metal)
    find_library(METALKIT_FRAMEWORK MetalKit)
    find_library(CORETEXT_FRAMEWORK CoreText)
    find_library(COREGRAPHICS_FRAMEWORK CoreGraphics)
    find_library(FOUNDATION_FRAMEWORK Foundation)
    find_library(IMAGEIO_FRAMEWORK ImageIO)
    
    target_link_libraries(GUI PUBLIC
        ${METAL_FRAMEWORK}
        ${METALKIT_FRAMEWORK}
        ${CORETEXT_FRAMEWORK}
        ${COREGRAPHICS_FRAMEWORK}
        ${FOUNDATION_FRAMEWORK}
        ${IMAGEIO_FRAMEWORK}
    )
endif()

# Link with engine components
target_link_libraries(GUI PUBLIC
    Engine
    Input
    Graphics
    Assets
)

# Compiler-specific options
if(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    target_compile_options(GUI PRIVATE
        -Wall
        -Wextra
        -Wpedantic
        -Wno-unused-parameter
        -fobjc-arc  # Enable ARC for Objective-C++
    )
endif()

# Define preprocessor macros
target_compile_definitions(GUI PRIVATE
    GUI_SYSTEM_VERSION_MAJOR=1
    GUI_SYSTEM_VERSION_MINOR=0
    GUI_SYSTEM_VERSION_PATCH=0
)

# Debug configuration
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(GUI PRIVATE
        GUI_DEBUG=1
        GUI_ENABLE_VALIDATION=1
    )
endif()

# Install targets
install(TARGETS GUI
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${GUI_HEADERS}
    DESTINATION include/gui
)

# Create GUI example executable (optional)
option(BUILD_GUI_EXAMPLES "Build GUI system examples" ON)

if(BUILD_GUI_EXAMPLES)
    add_executable(gui_example
        ${CMAKE_CURRENT_SOURCE_DIR}/../../examples/gui_example.cpp
    )
    
    target_link_libraries(gui_example PRIVATE
        GUI
        Engine
        Input
    )
    
    set_target_properties(gui_example PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
    )
    
    # Install example
    install(TARGETS gui_example
        RUNTIME DESTINATION bin/examples
    )
endif()

# Documentation
find_package(Doxygen QUIET)
if(DOXYGEN_FOUND)
    set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
    
    if(EXISTS ${DOXYGEN_IN})
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(gui_docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating GUI system documentation with Doxygen"
            VERBATIM
        )
        
        install(DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/html/
            DESTINATION share/doc/gui
            OPTIONAL
        )
    endif()
endif()

# Testing
option(BUILD_GUI_TESTS "Build GUI system tests" OFF)

if(BUILD_GUI_TESTS)
    enable_testing()
    
    # Add test subdirectory if it exists
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/tests)
        add_subdirectory(tests)
    endif()
endif()

# Package configuration
include(CMakePackageConfigHelpers)

configure_package_config_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/GUIConfig.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/GUIConfig.cmake
    INSTALL_DESTINATION lib/cmake/GUI
)

write_basic_package_version_file(
    ${CMAKE_CURRENT_BINARY_DIR}/GUIConfigVersion.cmake
    VERSION 1.0.0
    COMPATIBILITY SameMajorVersion
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/GUIConfig.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/GUIConfigVersion.cmake
    DESTINATION lib/cmake/GUI
)

# Print configuration summary
message(STATUS "GUI System Configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Build examples: ${BUILD_GUI_EXAMPLES}")
message(STATUS "  Build tests: ${BUILD_GUI_TESTS}")
message(STATUS "  Build documentation: ${DOXYGEN_FOUND}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")

# Export targets
export(TARGETS GUI
    FILE ${CMAKE_CURRENT_BINARY_DIR}/GUITargets.cmake
)

install(EXPORT GUITargets
    FILE GUITargets.cmake
    DESTINATION lib/cmake/GUI
)
