//
// UIRenderSystem.h
// macOS 3D Looter-Shooter Game Engine
//
// GUI rendering system using Metal API
//

#pragma once

#include "engine/System.h"
#include "UIComponents.h"
#include "UITextComponents.h"
#include "graphics/MetalDevice.h"

#ifdef __OBJC__
#import <Metal/Metal.h>
#import <MetalKit/MetalKit.h>
#import <CoreText/CoreText.h>
#else
#include <Metal/Metal.h>
#include <MetalKit/MetalKit.h>
#include <CoreText/CoreText.h>
#endif

#include <memory>
#include <vector>
#include <unordered_map>

namespace Engine {
namespace GUI {

// ============================================================================
// GUI Rendering Data Structures
// ============================================================================

// Vertex structure for GUI rendering
struct UIVertex {
    Vector2 position;
    Vector2 texCoord;
    Color color;
    
    UIVertex() = default;
    UIVertex(const Vector2& pos, const Vector2& uv, const Color& col)
        : position(pos), texCoord(uv), color(col) {}
};

// Render batch for efficient drawing
struct UIRenderBatch {
    std::vector<UIVertex> vertices;
    std::vector<uint16_t> indices;
    id<MTLTexture> texture = nil;
    int sortingOrder = 0;
    
    void Clear() {
        vertices.clear();
        indices.clear();
        texture = nil;
        sortingOrder = 0;
    }
    
    void AddQuad(const Rect& rect, const Rect& uvRect, const Color& color);
    void AddQuad(const Vector2& topLeft, const Vector2& topRight, 
                const Vector2& bottomLeft, const Vector2& bottomRight,
                const Rect& uvRect, const Color& color);
};

// Font rendering data
struct UIFont {
    std::string name;
    float size;
    CTFontRef ctFont = nullptr;
    std::unordered_map<char, id<MTLTexture>> glyphTextures;
    std::unordered_map<char, Rect> glyphMetrics;
    
    UIFont() = default;
    UIFont(const std::string& fontName, float fontSize);
    ~UIFont();
    
    void LoadGlyph(char character, id<MTLDevice> device);
    Rect GetGlyphMetrics(char character);
    id<MTLTexture> GetGlyphTexture(char character);
};

// ============================================================================
// GUI Render System
// ============================================================================

class UIRenderSystem : public System {
public:
    UIRenderSystem();
    ~UIRenderSystem();
    
    // System interface
    void Initialize() override;
    void Update(DeltaTime deltaTime) override;
    void Shutdown() override;
    const char* GetName() const override { return "UIRenderSystem"; }
    
    // Metal integration
    void SetMetalDevice(id<MTLDevice> device);
    void SetCommandQueue(id<MTLCommandQueue> commandQueue);
    void SetRenderPassDescriptor(MTLRenderPassDescriptor* renderPassDescriptor);
    
    // Rendering
    void Render(id<MTLCommandBuffer> commandBuffer);
    void RenderUI(id<MTLRenderCommandEncoder> renderEncoder);
    
    // Canvas management
    void SetCanvasSize(const Vector2& size);
    Vector2 GetCanvasSize() const { return m_canvasSize; }
    void SetScreenSize(const Vector2& size);
    Vector2 GetScreenSize() const { return m_screenSize; }
    
    // Texture management
    id<MTLTexture> LoadTexture(const std::string& path);
    id<MTLTexture> CreateTexture(int width, int height, const void* data);
    id<MTLTexture> GetWhiteTexture();
    
    // Font management
    UIFont* LoadFont(const std::string& name, float size);
    UIFont* GetFont(const std::string& name, float size);
    void UnloadFont(const std::string& name, float size);
    
    // Render state
    void SetProjectionMatrix(const Matrix4& projection);
    Matrix4 GetProjectionMatrix() const { return m_projectionMatrix; }
    
    // Debug rendering
    void SetDebugMode(bool enabled) { m_debugMode = enabled; }
    bool IsDebugMode() const { return m_debugMode; }
    void RenderDebugInfo();
    
    // Statistics
    struct RenderStats {
        int drawCalls = 0;
        int triangles = 0;
        int vertices = 0;
        int batches = 0;
        int textureBinds = 0;
        float frameTime = 0.0f;
    };
    
    const RenderStats& GetStats() const { return m_stats; }
    void ResetStats();

private:
    // Metal resources
    id<MTLDevice> m_device = nil;
    id<MTLCommandQueue> m_commandQueue = nil;
    MTLRenderPassDescriptor* m_renderPassDescriptor = nil;
    
    // Shaders and pipeline states
    id<MTLRenderPipelineState> m_uiPipelineState = nil;
    id<MTLRenderPipelineState> m_textPipelineState = nil;
    id<MTLDepthStencilState> m_depthStencilState = nil;
    
    // Buffers
    id<MTLBuffer> m_vertexBuffer = nil;
    id<MTLBuffer> m_indexBuffer = nil;
    id<MTLBuffer> m_uniformBuffer = nil;
    
    // Textures
    id<MTLTexture> m_whiteTexture = nil;
    std::unordered_map<std::string, id<MTLTexture>> m_textureCache;
    
    // Fonts
    std::unordered_map<std::string, std::unique_ptr<UIFont>> m_fontCache;
    
    // Render batches
    std::vector<UIRenderBatch> m_renderBatches;
    std::vector<UIRenderBatch> m_textBatches;
    
    // Canvas and screen properties
    Vector2 m_canvasSize = Vector2(1920.0f, 1080.0f);
    Vector2 m_screenSize = Vector2(1920.0f, 1080.0f);
    Matrix4 m_projectionMatrix;
    
    // Render state
    bool m_debugMode = false;
    RenderStats m_stats;
    
    // Internal methods
    bool InitializeMetalResources();
    void CreateShaders();
    void CreateBuffers();
    void CreateDefaultTextures();
    
    // Rendering helpers
    void CollectRenderData();
    void CollectEntityRenderData(Entity entity);
    void RenderBatches(id<MTLRenderCommandEncoder> renderEncoder);
    void RenderTextBatches(id<MTLRenderCommandEncoder> renderEncoder);
    
    // Batch management
    void ClearBatches();
    UIRenderBatch* GetBatch(id<MTLTexture> texture, int sortingOrder);
    UIRenderBatch* GetTextBatch(id<MTLTexture> texture, int sortingOrder);
    void SortBatches();
    
    // Component rendering
    void RenderPanel(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIPanel& panel);
    void RenderImage(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIImage& image);
    void RenderText(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIText& text);
    void RenderButton(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIButton& button);
    void RenderSlider(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UISlider& slider);
    void RenderProgressBar(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIProgressBar& progressBar);
    
    // Text rendering helpers
    void RenderTextString(const std::string& text, const Rect& bounds, const UIText& textComponent, const Color& color);
    Vector2 MeasureText(const std::string& text, const UIText& textComponent);
    std::vector<std::string> WrapText(const std::string& text, float maxWidth, const UIText& textComponent);
    
    // Coordinate conversion
    Vector2 ScreenToCanvas(const Vector2& screenPos);
    Vector2 CanvasToScreen(const Vector2& canvasPos);
    Rect ScreenToCanvas(const Rect& screenRect);
    Rect CanvasToScreen(const Rect& canvasRect);
    
    // Matrix helpers
    Matrix4 CreateOrthographicMatrix(float left, float right, float bottom, float top, float near, float far);
    void UpdateProjectionMatrix();
    
    bool m_initialized = false;
};

// ============================================================================
// Matrix4 Helper (if not already defined elsewhere)
// ============================================================================

struct Matrix4 {
    float m[16];
    
    Matrix4();
    Matrix4(float diagonal);
    
    static Matrix4 Identity();
    static Matrix4 Orthographic(float left, float right, float bottom, float top, float near, float far);
    
    Matrix4 operator*(const Matrix4& other) const;
    Vector2 operator*(const Vector2& vec) const;
};

} // namespace GUI
} // namespace Engine
