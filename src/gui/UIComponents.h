//
// UIComponents.h
// macOS 3D Looter-Shooter Game Engine
//
// Core GUI components for the Entity-Component System
//

#pragma once

#include "engine/Component.h"
#include "engine/Types.h"
#include <string>
#include <functional>
#include <memory>

namespace Engine {
namespace GUI {

// ============================================================================
// Forward Declarations
// ============================================================================

class UIElement;
class UICanvas;

// ============================================================================
// GUI Types and Enums
// ============================================================================

enum class AnchorType {
    TopLeft,
    TopCenter,
    TopRight,
    MiddleLeft,
    MiddleCenter,
    MiddleRight,
    BottomLeft,
    BottomCenter,
    BottomRight,
    Stretch,
    Custom
};

enum class UIState {
    Normal,
    Hovered,
    Pressed,
    Disabled,
    Selected
};

struct Vector2 {
    float x, y;
    
    Vector2() : x(0.0f), y(0.0f) {}
    Vector2(float x_, float y_) : x(x_), y(y_) {}
    
    Vector2 operator+(const Vector2& other) const { return Vector2(x + other.x, y + other.y); }
    Vector2 operator-(const Vector2& other) const { return Vector2(x - other.x, y - other.y); }
    Vector2 operator*(float scalar) const { return Vector2(x * scalar, y * scalar); }
};

struct Rect {
    float x, y, width, height;
    
    Rect() : x(0.0f), y(0.0f), width(0.0f), height(0.0f) {}
    Rect(float x_, float y_, float w_, float h_) : x(x_), y(y_), width(w_), height(h_) {}
    
    bool Contains(const Vector2& point) const {
        return point.x >= x && point.x <= x + width &&
               point.y >= y && point.y <= y + height;
    }
    
    Vector2 GetCenter() const { return Vector2(x + width * 0.5f, y + height * 0.5f); }
    Vector2 GetMin() const { return Vector2(x, y); }
    Vector2 GetMax() const { return Vector2(x + width, y + height); }
};

struct Color {
    float r, g, b, a;
    
    Color() : r(1.0f), g(1.0f), b(1.0f), a(1.0f) {}
    Color(float r_, float g_, float b_, float a_ = 1.0f) : r(r_), g(g_), b(b_), a(a_) {}
    
    static Color White() { return Color(1.0f, 1.0f, 1.0f, 1.0f); }
    static Color Black() { return Color(0.0f, 0.0f, 0.0f, 1.0f); }
    static Color Red() { return Color(1.0f, 0.0f, 0.0f, 1.0f); }
    static Color Green() { return Color(0.0f, 1.0f, 0.0f, 1.0f); }
    static Color Blue() { return Color(0.0f, 0.0f, 1.0f, 1.0f); }
    static Color Transparent() { return Color(0.0f, 0.0f, 0.0f, 0.0f); }
};

// ============================================================================
// Core GUI Components
// ============================================================================

// UI Transform Component - Handles positioning, sizing, and hierarchy
struct UITransform {
    // Position and size
    Vector2 position;           // Local position relative to parent
    Vector2 size;              // Size in pixels or relative units
    Vector2 pivot;             // Pivot point (0,0 = top-left, 1,1 = bottom-right)
    Vector2 anchorMin;         // Anchor minimum (for relative positioning)
    Vector2 anchorMax;         // Anchor maximum (for relative positioning)
    
    // Hierarchy
    Entity parent = INVALID_ENTITY;
    std::vector<Entity> children;
    
    // Layout properties
    bool useRelativeSize = false;  // If true, size is relative to parent
    bool maintainAspectRatio = false;
    float aspectRatio = 1.0f;
    
    // Computed properties (updated by layout system)
    Rect worldRect;            // Final world-space rectangle
    Vector2 worldPosition;     // Final world position
    Vector2 worldSize;         // Final world size
    
    UITransform() 
        : position(0.0f, 0.0f)
        , size(100.0f, 100.0f)
        , pivot(0.0f, 0.0f)
        , anchorMin(0.0f, 0.0f)
        , anchorMax(0.0f, 0.0f)
        , worldRect(0.0f, 0.0f, 100.0f, 100.0f)
        , worldPosition(0.0f, 0.0f)
        , worldSize(100.0f, 100.0f)
    {}
    
    UITransform(const Vector2& pos, const Vector2& sz)
        : position(pos)
        , size(sz)
        , pivot(0.0f, 0.0f)
        , anchorMin(0.0f, 0.0f)
        , anchorMax(0.0f, 0.0f)
        , worldRect(pos.x, pos.y, sz.x, sz.y)
        , worldPosition(pos)
        , worldSize(sz)
    {}
    
    // Helper methods
    void SetAnchor(AnchorType anchor);
    void AddChild(Entity child);
    void RemoveChild(Entity child);
    bool IsChildOf(Entity potentialParent) const;
};

// UI Renderer Component - Handles visual rendering properties
struct UIRenderer {
    // Visual properties
    Color color = Color::White();
    Color backgroundColor = Color::Transparent();
    
    // Texture/Material
    std::string texturePath;
    std::string materialName;
    
    // Rendering properties
    bool visible = true;
    int sortingOrder = 0;      // Higher values render on top
    float alpha = 1.0f;        // Overall alpha multiplier
    
    // Border properties
    bool hasBorder = false;
    Color borderColor = Color::Black();
    float borderWidth = 1.0f;
    
    // Corner rounding
    float cornerRadius = 0.0f;
    
    UIRenderer() = default;
    UIRenderer(const Color& col) : color(col) {}
};

// UI Interactable Component - Handles input and interaction
struct UIInteractable {
    // Interaction state
    UIState currentState = UIState::Normal;
    UIState previousState = UIState::Normal;
    
    // Interaction flags
    bool interactable = true;
    bool blockRaycasts = true;  // Whether this element blocks mouse events
    bool captureEvents = false; // Whether to capture events exclusively
    
    // Event callbacks
    std::function<void(Entity)> onHover;
    std::function<void(Entity)> onUnhover;
    std::function<void(Entity)> onPress;
    std::function<void(Entity)> onRelease;
    std::function<void(Entity)> onClick;
    std::function<void(Entity)> onDoubleClick;
    
    // Timing for double-click detection
    float lastClickTime = 0.0f;
    static constexpr float DOUBLE_CLICK_TIME = 0.3f;
    
    UIInteractable() = default;
    
    // Helper methods
    void SetState(UIState newState);
    bool IsHovered() const { return currentState == UIState::Hovered; }
    bool IsPressed() const { return currentState == UIState::Pressed; }
    bool IsDisabled() const { return currentState == UIState::Disabled; }
    bool StateChanged() const { return currentState != previousState; }
};

// UI Layout Component - Handles automatic layout and constraints
struct UILayout {
    // Layout type
    enum class LayoutType {
        None,           // Manual positioning
        Horizontal,     // Horizontal layout group
        Vertical,       // Vertical layout group
        Grid,          // Grid layout
        Flexible       // Flexible/responsive layout
    };
    
    LayoutType layoutType = LayoutType::None;
    
    // Layout properties
    Vector2 spacing = Vector2(5.0f, 5.0f);  // Spacing between elements
    Vector2 padding = Vector2(10.0f, 10.0f); // Padding around content
    
    // Grid layout properties
    int gridColumns = 1;
    int gridRows = 1;
    
    // Flexible layout properties
    float flexGrow = 0.0f;     // How much this element should grow
    float flexShrink = 1.0f;   // How much this element should shrink
    Vector2 minSize = Vector2(0.0f, 0.0f);
    Vector2 maxSize = Vector2(10000.0f, 10000.0f);
    
    // Layout flags
    bool autoSize = false;     // Automatically size to fit content
    bool wrapContent = false;  // Wrap content to new lines/columns
    
    UILayout() = default;
    UILayout(LayoutType type) : layoutType(type) {}
};

} // namespace GUI
} // namespace Engine
