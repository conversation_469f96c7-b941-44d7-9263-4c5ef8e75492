//
// UIDemo.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of GUI demonstration and examples
//

#include "UIDemo.h"
#include <iostream>

namespace Engine {
namespace GUI {

// ============================================================================
// UIDemo Implementation
// ============================================================================

UIDemo::UIDemo() {
    // Initialize demo data with default values
    m_demoData = DemoData();
}

UIDemo::~UIDemo() {
    if (m_initialized) {
        Shutdown();
    }
}

bool UIDemo::Initialize(UIManager* uiManager) {
    if (m_initialized) {
        std::cout << "⚠️  UIDemo already initialized" << std::endl;
        return true;
    }
    
    if (!uiManager) {
        std::cerr << "❌ Cannot initialize UIDemo with null UIManager" << std::endl;
        return false;
    }
    
    m_uiManager = uiManager;
    
    // Create all demo layouts
    CreateMainMenuDemo();
    CreateGameHUDDemo();
    CreateSettingsMenuDemo();
    CreateInventoryDemo();
    CreateDialogDemo();
    CreateLayoutDemo();
    CreateWidgetShowcaseDemo();
    CreateInteractionDemo();
    
    // Hide all demos initially
    HideAllDemos();
    
    m_initialized = true;
    std::cout << "✅ UIDemo initialized with " << m_demoEntities.size() << " demos" << std::endl;
    
    return true;
}

void UIDemo::Update(DeltaTime deltaTime) {
    if (!m_initialized) return;
    
    // Update demo-specific logic here
    // For example, animate progress bars, update counters, etc.
}

void UIDemo::Shutdown() {
    if (!m_initialized) return;
    
    // Clear all demo entities
    m_demoEntities.clear();
    m_demoVisibility.clear();
    m_currentDemo.clear();
    
    m_uiManager = nullptr;
    m_initialized = false;
    
    std::cout << "🔧 UIDemo shutdown complete" << std::endl;
}

void UIDemo::ShowDemo(const std::string& demoName) {
    if (!m_initialized) return;
    
    // Hide current demo
    if (!m_currentDemo.empty()) {
        HideDemo(m_currentDemo);
    }
    
    // Show new demo
    SetDemoVisibility(demoName, true);
    m_currentDemo = demoName;
    
    if (m_onDemoChanged) {
        m_onDemoChanged(demoName);
    }
    
    std::cout << "🎯 Showing demo: " << demoName << std::endl;
}

void UIDemo::HideDemo(const std::string& demoName) {
    SetDemoVisibility(demoName, false);
    
    if (m_currentDemo == demoName) {
        m_currentDemo.clear();
    }
}

void UIDemo::HideAllDemos() {
    for (auto& pair : m_demoVisibility) {
        SetDemoVisibility(pair.first, false);
    }
    m_currentDemo.clear();
}

void UIDemo::CreateMainMenuDemo() {
    const std::string demoName = "MainMenu";
    CreateDemoCanvas(demoName);
    
    // Create main menu background
    Entity background = CreateStyledPanel(Vector2(0, 0), Vector2(1920, 1080), Color(0.1f, 0.1f, 0.2f, 1.0f));
    AddEntityToDemo(demoName, background);
    
    // Game title
    Entity title = m_uiManager->CreateLabel("3D Looter-Shooter", Vector2(960, 200), 48.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, title);
    
    // Menu buttons
    float buttonWidth = 300.0f;
    float buttonHeight = 60.0f;
    float buttonSpacing = 80.0f;
    float startY = 400.0f;
    
    std::vector<std::pair<std::string, std::string>> buttons = {
        {"New Game", "NewGame"},
        {"Load Game", "LoadGame"},
        {"Settings", "Settings"},
        {"Credits", "Credits"},
        {"Exit", "Exit"}
    };
    
    for (size_t i = 0; i < buttons.size(); ++i) {
        Vector2 buttonPos(960 - buttonWidth/2, startY + i * buttonSpacing);
        Entity button = CreateStyledButton(buttons[i].first, buttonPos, Vector2(buttonWidth, buttonHeight),
                                         [this, buttonName = buttons[i].second]() {
                                             OnMainMenuButtonClicked(buttonName);
                                         });
        AddEntityToDemo(demoName, button);
    }
    
    // Version info
    Entity version = m_uiManager->CreateLabel("Version 1.0.0", Vector2(50, 1030), 14.0f, Color(0.7f, 0.7f, 0.7f, 1.0f), demoName);
    AddEntityToDemo(demoName, version);
}

void UIDemo::CreateGameHUDDemo() {
    const std::string demoName = "GameHUD";
    CreateDemoCanvas(demoName);
    
    // Health bar
    Entity healthBar = CreateProgressBarWithLabel("Health", m_demoData.healthValue / 100.0f, 
                                                 Vector2(50, 50), Vector2(200, 30), Color::Red());
    AddEntityToDemo(demoName, healthBar);
    
    // Mana bar
    Entity manaBar = CreateProgressBarWithLabel("Mana", m_demoData.manaValue / 100.0f,
                                               Vector2(50, 90), Vector2(200, 30), Color::Blue());
    AddEntityToDemo(demoName, manaBar);
    
    // Experience bar
    Entity expBar = CreateProgressBarWithLabel("Experience", m_demoData.experienceValue / 100.0f,
                                              Vector2(50, 130), Vector2(200, 20), Color::Yellow());
    AddEntityToDemo(demoName, expBar);
    
    // Ammo counter
    Entity ammoLabel = m_uiManager->CreateLabel("Ammo:", Vector2(1600, 50), 16.0f, Color::White(), demoName);
    Entity ammoCount = m_uiManager->CreateLabel(std::to_string(m_demoData.ammoCount), Vector2(1700, 50), 20.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, ammoLabel);
    AddEntityToDemo(demoName, ammoCount);
    
    // Weapon name
    Entity weaponLabel = m_uiManager->CreateLabel(m_demoData.weaponName, Vector2(1600, 80), 18.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, weaponLabel);
    
    // Minimap placeholder
    Entity minimap = CreateStyledPanel(Vector2(1650, 150), Vector2(200, 200), Color(0.2f, 0.2f, 0.2f, 0.8f));
    Entity minimapLabel = m_uiManager->CreateLabel("Minimap", Vector2(1720, 240), 16.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, minimap);
    AddEntityToDemo(demoName, minimapLabel);
    
    // Crosshair
    Entity crosshair = CreateStyledPanel(Vector2(958, 538), Vector2(4, 4), Color::White());
    AddEntityToDemo(demoName, crosshair);
}

void UIDemo::CreateSettingsMenuDemo() {
    const std::string demoName = "Settings";
    CreateDemoCanvas(demoName);
    
    // Settings background
    Entity background = CreateStyledPanel(Vector2(400, 200), Vector2(1120, 680), Color(0.15f, 0.15f, 0.15f, 0.95f));
    AddEntityToDemo(demoName, background);
    
    // Title
    Entity title = m_uiManager->CreateLabel("Settings", Vector2(960, 250), 32.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, title);
    
    // Audio settings
    Entity audioLabel = m_uiManager->CreateLabel("Audio", Vector2(500, 320), 24.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, audioLabel);
    
    Entity masterVolumeSlider = CreateLabeledSlider("Master Volume", m_demoData.masterVolume, 0.0f, 1.0f,
                                                   Vector2(500, 360), Vector2(300, 40),
                                                   [this](float value) { m_demoData.masterVolume = value; });
    AddEntityToDemo(demoName, masterVolumeSlider);
    
    Entity musicVolumeSlider = CreateLabeledSlider("Music Volume", m_demoData.musicVolume, 0.0f, 1.0f,
                                                  Vector2(500, 410), Vector2(300, 40),
                                                  [this](float value) { m_demoData.musicVolume = value; });
    AddEntityToDemo(demoName, musicVolumeSlider);
    
    Entity sfxVolumeSlider = CreateLabeledSlider("SFX Volume", m_demoData.sfxVolume, 0.0f, 1.0f,
                                                Vector2(500, 460), Vector2(300, 40),
                                                [this](float value) { m_demoData.sfxVolume = value; });
    AddEntityToDemo(demoName, sfxVolumeSlider);
    
    // Graphics settings
    Entity graphicsLabel = m_uiManager->CreateLabel("Graphics", Vector2(900, 320), 24.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, graphicsLabel);
    
    // Fullscreen toggle
    Entity fullscreenButton = CreateStyledButton(m_demoData.fullscreen ? "Fullscreen: ON" : "Fullscreen: OFF",
                                                Vector2(900, 360), Vector2(200, 40),
                                                [this]() {
                                                    m_demoData.fullscreen = !m_demoData.fullscreen;
                                                    OnSettingsChanged();
                                                });
    AddEntityToDemo(demoName, fullscreenButton);
    
    // VSync toggle
    Entity vsyncButton = CreateStyledButton(m_demoData.vsync ? "VSync: ON" : "VSync: OFF",
                                           Vector2(900, 410), Vector2(200, 40),
                                           [this]() {
                                               m_demoData.vsync = !m_demoData.vsync;
                                               OnSettingsChanged();
                                           });
    AddEntityToDemo(demoName, vsyncButton);
    
    // Control buttons
    Entity applyButton = CreateStyledButton("Apply", Vector2(700, 800), Vector2(120, 50),
                                           [this]() { OnSettingsChanged(); }, Color::Green());
    Entity cancelButton = CreateStyledButton("Cancel", Vector2(850, 800), Vector2(120, 50),
                                            [this]() { HideDemo("Settings"); }, Color::Red());
    Entity okButton = CreateStyledButton("OK", Vector2(1000, 800), Vector2(120, 50),
                                        [this]() { OnSettingsChanged(); HideDemo("Settings"); });
    
    AddEntityToDemo(demoName, applyButton);
    AddEntityToDemo(demoName, cancelButton);
    AddEntityToDemo(demoName, okButton);
}

void UIDemo::CreateInventoryDemo() {
    const std::string demoName = "Inventory";
    CreateDemoCanvas(demoName);
    
    // Inventory background
    Entity background = CreateStyledPanel(Vector2(300, 150), Vector2(1320, 780), Color(0.1f, 0.1f, 0.1f, 0.95f));
    AddEntityToDemo(demoName, background);
    
    // Title
    Entity title = m_uiManager->CreateLabel("Inventory", Vector2(960, 200), 28.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, title);
    
    // Player info
    Entity playerName = m_uiManager->CreateLabel("Player: " + m_demoData.playerName, Vector2(350, 250), 18.0f, Color::White(), demoName);
    Entity playerLevel = m_uiManager->CreateLabel("Level: " + std::to_string(m_demoData.level), Vector2(350, 280), 18.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, playerName);
    AddEntityToDemo(demoName, playerLevel);
    
    // Inventory grid
    int columns = 8;
    int rows = 6;
    float slotSize = 80.0f;
    float slotSpacing = 10.0f;
    Vector2 gridStart(400, 350);
    
    for (int row = 0; row < rows; ++row) {
        for (int col = 0; col < columns; ++col) {
            Vector2 slotPos(gridStart.x + col * (slotSize + slotSpacing),
                           gridStart.y + row * (slotSize + slotSpacing));
            
            Entity slot = CreateStyledPanel(slotPos, Vector2(slotSize, slotSize), Color(0.3f, 0.3f, 0.3f, 0.8f));
            AddEntityToDemo(demoName, slot);
            
            // Add item if available
            int itemIndex = row * columns + col;
            if (itemIndex < static_cast<int>(m_demoData.inventoryItems.size())) {
                Entity itemLabel = m_uiManager->CreateLabel(m_demoData.inventoryItems[itemIndex].substr(0, 8),
                                                           Vector2(slotPos.x + 5, slotPos.y + 30), 10.0f, Color::White(), demoName);
                AddEntityToDemo(demoName, itemLabel);
            }
        }
    }
    
    // Close button
    Entity closeButton = CreateStyledButton("Close", Vector2(1450, 200), Vector2(100, 40),
                                           [this]() { HideDemo("Inventory"); }, Color::Red());
    AddEntityToDemo(demoName, closeButton);
}

// ============================================================================
// Helper Methods
// ============================================================================

void UIDemo::AddEntityToDemo(const std::string& demoName, Entity entity) {
    m_demoEntities[demoName].push_back(entity);
}

void UIDemo::SetDemoVisibility(const std::string& demoName, bool visible) {
    m_demoVisibility[demoName] = visible;
    
    // Set visibility of all entities in the demo
    auto it = m_demoEntities.find(demoName);
    if (it != m_demoEntities.end() && m_uiManager && m_uiManager->GetRenderSystem()) {
        for (Entity entity : it->second) {
            // Set entity visibility through renderer component
            // This would need to be implemented in the actual ECS
        }
    }
}

void UIDemo::CreateDemoCanvas(const std::string& demoName) {
    m_uiManager->CreateCanvas(demoName);
    m_demoVisibility[demoName] = false;
}

Entity UIDemo::CreateStyledButton(const std::string& text, const Vector2& position, const Vector2& size,
                                 std::function<void()> onClick, const Color& color) {
    return m_uiManager->CreateButton(text, position, size, onClick);
}

Entity UIDemo::CreateStyledPanel(const Vector2& position, const Vector2& size, const Color& color) {
    return m_uiManager->CreatePanel(position, size, color);
}

Entity UIDemo::CreateProgressBarWithLabel(const std::string& label, float value, const Vector2& position,
                                         const Vector2& size, const Color& fillColor) {
    // Create label
    Entity labelEntity = m_uiManager->CreateLabel(label, Vector2(position.x, position.y - 20), 14.0f, Color::White());
    
    // Create progress bar (simplified - would use actual progress bar component)
    Entity background = CreateStyledPanel(position, size, Color(0.3f, 0.3f, 0.3f, 1.0f));
    Entity fill = CreateStyledPanel(position, Vector2(size.x * value, size.y), fillColor);
    
    return background; // Return main entity
}

Entity UIDemo::CreateLabeledSlider(const std::string& label, float value, float min, float max,
                                  const Vector2& position, const Vector2& size,
                                  std::function<void(float)> onValueChanged) {
    // Create label
    Entity labelEntity = m_uiManager->CreateLabel(label, Vector2(position.x, position.y - 25), 16.0f, Color::White());
    
    // Create slider
    Entity slider = m_uiManager->CreateSlider(position, size, min, max, value);
    
    return slider;
}

// ============================================================================
// Event Handlers
// ============================================================================

void UIDemo::OnMainMenuButtonClicked(const std::string& buttonName) {
    std::cout << "🎮 Main menu button clicked: " << buttonName << std::endl;
    
    if (buttonName == "NewGame") {
        ShowDemo("GameHUD");
    } else if (buttonName == "Settings") {
        ShowDemo("Settings");
    } else if (buttonName == "Exit") {
        std::cout << "🚪 Exit game requested" << std::endl;
    }
}

void UIDemo::OnSettingsChanged() {
    std::cout << "⚙️  Settings changed - applying new configuration" << std::endl;
}

void UIDemo::OnInventoryItemClicked(const std::string& itemName) {
    std::cout << "📦 Inventory item clicked: " << itemName << std::endl;
}

void UIDemo::OnDialogButtonClicked(const std::string& response) {
    std::cout << "💬 Dialog response: " << response << std::endl;
}

bool UIDemo::IsDemoVisible(const std::string& demoName) const {
    auto it = m_demoVisibility.find(demoName);
    return it != m_demoVisibility.end() && it->second;
}

std::vector<std::string> UIDemo::GetAvailableDemos() const {
    std::vector<std::string> demos;
    for (const auto& pair : m_demoEntities) {
        demos.push_back(pair.first);
    }
    return demos;
}

void UIDemo::SetOnDemoChanged(std::function<void(const std::string&)> callback) {
    m_onDemoChanged = callback;
}

void UIDemo::CreateDialogDemo() {
    const std::string demoName = "Dialog";
    CreateDemoCanvas(demoName);

    // Dialog background overlay
    Entity overlay = CreateStyledPanel(Vector2(0, 0), Vector2(1920, 1080), Color(0.0f, 0.0f, 0.0f, 0.5f));
    AddEntityToDemo(demoName, overlay);

    // Dialog box
    Entity dialogBox = CreateStyledPanel(Vector2(400, 300), Vector2(1120, 480), Color(0.2f, 0.2f, 0.2f, 0.95f));
    AddEntityToDemo(demoName, dialogBox);

    // Speaker name
    Entity speakerName = m_uiManager->CreateLabel("Village Elder", Vector2(450, 350), 20.0f, Color::Yellow(), demoName);
    AddEntityToDemo(demoName, speakerName);

    // Dialog text
    std::string dialogText = "Welcome, brave adventurer! The ancient ruins hold many secrets,\nbut beware of the dangers that lurk within. Will you accept\nthis quest to retrieve the lost artifact?";
    Entity dialogMessage = m_uiManager->CreateLabel(dialogText, Vector2(450, 400), 16.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, dialogMessage);

    // Response options
    std::vector<std::pair<std::string, std::string>> responses = {
        {"Yes, I accept the quest.", "Accept"},
        {"Tell me more about the ruins.", "MoreInfo"},
        {"Not right now.", "Decline"}
    };

    for (size_t i = 0; i < responses.size(); ++i) {
        Vector2 buttonPos(450, 550 + i * 60);
        Entity responseButton = CreateStyledButton(responses[i].first, buttonPos, Vector2(600, 50),
                                                  [this, response = responses[i].second]() {
                                                      OnDialogButtonClicked(response);
                                                      HideDemo("Dialog");
                                                  });
        AddEntityToDemo(demoName, responseButton);
    }
}

void UIDemo::CreateLayoutDemo() {
    const std::string demoName = "Layout";
    CreateDemoCanvas(demoName);

    // Title
    Entity title = m_uiManager->CreateLabel("Layout System Demo", Vector2(960, 50), 28.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, title);

    // Horizontal layout example
    Entity horizontalLayout = m_uiManager->CreateHorizontalLayout(Vector2(100, 150), Vector2(800, 100), 10.0f, demoName);
    AddEntityToDemo(demoName, horizontalLayout);

    // Add buttons to horizontal layout
    for (int i = 0; i < 4; ++i) {
        Entity button = CreateStyledButton("Button " + std::to_string(i + 1), Vector2(0, 0), Vector2(180, 80), nullptr);
        AddEntityToDemo(demoName, button);
    }

    // Vertical layout example
    Entity verticalLayout = m_uiManager->CreateVerticalLayout(Vector2(1000, 150), Vector2(200, 400), 10.0f, demoName);
    AddEntityToDemo(demoName, verticalLayout);

    // Add items to vertical layout
    for (int i = 0; i < 5; ++i) {
        Entity item = CreateStyledPanel(Vector2(0, 0), Vector2(180, 70), Color(0.3f + i * 0.1f, 0.3f, 0.6f, 1.0f));
        AddEntityToDemo(demoName, item);
    }

    // Grid layout example
    Entity gridLayout = m_uiManager->CreateGridLayout(Vector2(100, 600), Vector2(600, 400), 3, 2, Vector2(10, 10), demoName);
    AddEntityToDemo(demoName, gridLayout);

    // Add items to grid layout
    for (int i = 0; i < 6; ++i) {
        Entity gridItem = CreateStyledPanel(Vector2(0, 0), Vector2(190, 190), Color(0.6f, 0.3f + i * 0.1f, 0.3f, 1.0f));
        AddEntityToDemo(demoName, gridItem);
    }
}

void UIDemo::CreateWidgetShowcaseDemo() {
    const std::string demoName = "Widgets";
    CreateDemoCanvas(demoName);

    // Title
    Entity title = m_uiManager->CreateLabel("Widget Showcase", Vector2(960, 50), 28.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, title);

    // Buttons section
    Entity buttonLabel = m_uiManager->CreateLabel("Buttons:", Vector2(100, 120), 20.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, buttonLabel);

    Entity normalButton = CreateStyledButton("Normal Button", Vector2(100, 160), Vector2(150, 40), nullptr);
    Entity primaryButton = CreateStyledButton("Primary", Vector2(270, 160), Vector2(150, 40), nullptr, Color(0.2f, 0.6f, 1.0f, 1.0f));
    Entity dangerButton = CreateStyledButton("Danger", Vector2(440, 160), Vector2(150, 40), nullptr, Color(1.0f, 0.3f, 0.3f, 1.0f));

    AddEntityToDemo(demoName, buttonLabel);
    AddEntityToDemo(demoName, normalButton);
    AddEntityToDemo(demoName, primaryButton);
    AddEntityToDemo(demoName, dangerButton);

    // Text fields section
    Entity textFieldLabel = m_uiManager->CreateLabel("Text Fields:", Vector2(100, 240), 20.0f, Color::White(), demoName);
    Entity textField1 = m_uiManager->CreateTextField(Vector2(100, 280), Vector2(200, 40), "Enter your name...", demoName);
    Entity textField2 = m_uiManager->CreateTextField(Vector2(320, 280), Vector2(200, 40), "Email address...", demoName);

    AddEntityToDemo(demoName, textFieldLabel);
    AddEntityToDemo(demoName, textField1);
    AddEntityToDemo(demoName, textField2);

    // Sliders section
    Entity sliderLabel = m_uiManager->CreateLabel("Sliders:", Vector2(100, 360), 20.0f, Color::White(), demoName);
    Entity slider1 = m_uiManager->CreateSlider(Vector2(100, 400), Vector2(200, 30), 0.0f, 100.0f, 50.0f, demoName);
    Entity slider2 = m_uiManager->CreateSlider(Vector2(320, 400), Vector2(200, 30), 0.0f, 1.0f, 0.75f, demoName);

    AddEntityToDemo(demoName, sliderLabel);
    AddEntityToDemo(demoName, slider1);
    AddEntityToDemo(demoName, slider2);

    // Progress bars section
    Entity progressLabel = m_uiManager->CreateLabel("Progress Bars:", Vector2(100, 480), 20.0f, Color::White(), demoName);
    Entity progress1 = CreateProgressBarWithLabel("Loading", 0.65f, Vector2(100, 520), Vector2(200, 20), Color::Green());
    Entity progress2 = CreateProgressBarWithLabel("Health", 0.35f, Vector2(320, 520), Vector2(200, 20), Color::Red());

    AddEntityToDemo(demoName, progressLabel);
    AddEntityToDemo(demoName, progress1);
    AddEntityToDemo(demoName, progress2);
}

void UIDemo::CreateInteractionDemo() {
    const std::string demoName = "Interaction";
    CreateDemoCanvas(demoName);

    // Title
    Entity title = m_uiManager->CreateLabel("Interaction Demo", Vector2(960, 50), 28.0f, Color::White(), demoName);
    AddEntityToDemo(demoName, title);

    // Interactive elements
    Entity hoverButton = CreateStyledButton("Hover Me", Vector2(100, 150), Vector2(150, 50),
                                           []() { std::cout << "Button clicked!" << std::endl; });

    Entity dragPanel = CreateStyledPanel(Vector2(300, 150), Vector2(100, 100), Color(0.5f, 0.5f, 1.0f, 1.0f));
    Entity dragLabel = m_uiManager->CreateLabel("Drag Me", Vector2(320, 190), 14.0f, Color::White(), demoName);

    Entity clickCounter = m_uiManager->CreateLabel("Clicks: 0", Vector2(500, 180), 16.0f, Color::White(), demoName);

    AddEntityToDemo(demoName, title);
    AddEntityToDemo(demoName, hoverButton);
    AddEntityToDemo(demoName, dragPanel);
    AddEntityToDemo(demoName, dragLabel);
    AddEntityToDemo(demoName, clickCounter);

    // Focus demonstration
    Entity focusLabel = m_uiManager->CreateLabel("Tab Navigation:", Vector2(100, 300), 20.0f, Color::White(), demoName);

    for (int i = 0; i < 3; ++i) {
        Entity focusField = m_uiManager->CreateTextField(Vector2(100 + i * 220, 340), Vector2(200, 40),
                                                        "Field " + std::to_string(i + 1), demoName);
        AddEntityToDemo(demoName, focusField);
    }

    AddEntityToDemo(demoName, focusLabel);
}

} // namespace GUI
} // namespace Engine
