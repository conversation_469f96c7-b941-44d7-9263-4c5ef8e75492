//
// UIDemo.h
// macOS 3D Looter-Shooter Game Engine
//
// GUI demonstration and example layouts
//

#pragma once

#include "UIManager.h"
#include "engine/ECS.h"
#include <vector>
#include <functional>

namespace Engine {
namespace GUI {

// ============================================================================
// GUI Demo Class
// ============================================================================

class UIDemo {
public:
    UIDemo();
    ~UIDemo();
    
    // Demo lifecycle
    bool Initialize(UIManager* uiManager);
    void Update(DeltaTime deltaTime);
    void Shutdown();
    
    // Demo management
    void ShowDemo(const std::string& demoName);
    void HideDemo(const std::string& demoName);
    void HideAllDemos();
    
    // Available demos
    void CreateMainMenuDemo();
    void CreateGameHUDDemo();
    void CreateSettingsMenuDemo();
    void CreateInventoryDemo();
    void CreateDialogDemo();
    void CreateLayoutDemo();
    void CreateWidgetShowcaseDemo();
    void CreateInteractionDemo();
    
    // Demo state
    bool IsDemoVisible(const std::string& demoName) const;
    std::vector<std::string> GetAvailableDemos() const;
    
    // Event handlers
    void SetOnDemoChanged(std::function<void(const std::string&)> callback);

private:
    UIManager* m_uiManager = nullptr;
    
    // Demo entities and state
    std::unordered_map<std::string, std::vector<Entity>> m_demoEntities;
    std::unordered_map<std::string, bool> m_demoVisibility;
    std::string m_currentDemo;
    
    // Demo data
    struct DemoData {
        float healthValue = 100.0f;
        float manaValue = 75.0f;
        float experienceValue = 45.0f;
        int ammoCount = 30;
        int grenadeCount = 3;
        std::string weaponName = "Assault Rifle";
        std::string playerName = "Player";
        int level = 15;
        
        // Settings
        float masterVolume = 0.8f;
        float musicVolume = 0.6f;
        float sfxVolume = 0.9f;
        bool fullscreen = false;
        bool vsync = true;
        int resolutionIndex = 1;
        int qualityIndex = 2;
        
        // Inventory
        std::vector<std::string> inventoryItems = {
            "Health Potion", "Mana Potion", "Iron Sword", "Steel Shield",
            "Magic Ring", "Leather Armor", "Gold Coins", "Ancient Key"
        };
    };
    
    DemoData m_demoData;
    
    // Callback
    std::function<void(const std::string&)> m_onDemoChanged;
    
    // Helper methods
    void AddEntityToDemo(const std::string& demoName, Entity entity);
    void SetDemoVisibility(const std::string& demoName, bool visible);
    void CreateDemoCanvas(const std::string& demoName);
    
    // Demo creation helpers
    Entity CreateStyledButton(const std::string& text, const Vector2& position, const Vector2& size,
                             std::function<void()> onClick, const Color& color = Color(0.3f, 0.6f, 1.0f, 1.0f));
    Entity CreateStyledPanel(const Vector2& position, const Vector2& size, 
                            const Color& color = Color(0.2f, 0.2f, 0.2f, 0.9f));
    Entity CreateProgressBarWithLabel(const std::string& label, float value, const Vector2& position, 
                                     const Vector2& size, const Color& fillColor = Color::Green());
    Entity CreateLabeledSlider(const std::string& label, float value, float min, float max,
                              const Vector2& position, const Vector2& size,
                              std::function<void(float)> onValueChanged = nullptr);
    
    // Event handlers for demos
    void OnMainMenuButtonClicked(const std::string& buttonName);
    void OnSettingsChanged();
    void OnInventoryItemClicked(const std::string& itemName);
    void OnDialogButtonClicked(const std::string& response);
    
    bool m_initialized = false;
};

// ============================================================================
// Demo Utility Functions
// ============================================================================

// Create a complete game menu system
class GameMenuSystem {
public:
    static void CreateMainMenu(UIManager* uiManager);
    static void CreatePauseMenu(UIManager* uiManager);
    static void CreateSettingsMenu(UIManager* uiManager);
    static void CreateInventoryMenu(UIManager* uiManager);
    static void CreateCharacterSheet(UIManager* uiManager);
    static void CreateShopInterface(UIManager* uiManager);
    
    // Menu navigation
    static void ShowMenu(const std::string& menuName);
    static void HideMenu(const std::string& menuName);
    static void HideAllMenus();
    
private:
    static std::unordered_map<std::string, std::vector<Entity>> s_menuEntities;
};

// Create HUD elements for gameplay
class GameHUD {
public:
    static void CreatePlayerHUD(UIManager* uiManager);
    static void CreateMinimap(UIManager* uiManager);
    static void CreateChatWindow(UIManager* uiManager);
    static void CreateNotificationSystem(UIManager* uiManager);
    static void CreateCrosshair(UIManager* uiManager);
    
    // HUD updates
    static void UpdateHealth(float health, float maxHealth);
    static void UpdateMana(float mana, float maxMana);
    static void UpdateAmmo(int current, int max);
    static void UpdateExperience(float experience, float maxExperience);
    static void ShowNotification(const std::string& message, float duration = 3.0f);
    
private:
    static Entity s_healthBar;
    static Entity s_manaBar;
    static Entity s_ammoText;
    static Entity s_experienceBar;
    static std::vector<Entity> s_notifications;
};

// Dialog and conversation system
class DialogSystem {
public:
    struct DialogOption {
        std::string text;
        std::function<void()> callback;
        bool enabled = true;
    };
    
    struct DialogData {
        std::string speakerName;
        std::string message;
        std::vector<DialogOption> options;
        std::string portraitPath;
    };
    
    static void ShowDialog(const DialogData& dialog, UIManager* uiManager);
    static void HideDialog();
    static bool IsDialogVisible();
    
private:
    static std::vector<Entity> s_dialogEntities;
    static bool s_dialogVisible;
};

// Tooltip system
class TooltipSystem {
public:
    static void ShowTooltip(const std::string& text, const Vector2& position, UIManager* uiManager);
    static void HideTooltip();
    static void UpdateTooltipPosition(const Vector2& position);
    
private:
    static Entity s_tooltipEntity;
    static bool s_tooltipVisible;
};

} // namespace GUI
} // namespace Engine
