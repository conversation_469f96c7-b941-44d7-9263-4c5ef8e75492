//
// UILayoutSystem.h
// macOS 3D Looter-Shooter Game Engine
//
// Layout system for automatic positioning and sizing of GUI elements
//

#pragma once

#include "engine/System.h"
#include "UIComponents.h"
#include <vector>
#include <unordered_map>

namespace Engine {
namespace GUI {

// ============================================================================
// Layout Constraint System
// ============================================================================

enum class ConstraintType {
    Fixed,              // Fixed position/size
    Relative,           // Relative to parent
    AspectRatio,        // Maintain aspect ratio
    ContentSize,        // Size to fit content
    Fill,               // Fill available space
    Center              // Center in available space
};

struct LayoutConstraint {
    ConstraintType type = ConstraintType::Fixed;
    Vector2 value = Vector2(0.0f, 0.0f);    // Value depends on constraint type
    Entity targetEntity = INVALID_ENTITY;   // For relative constraints
    float priority = 1.0f;                  // Constraint priority (higher = more important)
    
    LayoutConstraint() = default;
    LayoutConstraint(ConstraintType t, const Vector2& v) : type(t), value(v) {}
    LayoutConstraint(ConstraintType t, float x, float y) : type(t), value(x, y) {}
};

// ============================================================================
// Layout Groups
// ============================================================================

struct LayoutGroup {
    UILayout::LayoutType layoutType = UILayout::LayoutType::None;
    std::vector<Entity> children;
    
    // Layout properties
    Vector2 spacing = Vector2(5.0f, 5.0f);
    Vector2 padding = Vector2(10.0f, 10.0f);
    
    // Alignment
    enum class Alignment {
        Start,      // Top/Left
        Center,     // Center
        End,        // Bottom/Right
        Stretch     // Fill available space
    };
    
    Alignment horizontalAlignment = Alignment::Start;
    Alignment verticalAlignment = Alignment::Start;
    
    // Grid-specific properties
    int gridColumns = 1;
    int gridRows = 1;
    bool autoGridSize = true;   // Automatically determine grid size
    
    // Flexible layout properties
    bool wrapContent = false;   // Wrap to new lines/columns
    Vector2 minSize = Vector2(0.0f, 0.0f);
    Vector2 maxSize = Vector2(10000.0f, 10000.0f);
    
    LayoutGroup() = default;
    LayoutGroup(UILayout::LayoutType type) : layoutType(type) {}
};

// ============================================================================
// Layout System
// ============================================================================

class UILayoutSystem : public System {
public:
    UILayoutSystem();
    ~UILayoutSystem();
    
    // System interface
    void Initialize() override;
    void Update(DeltaTime deltaTime) override;
    void Shutdown() override;
    const char* GetName() const override { return "UILayoutSystem"; }
    
    // Layout management
    void RegisterLayoutGroup(Entity entity, const LayoutGroup& group);
    void UnregisterLayoutGroup(Entity entity);
    void UpdateLayout(Entity entity);
    void UpdateAllLayouts();
    
    // Constraint management
    void AddConstraint(Entity entity, const LayoutConstraint& constraint);
    void RemoveConstraint(Entity entity, ConstraintType type);
    void ClearConstraints(Entity entity);
    
    // Layout calculation
    void CalculateLayout(Entity entity);
    void CalculateHorizontalLayout(Entity entity, const LayoutGroup& group);
    void CalculateVerticalLayout(Entity entity, const LayoutGroup& group);
    void CalculateGridLayout(Entity entity, const LayoutGroup& group);
    void CalculateFlexibleLayout(Entity entity, const LayoutGroup& group);
    
    // Utility methods
    Vector2 CalculateContentSize(Entity entity);
    Vector2 CalculatePreferredSize(Entity entity);
    Rect CalculateWorldRect(Entity entity);
    void ApplyConstraints(Entity entity);
    
    // Canvas and screen management
    void SetCanvasSize(const Vector2& size);
    Vector2 GetCanvasSize() const { return m_canvasSize; }
    void SetScreenSize(const Vector2& size);
    Vector2 GetScreenSize() const { return m_screenSize; }
    
    // Anchor and pivot calculations
    Vector2 CalculateAnchoredPosition(const UITransform& transform, const Vector2& parentSize);
    Vector2 CalculateAnchoredSize(const UITransform& transform, const Vector2& parentSize);
    
    // Debug and visualization
    void SetDebugMode(bool enabled) { m_debugMode = enabled; }
    bool IsDebugMode() const { return m_debugMode; }
    void DrawLayoutDebugInfo(Entity entity);

private:
    // Layout groups and constraints
    std::unordered_map<Entity, LayoutGroup> m_layoutGroups;
    std::unordered_map<Entity, std::vector<LayoutConstraint>> m_constraints;
    
    // Canvas and screen properties
    Vector2 m_canvasSize = Vector2(1920.0f, 1080.0f);
    Vector2 m_screenSize = Vector2(1920.0f, 1080.0f);
    
    // Layout calculation state
    std::vector<Entity> m_layoutQueue;
    std::unordered_map<Entity, bool> m_layoutDirty;
    
    // Debug mode
    bool m_debugMode = false;
    
    // Internal helper methods
    void MarkLayoutDirty(Entity entity);
    void ProcessLayoutQueue();
    bool IsLayoutDirty(Entity entity) const;
    
    // Layout calculation helpers
    void DistributeSpace(std::vector<Entity>& children, float availableSpace, 
                        bool horizontal, LayoutGroup::Alignment alignment);
    void ApplyAlignment(Entity entity, const Rect& availableRect, 
                       LayoutGroup::Alignment horizontal, LayoutGroup::Alignment vertical);
    
    // Constraint resolution
    void ResolveConstraints(Entity entity);
    bool SolveConstraint(Entity entity, const LayoutConstraint& constraint);
    
    // Hierarchy traversal
    void UpdateChildrenLayout(Entity entity);
    std::vector<Entity> GetLayoutChildren(Entity entity);
    Entity GetLayoutParent(Entity entity);
    
    bool m_initialized = false;
};

// ============================================================================
// Layout Helper Functions
// ============================================================================

// Create common layout configurations
LayoutGroup CreateHorizontalLayoutGroup(float spacing = 5.0f, 
                                       LayoutGroup::Alignment alignment = LayoutGroup::Alignment::Start);

LayoutGroup CreateVerticalLayoutGroup(float spacing = 5.0f, 
                                     LayoutGroup::Alignment alignment = LayoutGroup::Alignment::Start);

LayoutGroup CreateGridLayoutGroup(int columns, int rows, 
                                 const Vector2& spacing = Vector2(5.0f, 5.0f));

LayoutGroup CreateFlexibleLayoutGroup(bool wrapContent = false);

// Create common constraints
LayoutConstraint CreateFixedConstraint(const Vector2& position, const Vector2& size);
LayoutConstraint CreateRelativeConstraint(float xPercent, float yPercent, 
                                        float widthPercent, float heightPercent);
LayoutConstraint CreateAspectRatioConstraint(float aspectRatio);
LayoutConstraint CreateContentSizeConstraint();
LayoutConstraint CreateFillConstraint();
LayoutConstraint CreateCenterConstraint();

// Anchor preset functions
void SetAnchorPreset(UITransform& transform, AnchorType anchor);
void SetAnchorAndPivot(UITransform& transform, const Vector2& anchorMin, 
                      const Vector2& anchorMax, const Vector2& pivot);

} // namespace GUI
} // namespace Engine
