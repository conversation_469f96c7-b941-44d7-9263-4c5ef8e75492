//
// GUI.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of GUI system integration functions
//

#include "GUI.h"
#include <iostream>
#include <memory>

namespace Engine {
namespace GUI {

// ============================================================================
// Static Variables
// ============================================================================

static std::unique_ptr<UIManager> s_uiManager = nullptr;
static std::unique_ptr<UIDemo> s_guiDemo = nullptr;
static bool s_initialized = false;

// ============================================================================
// GUI System Integration
// ============================================================================

bool InitializeGUISystem(ECS* ecs, 
                        Input::InputSystem* inputSystem,
                        id<MTLDevice> metalDevice,
                        id<MTLCommandQueue> commandQueue,
                        MTLRenderPassDescriptor* renderPassDescriptor) {
    if (s_initialized) {
        std::cout << "⚠️  GUI System already initialized" << std::endl;
        return true;
    }
    
    if (!ecs || !inputSystem || !metalDevice || !commandQueue) {
        std::cerr << "❌ Cannot initialize GUI System with null parameters" << std::endl;
        return false;
    }
    
    std::cout << "🎨 Initializing GUI System..." << std::endl;
    
    // Create UI manager
    s_uiManager = std::make_unique<UIManager>();
    
    // Initialize UI manager
    if (!s_uiManager->Initialize(ecs, inputSystem)) {
        std::cerr << "❌ Failed to initialize UIManager" << std::endl;
        s_uiManager.reset();
        return false;
    }
    
    // Set Metal resources
    s_uiManager->SetMetalDevice(metalDevice);
    s_uiManager->SetCommandQueue(commandQueue);
    s_uiManager->SetRenderPassDescriptor(renderPassDescriptor);
    
    // Create GUI demo
    s_guiDemo = std::make_unique<UIDemo>();
    if (!s_guiDemo->Initialize(s_uiManager.get())) {
        std::cerr << "❌ Failed to initialize GUI Demo" << std::endl;
        s_guiDemo.reset();
        // Continue without demo - not critical
    }
    
    s_initialized = true;
    std::cout << "✅ GUI System initialized successfully" << std::endl;
    
    return true;
}

void ShutdownGUISystem() {
    if (!s_initialized) return;
    
    std::cout << "🔧 Shutting down GUI System..." << std::endl;
    
    // Shutdown demo
    if (s_guiDemo) {
        s_guiDemo->Shutdown();
        s_guiDemo.reset();
    }
    
    // Shutdown UI manager
    if (s_uiManager) {
        s_uiManager->Shutdown();
        s_uiManager.reset();
    }
    
    s_initialized = false;
    std::cout << "✅ GUI System shutdown complete" << std::endl;
}

void UpdateGUISystem(DeltaTime deltaTime) {
    if (!s_initialized || !s_uiManager) return;
    
    s_uiManager->Update(deltaTime);
    
    if (s_guiDemo) {
        s_guiDemo->Update(deltaTime);
    }
}

void RenderGUISystem(id<MTLCommandBuffer> commandBuffer) {
    if (!s_initialized || !s_uiManager) return;
    
    s_uiManager->Render(commandBuffer);
}

void SetGUIScreenSize(float width, float height) {
    if (s_uiManager) {
        s_uiManager->SetScreenSize(Vector2(width, height));
    }
}

void SetGUICanvasSize(float width, float height) {
    if (s_uiManager) {
        s_uiManager->SetCanvasSize(Vector2(width, height));
    }
}

UIManager* GetUIManager() {
    return s_uiManager.get();
}

UIDemo* GetGUIDemo() {
    return s_guiDemo.get();
}

// ============================================================================
// Quick Setup Functions
// ============================================================================

void CreateGameMenus() {
    if (!s_guiDemo) return;
    
    s_guiDemo->CreateMainMenuDemo();
    s_guiDemo->CreateSettingsMenuDemo();
    s_guiDemo->CreateInventoryDemo();
    s_guiDemo->CreateDialogDemo();
    
    std::cout << "🎮 Game menus created" << std::endl;
}

void CreateGameHUD() {
    if (!s_guiDemo) return;
    
    s_guiDemo->CreateGameHUDDemo();
    
    std::cout << "📊 Game HUD created" << std::endl;
}

void ShowMainMenu() {
    if (s_guiDemo) {
        s_guiDemo->ShowDemo("MainMenu");
    }
}

void ShowGameHUD() {
    if (s_guiDemo) {
        s_guiDemo->ShowDemo("GameHUD");
    }
}

void ShowSettingsMenu() {
    if (s_guiDemo) {
        s_guiDemo->ShowDemo("Settings");
    }
}

void HideAllGUI() {
    if (s_guiDemo) {
        s_guiDemo->HideAllDemos();
    }
}

// ============================================================================
// Event Handling
// ============================================================================

void RegisterGUIEventHandler(std::function<void(const UIInputEvent&)> handler) {
    if (s_uiManager) {
        s_uiManager->RegisterGlobalEventHandler(handler);
    }
}

void UnregisterGUIEventHandler() {
    if (s_uiManager) {
        s_uiManager->UnregisterGlobalEventHandler();
    }
}

// ============================================================================
// Utility Functions
// ============================================================================

Vector2 ScreenToCanvas(const Vector2& screenPos) {
    if (s_uiManager && s_uiManager->GetActiveCanvas()) {
        return s_uiManager->GetActiveCanvas()->ScreenToCanvas(screenPos);
    }
    return screenPos;
}

Vector2 CanvasToScreen(const Vector2& canvasPos) {
    if (s_uiManager && s_uiManager->GetActiveCanvas()) {
        return s_uiManager->GetActiveCanvas()->CanvasToScreen(canvasPos);
    }
    return canvasPos;
}

bool IsPointInUIElement(const Vector2& point, Entity entity) {
    if (!s_uiManager || !s_uiManager->GetInputSystem()) return false;
    
    return s_uiManager->GetInputSystem()->IsPositionInEntity(point, entity);
}

Entity GetUIElementAtPosition(const Vector2& position) {
    if (!s_uiManager || !s_uiManager->GetInputSystem()) return INVALID_ENTITY;
    
    return s_uiManager->GetInputSystem()->GetEntityAtPosition(position);
}

// ============================================================================
// Debug and Development
// ============================================================================

void SetGUIDebugMode(bool enabled) {
    if (s_uiManager) {
        s_uiManager->SetDebugMode(enabled);
    }
}

bool IsGUIDebugMode() {
    if (s_uiManager) {
        return s_uiManager->IsDebugMode();
    }
    return false;
}

void PrintGUIStats() {
    if (!s_uiManager) return;
    
    const auto& updateStats = s_uiManager->GetStats();
    std::cout << "📊 GUI Update Statistics:" << std::endl;
    std::cout << "   Total Entities: " << updateStats.totalEntities << std::endl;
    std::cout << "   Visible Entities: " << updateStats.visibleEntities << std::endl;
    std::cout << "   Interactable Entities: " << updateStats.interactableEntities << std::endl;
    std::cout << "   Layout Groups: " << updateStats.layoutGroups << std::endl;
    std::cout << "   Update Time: " << (updateStats.updateTime * 1000.0f) << " ms" << std::endl;
    std::cout << "   Render Time: " << (updateStats.renderTime * 1000.0f) << " ms" << std::endl;
    
    if (s_uiManager->GetRenderSystem()) {
        const auto& renderStats = s_uiManager->GetRenderSystem()->GetStats();
        std::cout << "🎨 GUI Render Statistics:" << std::endl;
        std::cout << "   Draw Calls: " << renderStats.drawCalls << std::endl;
        std::cout << "   Triangles: " << renderStats.triangles << std::endl;
        std::cout << "   Vertices: " << renderStats.vertices << std::endl;
        std::cout << "   Batches: " << renderStats.batches << std::endl;
        std::cout << "   Texture Binds: " << renderStats.textureBinds << std::endl;
        std::cout << "   Frame Time: " << (renderStats.frameTime * 1000.0f) << " ms" << std::endl;
    }
}

void ReloadGUIStyles() {
    // Implementation would reload style files and update all UI elements
    std::cout << "🎨 Reloading GUI styles..." << std::endl;
}

// ============================================================================
// Configuration
// ============================================================================

void SetGUIConfig(const UIConfig& config) {
    if (s_uiManager) {
        s_uiManager->SetConfig(config);
    }
}

const UIConfig& GetGUIConfig() {
    static UIConfig defaultConfig;
    if (s_uiManager) {
        return s_uiManager->GetConfig();
    }
    return defaultConfig;
}

bool LoadGUIConfig(const std::string& filename) {
    // Implementation would load configuration from file
    std::cout << "📁 Loading GUI config from: " << filename << std::endl;
    return true;
}

bool SaveGUIConfig(const std::string& filename) {
    // Implementation would save configuration to file
    std::cout << "💾 Saving GUI config to: " << filename << std::endl;
    return true;
}

// ============================================================================
// Theme and Styling
// ============================================================================

bool LoadGUITheme(const std::string& themeName) {
    std::cout << "🎨 Loading GUI theme: " << themeName << std::endl;
    
    // Set up theme colors based on theme name
    if (themeName == "dark") {
        SetGUIColors(
            Color(0.2f, 0.6f, 1.0f, 1.0f),  // Primary
            Color(0.4f, 0.4f, 0.4f, 1.0f),  // Secondary
            Color(1.0f, 0.6f, 0.2f, 1.0f),  // Accent
            Color(0.1f, 0.1f, 0.1f, 1.0f),  // Background
            Color(0.9f, 0.9f, 0.9f, 1.0f)   // Text
        );
    } else if (themeName == "light") {
        SetGUIColors(
            Color(0.2f, 0.6f, 1.0f, 1.0f),  // Primary
            Color(0.8f, 0.8f, 0.8f, 1.0f),  // Secondary
            Color(1.0f, 0.6f, 0.2f, 1.0f),  // Accent
            Color(0.95f, 0.95f, 0.95f, 1.0f), // Background
            Color(0.1f, 0.1f, 0.1f, 1.0f)   // Text
        );
    }
    
    return true;
}

void SetGUIColors(const Color& primary, const Color& secondary, const Color& accent,
                  const Color& background, const Color& text) {
    if (!s_uiManager) return;
    
    // Update widget factory default styles
    UIWidgetFactory::SetDefaultButtonStyle(secondary, primary, accent);
    UIWidgetFactory::SetDefaultTextStyle("Arial", 16.0f, text);
    UIWidgetFactory::SetDefaultPanelStyle(background, text, 1.0f);
    
    std::cout << "🎨 GUI colors updated" << std::endl;
}

void SetGUIFont(const std::string& fontName, float fontSize) {
    if (!s_uiManager) return;
    
    UIWidgetFactory::SetDefaultTextStyle(fontName, fontSize, Color::Black());
    
    std::cout << "🔤 GUI font set to: " << fontName << " (" << fontSize << "pt)" << std::endl;
}

// ============================================================================
// Performance and Optimization
// ============================================================================

void SetGUIBatching(bool enabled) {
    std::cout << "⚡ GUI batching " << (enabled ? "enabled" : "disabled") << std::endl;
}

void SetGUIMaxVertices(int maxVertices) {
    std::cout << "📊 GUI max vertices set to: " << maxVertices << std::endl;
}

const UIRenderSystem::RenderStats& GetGUIRenderStats() {
    static UIRenderSystem::RenderStats defaultStats;
    if (s_uiManager && s_uiManager->GetRenderSystem()) {
        return s_uiManager->GetRenderSystem()->GetStats();
    }
    return defaultStats;
}

const UIManager::UIStats& GetGUIUpdateStats() {
    static UIManager::UIStats defaultStats;
    if (s_uiManager) {
        return s_uiManager->GetStats();
    }
    return defaultStats;
}

} // namespace GUI
} // namespace Engine
