//
// UIInputSystem.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of the GUI input system
//

#include "UIInputSystem.h"
#include "engine/ECS.h"
#include <algorithm>
#include <cmath>
#include <iostream>

namespace Engine {
namespace GUI {

// ============================================================================
// UIFocusManager Implementation
// ============================================================================

void UIFocusManager::SetFocus(Entity entity) {
    if (m_focusedEntity == entity) return;
    
    Entity previousFocus = m_focusedEntity;
    m_focusedEntity = entity;
    
    // Update focus index
    UpdateFocusIndex();
    
    // Note: Focus events would be dispatched by the UIInputSystem
}

void UIFocusManager::ClearFocus() {
    m_focusedEntity = INVALID_ENTITY;
    m_currentFocusIndex = -1;
}

void UIFocusManager::FocusNext() {
    if (m_focusOrder.empty()) return;
    
    m_currentFocusIndex = (m_currentFocusIndex + 1) % static_cast<int>(m_focusOrder.size());
    m_focusedEntity = m_focusOrder[m_currentFocusIndex];
}

void UIFocusManager::FocusPrevious() {
    if (m_focusOrder.empty()) return;
    
    m_currentFocusIndex = (m_currentFocusIndex - 1 + static_cast<int>(m_focusOrder.size())) % static_cast<int>(m_focusOrder.size());
    m_focusedEntity = m_focusOrder[m_currentFocusIndex];
}

void UIFocusManager::SetFocusOrder(const std::vector<Entity>& order) {
    m_focusOrder = order;
    UpdateFocusIndex();
}

void UIFocusManager::UpdateFocusIndex() {
    m_currentFocusIndex = -1;
    for (int i = 0; i < static_cast<int>(m_focusOrder.size()); ++i) {
        if (m_focusOrder[i] == m_focusedEntity) {
            m_currentFocusIndex = i;
            break;
        }
    }
}

// ============================================================================
// UIInputSystem Implementation
// ============================================================================

UIInputSystem::UIInputSystem() {
    // Initialize pressed entities array
    for (int i = 0; i < static_cast<int>(Input::MouseButton::COUNT); ++i) {
        m_pressedEntities[i] = INVALID_ENTITY;
    }
}

UIInputSystem::~UIInputSystem() {
    if (m_initialized) {
        Shutdown();
    }
}

void UIInputSystem::Initialize() {
    if (m_initialized) {
        std::cout << "⚠️  UIInputSystem already initialized" << std::endl;
        return;
    }
    
    m_initialized = true;
    std::cout << "✅ UIInputSystem initialized" << std::endl;
}

void UIInputSystem::Update(DeltaTime deltaTime) {
    if (!m_initialized || !m_inputSystem) return;
    
    // Process mouse input
    ProcessMouseInput();
    
    // Process keyboard input
    ProcessKeyboardInput();
    
    // Update timing for double-click detection
    m_lastClickTime += deltaTime;
}

void UIInputSystem::Shutdown() {
    if (!m_initialized) return;
    
    m_inputSystem = nullptr;
    m_eventHandlers.clear();
    m_blockingEntities.clear();
    m_focusManager.ClearFocus();
    
    m_initialized = false;
    std::cout << "🔧 UIInputSystem shutdown complete" << std::endl;
}

void UIInputSystem::SetInputSystem(Input::InputSystem* inputSystem) {
    m_inputSystem = inputSystem;
}

// ============================================================================
// Mouse Input Processing
// ============================================================================

void UIInputSystem::ProcessMouseInput() {
    if (!m_inputSystem) return;
    
    Vector2 mousePosition = Vector2(m_inputSystem->GetMousePosition().x, m_inputSystem->GetMousePosition().y);
    
    // Handle mouse movement
    if (mousePosition.x != m_lastMousePosition.x || mousePosition.y != m_lastMousePosition.y) {
        HandleMouseMove(mousePosition);
        m_lastMousePosition = mousePosition;
    }
    
    // Handle mouse buttons
    for (int i = 0; i < static_cast<int>(Input::MouseButton::COUNT); ++i) {
        Input::MouseButton button = static_cast<Input::MouseButton>(i);
        
        if (m_inputSystem->IsMouseButtonPressed(button)) {
            HandleMouseButton(button, Input::InputState::Pressed, mousePosition);
        } else if (m_inputSystem->IsMouseButtonReleased(button)) {
            HandleMouseButton(button, Input::InputState::Released, mousePosition);
        }
    }
}

void UIInputSystem::HandleMouseMove(const Vector2& mousePosition) {
    if (!ShouldProcessInput()) return;
    
    // Update hover state
    UpdateHoverState(mousePosition);
    
    // Update drag state
    if (m_draggedEntity != INVALID_ENTITY) {
        UpdateDragState(mousePosition);
    }
}

void UIInputSystem::HandleMouseButton(Input::MouseButton button, Input::InputState state, const Vector2& position) {
    if (!ShouldProcessInput()) return;
    
    int buttonIndex = static_cast<int>(button);
    
    if (state == Input::InputState::Pressed) {
        // Find entity at mouse position
        Entity hitEntity = GetEntityAtPosition(position);
        
        if (hitEntity != INVALID_ENTITY && IsEntityInteractable(hitEntity)) {
            SetPressedEntity(hitEntity, button);
            
            // Set focus if entity is focusable
            if (GetECS() && GetECS()->HasComponent<UIInteractable>(hitEntity)) {
                m_focusManager.SetFocus(hitEntity);
                DispatchFocusEvent(UIInputEvent::Type::FocusGained, hitEntity);
            }
            
            // Dispatch mouse down event
            DispatchMouseEvent(UIInputEvent::Type::MouseDown, hitEntity, position, button);
            
            // Check for drag start
            if (button == Input::MouseButton::Left) {
                m_dragStartPosition = position;
                m_isDragStarted = false;
            }
        } else {
            // Clicked on empty space - clear focus
            if (m_focusManager.GetFocusedEntity() != INVALID_ENTITY) {
                Entity previousFocus = m_focusManager.GetFocusedEntity();
                m_focusManager.ClearFocus();
                DispatchFocusEvent(UIInputEvent::Type::FocusLost, previousFocus);
            }
        }
    } else if (state == Input::InputState::Released) {
        Entity pressedEntity = m_pressedEntities[buttonIndex];
        
        if (pressedEntity != INVALID_ENTITY) {
            // Dispatch mouse up event
            DispatchMouseEvent(UIInputEvent::Type::MouseUp, pressedEntity, position, button);
            
            // Check for click (mouse up on same entity as mouse down)
            Entity hitEntity = GetEntityAtPosition(position);
            if (hitEntity == pressedEntity) {
                ProcessClickEvents(hitEntity, button, position);
            }
            
            // Clear pressed state
            m_pressedEntities[buttonIndex] = INVALID_ENTITY;
        }
        
        // End drag if left button released
        if (button == Input::MouseButton::Left && m_draggedEntity != INVALID_ENTITY) {
            EndDrag(position);
        }
    }
}

void UIInputSystem::ProcessClickEvents(Entity entity, Input::MouseButton button, const Vector2& position) {
    // Dispatch click event
    DispatchMouseEvent(UIInputEvent::Type::MouseClick, entity, position, button);
    
    // Check for double-click
    if (button == Input::MouseButton::Left) {
        if (entity == m_lastClickedEntity && m_lastClickTime < m_doubleClickTime) {
            DispatchMouseEvent(UIInputEvent::Type::MouseDoubleClick, entity, position, button);
        }
        
        m_lastClickedEntity = entity;
        m_lastClickTime = 0.0f;
    }
    
    // Trigger component callbacks
    if (GetECS() && GetECS()->HasComponent<UIInteractable>(entity)) {
        auto& interactable = GetECS()->GetComponent<UIInteractable>(entity);
        if (interactable.onClick) {
            interactable.onClick(entity);
        }
    }
}

// ============================================================================
// Keyboard Input Processing
// ============================================================================

void UIInputSystem::ProcessKeyboardInput() {
    if (!m_inputSystem) return;
    
    // Handle tab navigation
    if (m_inputSystem->IsKeyPressed(Input::KeyCode::Tab)) {
        if (m_inputSystem->IsKeyHeld(Input::KeyCode::Shift)) {
            m_focusManager.FocusPrevious();
        } else {
            m_focusManager.FocusNext();
        }
    }
    
    // Handle escape key (clear focus)
    if (m_inputSystem->IsKeyPressed(Input::KeyCode::Escape)) {
        if (m_focusManager.GetFocusedEntity() != INVALID_ENTITY) {
            Entity previousFocus = m_focusManager.GetFocusedEntity();
            m_focusManager.ClearFocus();
            DispatchFocusEvent(UIInputEvent::Type::FocusLost, previousFocus);
        }
    }
    
    // Forward key events to focused entity
    Entity focusedEntity = m_focusManager.GetFocusedEntity();
    if (focusedEntity != INVALID_ENTITY) {
        // Check all keys for press/release events
        for (int i = 0; i < static_cast<int>(Input::KeyCode::COUNT); ++i) {
            Input::KeyCode key = static_cast<Input::KeyCode>(i);
            
            if (m_inputSystem->IsKeyPressed(key)) {
                DispatchKeyEvent(UIInputEvent::Type::KeyDown, focusedEntity, key);
            } else if (m_inputSystem->IsKeyReleased(key)) {
                DispatchKeyEvent(UIInputEvent::Type::KeyUp, focusedEntity, key);
            }
        }
    }
}

// ============================================================================
// Hit Testing
// ============================================================================

Entity UIInputSystem::GetEntityAtPosition(const Vector2& position) {
    auto entities = GetSortedEntitiesAtPosition(position);
    return entities.empty() ? INVALID_ENTITY : entities.front();
}

std::vector<Entity> UIInputSystem::GetEntitiesAtPosition(const Vector2& position) {
    return GetSortedEntitiesAtPosition(position);
}

bool UIInputSystem::IsPositionInEntity(const Vector2& position, Entity entity) {
    if (!GetECS() || !GetECS()->HasComponent<UITransform>(entity)) return false;
    
    const auto& transform = GetECS()->GetComponent<UITransform>(entity);
    return transform.worldRect.Contains(position);
}

std::vector<Entity> UIInputSystem::GetSortedEntitiesAtPosition(const Vector2& position) {
    std::vector<Entity> hitEntities;
    
    if (!GetECS()) return hitEntities;
    
    // Get all entities with UITransform and UIRenderer components
    auto entities = GetECS()->GetEntitiesWithComponents<UITransform, UIRenderer>();
    
    for (Entity entity : entities) {
        if (IsPositionInEntity(position, entity) && IsEntityVisible(entity)) {
            hitEntities.push_back(entity);
        }
    }
    
    // Sort by rendering order (higher values on top)
    std::sort(hitEntities.begin(), hitEntities.end(), [this](Entity a, Entity b) {
        const auto& rendererA = GetECS()->GetComponent<UIRenderer>(a);
        const auto& rendererB = GetECS()->GetComponent<UIRenderer>(b);
        return rendererA.sortingOrder > rendererB.sortingOrder;
    });
    
    return hitEntities;
}

// ============================================================================
// Helper Methods
// ============================================================================

void UIInputSystem::UpdateHoverState(const Vector2& mousePosition) {
    Entity newHoveredEntity = GetEntityAtPosition(mousePosition);
    
    if (newHoveredEntity != m_hoveredEntity) {
        // Mouse exit previous entity
        if (m_hoveredEntity != INVALID_ENTITY) {
            DispatchMouseEvent(UIInputEvent::Type::MouseExit, m_hoveredEntity, mousePosition);
            
            if (GetECS() && GetECS()->HasComponent<UIInteractable>(m_hoveredEntity)) {
                auto& interactable = GetECS()->GetComponent<UIInteractable>(m_hoveredEntity);
                interactable.SetState(UIState::Normal);
                if (interactable.onUnhover) {
                    interactable.onUnhover(m_hoveredEntity);
                }
            }
        }
        
        // Mouse enter new entity
        if (newHoveredEntity != INVALID_ENTITY && IsEntityInteractable(newHoveredEntity)) {
            DispatchMouseEvent(UIInputEvent::Type::MouseEnter, newHoveredEntity, mousePosition);
            
            if (GetECS() && GetECS()->HasComponent<UIInteractable>(newHoveredEntity)) {
                auto& interactable = GetECS()->GetComponent<UIInteractable>(newHoveredEntity);
                interactable.SetState(UIState::Hovered);
                if (interactable.onHover) {
                    interactable.onHover(newHoveredEntity);
                }
            }
        }
        
        m_hoveredEntity = newHoveredEntity;
    }
}

bool UIInputSystem::IsEntityInteractable(Entity entity) {
    if (!GetECS()) return false;
    
    // Must have UIInteractable component and be interactable
    if (!GetECS()->HasComponent<UIInteractable>(entity)) return false;
    
    const auto& interactable = GetECS()->GetComponent<UIInteractable>(entity);
    return interactable.interactable;
}

bool UIInputSystem::IsEntityVisible(Entity entity) {
    if (!GetECS()) return false;
    
    // Must have UIRenderer component and be visible
    if (!GetECS()->HasComponent<UIRenderer>(entity)) return false;
    
    const auto& renderer = GetECS()->GetComponent<UIRenderer>(entity);
    return renderer.visible && renderer.alpha > 0.0f;
}

bool UIInputSystem::ShouldProcessInput() const {
    return m_initialized && m_inputSystem;
}

void UIInputSystem::SetPressedEntity(Entity entity, Input::MouseButton button) {
    int buttonIndex = static_cast<int>(button);
    m_pressedEntities[buttonIndex] = entity;
    
    if (GetECS() && GetECS()->HasComponent<UIInteractable>(entity)) {
        auto& interactable = GetECS()->GetComponent<UIInteractable>(entity);
        interactable.SetState(UIState::Pressed);
        if (interactable.onPress) {
            interactable.onPress(entity);
        }
    }
}

// ============================================================================
// Event Dispatch
// ============================================================================

void UIInputSystem::DispatchMouseEvent(UIInputEvent::Type type, Entity entity, const Vector2& position, Input::MouseButton button) {
    UIInputEvent event(type, entity);
    event.mousePosition = position;
    event.mouseButton = button;
    DispatchEvent(event);
}

void UIInputSystem::DispatchKeyEvent(UIInputEvent::Type type, Entity entity, Input::KeyCode key) {
    UIInputEvent event(type, entity);
    event.keyCode = key;
    DispatchEvent(event);
}

void UIInputSystem::DispatchFocusEvent(UIInputEvent::Type type, Entity entity) {
    UIInputEvent event(type, entity);
    DispatchEvent(event);
}

void UIInputSystem::DispatchEvent(const UIInputEvent& event) {
    auto it = m_eventHandlers.find(event.targetEntity);
    if (it != m_eventHandlers.end()) {
        it->second(event);
    }
}

// ============================================================================
// Additional Methods
// ============================================================================

void UIInputSystem::UpdateDragState(const Vector2& mousePosition) {
    if (m_draggedEntity == INVALID_ENTITY) return;

    // Check if we should start dragging
    if (!m_isDragStarted) {
        float distance = std::sqrt(std::pow(mousePosition.x - m_dragStartPosition.x, 2) +
                                  std::pow(mousePosition.y - m_dragStartPosition.y, 2));
        if (distance > m_dragThreshold) {
            m_isDragStarted = true;
            StartDrag(m_draggedEntity, m_dragStartPosition);
        }
    }

    if (m_isDragStarted) {
        UpdateDrag(mousePosition);
    }
}

void UIInputSystem::StartDrag(Entity entity, const Vector2& startPosition) {
    m_draggedEntity = entity;
    m_dragStartPosition = startPosition;
    m_isDragStarted = true;

    // Dispatch drag start event
    UIInputEvent event(UIInputEvent::Type::MouseDrag, entity);
    event.mousePosition = startPosition;
    DispatchEvent(event);
}

void UIInputSystem::UpdateDrag(const Vector2& currentPosition) {
    if (m_draggedEntity == INVALID_ENTITY) return;

    // Dispatch drag update event
    UIInputEvent event(UIInputEvent::Type::MouseDrag, m_draggedEntity);
    event.mousePosition = currentPosition;
    DispatchEvent(event);
}

void UIInputSystem::EndDrag(const Vector2& endPosition) {
    if (m_draggedEntity == INVALID_ENTITY) return;

    Entity draggedEntity = m_draggedEntity;
    m_draggedEntity = INVALID_ENTITY;
    m_isDragStarted = false;

    // Dispatch drag end event (could be a custom event type)
    UIInputEvent event(UIInputEvent::Type::MouseUp, draggedEntity);
    event.mousePosition = endPosition;
    DispatchEvent(event);
}

Entity UIInputSystem::GetPressedEntity(Input::MouseButton button) const {
    int buttonIndex = static_cast<int>(button);
    if (buttonIndex >= 0 && buttonIndex < static_cast<int>(Input::MouseButton::COUNT)) {
        return m_pressedEntities[buttonIndex];
    }
    return INVALID_ENTITY;
}

void UIInputSystem::SetHoveredEntity(Entity entity) {
    m_hoveredEntity = entity;
}

void UIInputSystem::SetDraggedEntity(Entity entity) {
    m_draggedEntity = entity;
}

void UIInputSystem::RegisterEventHandler(Entity entity, std::function<void(const UIInputEvent&)> handler) {
    m_eventHandlers[entity] = handler;
}

void UIInputSystem::UnregisterEventHandler(Entity entity) {
    auto it = m_eventHandlers.find(entity);
    if (it != m_eventHandlers.end()) {
        m_eventHandlers.erase(it);
    }
}

void UIInputSystem::SetInputCapture(Entity entity, bool capture) {
    if (capture) {
        m_captureEntity = entity;
    } else if (m_captureEntity == entity) {
        m_captureEntity = INVALID_ENTITY;
    }
}

void UIInputSystem::SetInputBlocking(Entity entity, bool blocking) {
    if (blocking) {
        m_blockingEntities.insert(entity);
    } else {
        m_blockingEntities.erase(entity);
    }
}

bool UIInputSystem::IsInputBlocked(const Vector2& position) const {
    for (Entity entity : m_blockingEntities) {
        if (IsPositionInEntity(position, entity)) {
            return true;
        }
    }
    return false;
}

void UIInputSystem::HandleKeyInput(Input::KeyCode key, Input::InputState state, bool isRepeat) {
    Entity focusedEntity = m_focusManager.GetFocusedEntity();
    if (focusedEntity != INVALID_ENTITY) {
        if (state == Input::InputState::Pressed) {
            DispatchKeyEvent(UIInputEvent::Type::KeyDown, focusedEntity, key);
        } else if (state == Input::InputState::Released) {
            DispatchKeyEvent(UIInputEvent::Type::KeyUp, focusedEntity, key);
        }
    }
}

void UIInputSystem::HandleTextInput(const std::string& text) {
    Entity focusedEntity = m_focusManager.GetFocusedEntity();
    if (focusedEntity != INVALID_ENTITY) {
        UIInputEvent event(UIInputEvent::Type::TextInput, focusedEntity);
        event.textInput = text;
        DispatchEvent(event);
    }
}

void UIInputSystem::HandleMouseScroll(float deltaX, float deltaY, const Vector2& position) {
    Entity hitEntity = GetEntityAtPosition(position);
    if (hitEntity != INVALID_ENTITY && IsEntityInteractable(hitEntity)) {
        // Handle scroll events - could be implemented as a custom event type
        // For now, we'll just forward to the entity's event handler
        UIInputEvent event(UIInputEvent::Type::MouseClick, hitEntity); // Placeholder
        event.mousePosition = position;
        DispatchEvent(event);
    }
}

} // namespace GUI
} // namespace Engine
