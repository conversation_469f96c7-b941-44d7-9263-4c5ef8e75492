//
// UILayoutSystem.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of the GUI layout system
//

#include "UILayoutSystem.h"
#include "engine/ECS.h"
#include <algorithm>
#include <cmath>
#include <iostream>

namespace Engine {
namespace GUI {

// ============================================================================
// UILayoutSystem Implementation
// ============================================================================

UILayoutSystem::UILayoutSystem() {
    // Constructor
}

UILayoutSystem::~UILayoutSystem() {
    if (m_initialized) {
        Shutdown();
    }
}

void UILayoutSystem::Initialize() {
    if (m_initialized) {
        std::cout << "⚠️  UILayoutSystem already initialized" << std::endl;
        return;
    }
    
    m_initialized = true;
    std::cout << "✅ UILayoutSystem initialized" << std::endl;
}

void UILayoutSystem::Update(DeltaTime deltaTime) {
    if (!m_initialized) return;
    
    // Process any pending layout updates
    ProcessLayoutQueue();
}

void UILayoutSystem::Shutdown() {
    if (!m_initialized) return;
    
    m_layoutGroups.clear();
    m_constraints.clear();
    m_layoutQueue.clear();
    m_layoutDirty.clear();
    
    m_initialized = false;
    std::cout << "🔧 UILayoutSystem shutdown complete" << std::endl;
}

// ============================================================================
// Layout Management
// ============================================================================

void UILayoutSystem::RegisterLayoutGroup(Entity entity, const LayoutGroup& group) {
    m_layoutGroups[entity] = group;
    MarkLayoutDirty(entity);
}

void UILayoutSystem::UnregisterLayoutGroup(Entity entity) {
    auto it = m_layoutGroups.find(entity);
    if (it != m_layoutGroups.end()) {
        m_layoutGroups.erase(it);
    }
}

void UILayoutSystem::UpdateLayout(Entity entity) {
    MarkLayoutDirty(entity);
}

void UILayoutSystem::UpdateAllLayouts() {
    // Mark all layout groups as dirty
    for (const auto& pair : m_layoutGroups) {
        MarkLayoutDirty(pair.first);
    }
}

// ============================================================================
// Layout Calculation
// ============================================================================

void UILayoutSystem::CalculateLayout(Entity entity) {
    auto it = m_layoutGroups.find(entity);
    if (it == m_layoutGroups.end()) return;
    
    const LayoutGroup& group = it->second;
    
    switch (group.layoutType) {
        case UILayout::LayoutType::Horizontal:
            CalculateHorizontalLayout(entity, group);
            break;
            
        case UILayout::LayoutType::Vertical:
            CalculateVerticalLayout(entity, group);
            break;
            
        case UILayout::LayoutType::Grid:
            CalculateGridLayout(entity, group);
            break;
            
        case UILayout::LayoutType::Flexible:
            CalculateFlexibleLayout(entity, group);
            break;
            
        case UILayout::LayoutType::None:
        default:
            // No automatic layout - just apply constraints
            ApplyConstraints(entity);
            break;
    }
    
    // Update children layouts
    UpdateChildrenLayout(entity);
}

void UILayoutSystem::CalculateHorizontalLayout(Entity entity, const LayoutGroup& group) {
    if (!GetECS()) return;
    
    auto& transform = GetECS()->GetComponent<UITransform>(entity);
    
    // Calculate available space
    float availableWidth = transform.worldSize.x - group.padding.x * 2.0f;
    float availableHeight = transform.worldSize.y - group.padding.y * 2.0f;
    
    // Calculate total spacing
    float totalSpacing = (group.children.size() > 1) ? (group.children.size() - 1) * group.spacing.x : 0.0f;
    float contentWidth = availableWidth - totalSpacing;
    
    // Distribute space among children
    std::vector<Entity> children = group.children;
    DistributeSpace(children, contentWidth, true, group.horizontalAlignment);
    
    // Position children
    float currentX = transform.worldPosition.x + group.padding.x;
    float currentY = transform.worldPosition.y + group.padding.y;
    
    for (Entity child : children) {
        if (!GetECS()->IsEntityValid(child)) continue;
        
        auto& childTransform = GetECS()->GetComponent<UITransform>(child);
        
        // Set position
        childTransform.worldPosition.x = currentX;
        childTransform.worldPosition.y = currentY;
        
        // Apply vertical alignment
        if (group.verticalAlignment == LayoutGroup::Alignment::Center) {
            childTransform.worldPosition.y += (availableHeight - childTransform.worldSize.y) * 0.5f;
        } else if (group.verticalAlignment == LayoutGroup::Alignment::End) {
            childTransform.worldPosition.y += availableHeight - childTransform.worldSize.y;
        } else if (group.verticalAlignment == LayoutGroup::Alignment::Stretch) {
            childTransform.worldSize.y = availableHeight;
        }
        
        // Update world rect
        childTransform.worldRect = Rect(childTransform.worldPosition.x, childTransform.worldPosition.y,
                                       childTransform.worldSize.x, childTransform.worldSize.y);
        
        // Move to next position
        currentX += childTransform.worldSize.x + group.spacing.x;
    }
}

void UILayoutSystem::CalculateVerticalLayout(Entity entity, const LayoutGroup& group) {
    if (!GetECS()) return;
    
    auto& transform = GetECS()->GetComponent<UITransform>(entity);
    
    // Calculate available space
    float availableWidth = transform.worldSize.x - group.padding.x * 2.0f;
    float availableHeight = transform.worldSize.y - group.padding.y * 2.0f;
    
    // Calculate total spacing
    float totalSpacing = (group.children.size() > 1) ? (group.children.size() - 1) * group.spacing.y : 0.0f;
    float contentHeight = availableHeight - totalSpacing;
    
    // Distribute space among children
    std::vector<Entity> children = group.children;
    DistributeSpace(children, contentHeight, false, group.verticalAlignment);
    
    // Position children
    float currentX = transform.worldPosition.x + group.padding.x;
    float currentY = transform.worldPosition.y + group.padding.y;
    
    for (Entity child : children) {
        if (!GetECS()->IsEntityValid(child)) continue;
        
        auto& childTransform = GetECS()->GetComponent<UITransform>(child);
        
        // Set position
        childTransform.worldPosition.x = currentX;
        childTransform.worldPosition.y = currentY;
        
        // Apply horizontal alignment
        if (group.horizontalAlignment == LayoutGroup::Alignment::Center) {
            childTransform.worldPosition.x += (availableWidth - childTransform.worldSize.x) * 0.5f;
        } else if (group.horizontalAlignment == LayoutGroup::Alignment::End) {
            childTransform.worldPosition.x += availableWidth - childTransform.worldSize.x;
        } else if (group.horizontalAlignment == LayoutGroup::Alignment::Stretch) {
            childTransform.worldSize.x = availableWidth;
        }
        
        // Update world rect
        childTransform.worldRect = Rect(childTransform.worldPosition.x, childTransform.worldPosition.y,
                                       childTransform.worldSize.x, childTransform.worldSize.y);
        
        // Move to next position
        currentY += childTransform.worldSize.y + group.spacing.y;
    }
}

void UILayoutSystem::CalculateGridLayout(Entity entity, const LayoutGroup& group) {
    if (!GetECS()) return;
    
    auto& transform = GetECS()->GetComponent<UITransform>(entity);
    
    // Calculate available space
    float availableWidth = transform.worldSize.x - group.padding.x * 2.0f;
    float availableHeight = transform.worldSize.y - group.padding.y * 2.0f;
    
    // Determine grid size
    int columns = group.gridColumns;
    int rows = group.gridRows;
    
    if (group.autoGridSize && !group.children.empty()) {
        // Auto-calculate grid size based on children count
        int childCount = static_cast<int>(group.children.size());
        if (columns > 0) {
            rows = (childCount + columns - 1) / columns; // Ceiling division
        } else if (rows > 0) {
            columns = (childCount + rows - 1) / rows;
        } else {
            // Default to square grid
            columns = static_cast<int>(std::ceil(std::sqrt(childCount)));
            rows = (childCount + columns - 1) / columns;
        }
    }
    
    if (columns <= 0 || rows <= 0) return;
    
    // Calculate cell size
    float cellWidth = (availableWidth - (columns - 1) * group.spacing.x) / columns;
    float cellHeight = (availableHeight - (rows - 1) * group.spacing.y) / rows;
    
    // Position children in grid
    float startX = transform.worldPosition.x + group.padding.x;
    float startY = transform.worldPosition.y + group.padding.y;
    
    for (int i = 0; i < static_cast<int>(group.children.size()); ++i) {
        Entity child = group.children[i];
        if (!GetECS()->IsEntityValid(child)) continue;
        
        int col = i % columns;
        int row = i / columns;
        
        if (row >= rows) break; // Don't exceed grid bounds
        
        auto& childTransform = GetECS()->GetComponent<UITransform>(child);
        
        // Calculate cell position
        float cellX = startX + col * (cellWidth + group.spacing.x);
        float cellY = startY + row * (cellHeight + group.spacing.y);
        
        // Set child size to cell size (or maintain aspect ratio)
        childTransform.worldSize.x = cellWidth;
        childTransform.worldSize.y = cellHeight;
        
        // Position child in cell
        childTransform.worldPosition.x = cellX;
        childTransform.worldPosition.y = cellY;
        
        // Update world rect
        childTransform.worldRect = Rect(childTransform.worldPosition.x, childTransform.worldPosition.y,
                                       childTransform.worldSize.x, childTransform.worldSize.y);
    }
}

void UILayoutSystem::CalculateFlexibleLayout(Entity entity, const LayoutGroup& group) {
    // Flexible layout implementation would be more complex
    // For now, fall back to vertical layout
    CalculateVerticalLayout(entity, group);
}

// ============================================================================
// Helper Methods
// ============================================================================

void UILayoutSystem::MarkLayoutDirty(Entity entity) {
    m_layoutDirty[entity] = true;
    
    // Add to layout queue if not already present
    auto it = std::find(m_layoutQueue.begin(), m_layoutQueue.end(), entity);
    if (it == m_layoutQueue.end()) {
        m_layoutQueue.push_back(entity);
    }
}

void UILayoutSystem::ProcessLayoutQueue() {
    while (!m_layoutQueue.empty()) {
        Entity entity = m_layoutQueue.front();
        m_layoutQueue.erase(m_layoutQueue.begin());
        
        if (IsLayoutDirty(entity)) {
            CalculateLayout(entity);
            m_layoutDirty[entity] = false;
        }
    }
}

bool UILayoutSystem::IsLayoutDirty(Entity entity) const {
    auto it = m_layoutDirty.find(entity);
    return it != m_layoutDirty.end() && it->second;
}

void UILayoutSystem::DistributeSpace(std::vector<Entity>& children, float availableSpace, 
                                    bool horizontal, LayoutGroup::Alignment alignment) {
    if (children.empty() || !GetECS()) return;
    
    // For now, distribute space equally
    float spacePerChild = availableSpace / children.size();
    
    for (Entity child : children) {
        if (!GetECS()->IsEntityValid(child)) continue;
        
        auto& childTransform = GetECS()->GetComponent<UITransform>(child);
        
        if (horizontal) {
            childTransform.worldSize.x = spacePerChild;
        } else {
            childTransform.worldSize.y = spacePerChild;
        }
    }
}

void UILayoutSystem::ApplyConstraints(Entity entity) {
    auto it = m_constraints.find(entity);
    if (it != m_constraints.end()) {
        for (const auto& constraint : it->second) {
            SolveConstraint(entity, constraint);
        }
    }
}

bool UILayoutSystem::SolveConstraint(Entity entity, const LayoutConstraint& constraint) {
    if (!GetECS() || !GetECS()->IsEntityValid(entity)) return false;
    
    auto& transform = GetECS()->GetComponent<UITransform>(entity);
    
    switch (constraint.type) {
        case ConstraintType::Fixed:
            transform.worldPosition = constraint.value;
            break;
            
        case ConstraintType::Relative:
            // Implement relative positioning
            break;
            
        case ConstraintType::AspectRatio:
            // Maintain aspect ratio
            if (constraint.value.x > 0.0f) {
                transform.worldSize.y = transform.worldSize.x / constraint.value.x;
            }
            break;
            
        default:
            break;
    }
    
    return true;
}

void UILayoutSystem::UpdateChildrenLayout(Entity entity) {
    if (!GetECS()) return;
    
    auto& transform = GetECS()->GetComponent<UITransform>(entity);
    for (Entity child : transform.children) {
        if (IsLayoutDirty(child)) {
            CalculateLayout(child);
        }
    }
}

void UILayoutSystem::SetCanvasSize(const Vector2& size) {
    m_canvasSize = size;
    UpdateAllLayouts();
}

void UILayoutSystem::SetScreenSize(const Vector2& size) {
    m_screenSize = size;
    UpdateAllLayouts();
}

// ============================================================================
// Layout Helper Functions
// ============================================================================

LayoutGroup CreateHorizontalLayoutGroup(float spacing, LayoutGroup::Alignment alignment) {
    LayoutGroup group(UILayout::LayoutType::Horizontal);
    group.spacing = Vector2(spacing, 0.0f);
    group.horizontalAlignment = alignment;
    return group;
}

LayoutGroup CreateVerticalLayoutGroup(float spacing, LayoutGroup::Alignment alignment) {
    LayoutGroup group(UILayout::LayoutType::Vertical);
    group.spacing = Vector2(0.0f, spacing);
    group.verticalAlignment = alignment;
    return group;
}

LayoutGroup CreateGridLayoutGroup(int columns, int rows, const Vector2& spacing) {
    LayoutGroup group(UILayout::LayoutType::Grid);
    group.gridColumns = columns;
    group.gridRows = rows;
    group.spacing = spacing;
    return group;
}

LayoutGroup CreateFlexibleLayoutGroup(bool wrapContent) {
    LayoutGroup group(UILayout::LayoutType::Flexible);
    group.wrapContent = wrapContent;
    return group;
}

// Create common constraints
LayoutConstraint CreateFixedConstraint(const Vector2& position, const Vector2& size) {
    LayoutConstraint constraint(ConstraintType::Fixed, position);
    return constraint;
}

LayoutConstraint CreateRelativeConstraint(float xPercent, float yPercent,
                                        float widthPercent, float heightPercent) {
    LayoutConstraint constraint(ConstraintType::Relative, Vector2(xPercent, yPercent));
    return constraint;
}

LayoutConstraint CreateAspectRatioConstraint(float aspectRatio) {
    LayoutConstraint constraint(ConstraintType::AspectRatio, Vector2(aspectRatio, 0.0f));
    return constraint;
}

LayoutConstraint CreateContentSizeConstraint() {
    LayoutConstraint constraint(ConstraintType::ContentSize, Vector2(0.0f, 0.0f));
    return constraint;
}

LayoutConstraint CreateFillConstraint() {
    LayoutConstraint constraint(ConstraintType::Fill, Vector2(0.0f, 0.0f));
    return constraint;
}

LayoutConstraint CreateCenterConstraint() {
    LayoutConstraint constraint(ConstraintType::Center, Vector2(0.0f, 0.0f));
    return constraint;
}

// Anchor preset functions
void SetAnchorPreset(UITransform& transform, AnchorType anchor) {
    transform.SetAnchor(anchor);
}

void SetAnchorAndPivot(UITransform& transform, const Vector2& anchorMin,
                      const Vector2& anchorMax, const Vector2& pivot) {
    transform.anchorMin = anchorMin;
    transform.anchorMax = anchorMax;
    transform.pivot = pivot;
}

} // namespace GUI
} // namespace Engine
