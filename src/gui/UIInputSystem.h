//
// UIInputSystem.h
// macOS 3D Looter-Shooter Game Engine
//
// GUI input system for handling mouse and keyboard interactions with UI elements
//

#pragma once

#include "engine/System.h"
#include "UIComponents.h"
#include "input/InputSystem.h"
#include <vector>
#include <unordered_set>

namespace Engine {
namespace GUI {

// ============================================================================
// GUI Input Events
// ============================================================================

struct UIInputEvent {
    enum class Type {
        MouseEnter,
        MouseExit,
        MouseDown,
        MouseUp,
        <PERSON>Click,
        <PERSON>DoubleClick,
        MouseDrag,
        KeyDown,
        KeyUp,
        TextInput,
        FocusGained,
        FocusLost
    };
    
    Type type;
    Entity targetEntity = INVALID_ENTITY;
    Vector2 mousePosition;
    Input::MouseButton mouseButton = Input::MouseButton::Left;
    Input::KeyCode keyCode = Input::KeyCode::A;
    std::string textInput;
    uint32_t modifiers = 0;  // Shift, Ctrl, Alt, etc.
    
    UIInputEvent(Type t, Entity target) : type(t), targetEntity(target) {}
};

// ============================================================================
// Focus Management
// ============================================================================

class UIFocusManager {
public:
    // Focus management
    void SetFocus(Entity entity);
    void ClearFocus();
    Entity GetFocusedEntity() const { return m_focusedEntity; }
    bool HasFocus(Entity entity) const { return m_focusedEntity == entity; }
    
    // Focus navigation
    void FocusNext();
    void FocusPrevious();
    void SetFocusOrder(const std::vector<Entity>& order);
    
    // Focus groups (for tab navigation)
    void AddToFocusGroup(const std::string& groupName, Entity entity);
    void RemoveFromFocusGroup(const std::string& groupName, Entity entity);
    void SetActiveFocusGroup(const std::string& groupName);

private:
    Entity m_focusedEntity = INVALID_ENTITY;
    std::vector<Entity> m_focusOrder;
    int m_currentFocusIndex = -1;
    
    std::unordered_map<std::string, std::vector<Entity>> m_focusGroups;
    std::string m_activeFocusGroup;
    
    void UpdateFocusIndex();
};

// ============================================================================
// GUI Input System
// ============================================================================

class UIInputSystem : public System {
public:
    UIInputSystem();
    ~UIInputSystem();
    
    // System interface
    void Initialize() override;
    void Update(DeltaTime deltaTime) override;
    void Shutdown() override;
    const char* GetName() const override { return "UIInputSystem"; }
    
    // Input system integration
    void SetInputSystem(Input::InputSystem* inputSystem);
    Input::InputSystem* GetInputSystem() const { return m_inputSystem; }
    
    // Mouse interaction
    void ProcessMouseInput();
    void HandleMouseMove(const Vector2& mousePosition);
    void HandleMouseButton(Input::MouseButton button, Input::InputState state, const Vector2& position);
    void HandleMouseScroll(float deltaX, float deltaY, const Vector2& position);
    
    // Keyboard interaction
    void ProcessKeyboardInput();
    void HandleKeyInput(Input::KeyCode key, Input::InputState state, bool isRepeat = false);
    void HandleTextInput(const std::string& text);
    
    // Hit testing
    Entity GetEntityAtPosition(const Vector2& position);
    std::vector<Entity> GetEntitiesAtPosition(const Vector2& position);
    bool IsPositionInEntity(const Vector2& position, Entity entity);
    
    // Interaction state management
    void SetHoveredEntity(Entity entity);
    void SetPressedEntity(Entity entity, Input::MouseButton button);
    void SetDraggedEntity(Entity entity);
    
    Entity GetHoveredEntity() const { return m_hoveredEntity; }
    Entity GetPressedEntity(Input::MouseButton button) const;
    Entity GetDraggedEntity() const { return m_draggedEntity; }
    
    // Focus management
    UIFocusManager& GetFocusManager() { return m_focusManager; }
    const UIFocusManager& GetFocusManager() const { return m_focusManager; }
    
    // Event handling
    void RegisterEventHandler(Entity entity, std::function<void(const UIInputEvent&)> handler);
    void UnregisterEventHandler(Entity entity);
    void DispatchEvent(const UIInputEvent& event);
    
    // Input capture and blocking
    void SetInputCapture(Entity entity, bool capture);
    void SetInputBlocking(Entity entity, bool blocking);
    bool IsInputCaptured() const { return m_captureEntity != INVALID_ENTITY; }
    Entity GetCaptureEntity() const { return m_captureEntity; }
    
    // Drag and drop
    void StartDrag(Entity entity, const Vector2& startPosition);
    void UpdateDrag(const Vector2& currentPosition);
    void EndDrag(const Vector2& endPosition);
    bool IsDragging() const { return m_draggedEntity != INVALID_ENTITY; }
    
    // Configuration
    void SetDoubleClickTime(float time) { m_doubleClickTime = time; }
    float GetDoubleClickTime() const { return m_doubleClickTime; }
    void SetDragThreshold(float threshold) { m_dragThreshold = threshold; }
    float GetDragThreshold() const { return m_dragThreshold; }
    
    // Debug and visualization
    void SetDebugMode(bool enabled) { m_debugMode = enabled; }
    bool IsDebugMode() const { return m_debugMode; }
    void DrawInputDebugInfo();

private:
    // Input system reference
    Input::InputSystem* m_inputSystem = nullptr;
    
    // Current interaction state
    Entity m_hoveredEntity = INVALID_ENTITY;
    Entity m_pressedEntities[static_cast<int>(Input::MouseButton::COUNT)] = {INVALID_ENTITY};
    Entity m_draggedEntity = INVALID_ENTITY;
    Entity m_captureEntity = INVALID_ENTITY;
    
    // Mouse state
    Vector2 m_lastMousePosition;
    Vector2 m_dragStartPosition;
    bool m_isDragStarted = false;
    
    // Click detection
    float m_lastClickTime = 0.0f;
    Entity m_lastClickedEntity = INVALID_ENTITY;
    
    // Focus management
    UIFocusManager m_focusManager;
    
    // Event handlers
    std::unordered_map<Entity, std::function<void(const UIInputEvent&)>> m_eventHandlers;
    
    // Input blocking entities
    std::unordered_set<Entity> m_blockingEntities;
    
    // Configuration
    float m_doubleClickTime = 0.3f;
    float m_dragThreshold = 5.0f;
    
    // Debug mode
    bool m_debugMode = false;
    
    // Internal helper methods
    void UpdateHoverState(const Vector2& mousePosition);
    void UpdateDragState(const Vector2& mousePosition);
    void ProcessClickEvents(Entity entity, Input::MouseButton button, const Vector2& position);
    
    // Event dispatch helpers
    void DispatchMouseEvent(UIInputEvent::Type type, Entity entity, const Vector2& position, 
                           Input::MouseButton button = Input::MouseButton::Left);
    void DispatchKeyEvent(UIInputEvent::Type type, Entity entity, Input::KeyCode key);
    void DispatchTextEvent(Entity entity, const std::string& text);
    void DispatchFocusEvent(UIInputEvent::Type type, Entity entity);
    
    // Hit testing helpers
    bool IsEntityInteractable(Entity entity);
    bool IsEntityVisible(Entity entity);
    std::vector<Entity> GetSortedEntitiesAtPosition(const Vector2& position);
    
    // Input validation
    bool ShouldProcessInput() const;
    bool IsInputBlocked(const Vector2& position) const;
    
    bool m_initialized = false;
};

// ============================================================================
// Input Event Callbacks
// ============================================================================

// Convenience functions for setting up common input callbacks
void SetupButtonCallbacks(Entity buttonEntity, UIInputSystem& inputSystem);
void SetupTextFieldCallbacks(Entity textFieldEntity, UIInputSystem& inputSystem);
void SetupSliderCallbacks(Entity sliderEntity, UIInputSystem& inputSystem);
void SetupScrollViewCallbacks(Entity scrollViewEntity, UIInputSystem& inputSystem);

} // namespace GUI
} // namespace Engine
