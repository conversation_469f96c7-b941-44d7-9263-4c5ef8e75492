//
// UITextComponents.h
// macOS 3D Looter-Shooter Game Engine
//
// Text and image specific GUI components
//

#pragma once

#include "UIComponents.h"
#include <string>

namespace Engine {
namespace GUI {

// ============================================================================
// Text Components
// ============================================================================

// UI Text Component - Handles text rendering
struct UIText {
    // Text content
    std::string text;
    
    // Font properties
    std::string fontName = "Arial";
    float fontSize = 16.0f;
    bool bold = false;
    bool italic = false;
    
    // Text formatting
    enum class TextAlignment {
        Left,
        Center,
        Right,
        Justify
    };
    
    enum class VerticalAlignment {
        Top,
        Middle,
        Bottom
    };
    
    TextAlignment horizontalAlignment = TextAlignment::Left;
    VerticalAlignment verticalAlignment = VerticalAlignment::Top;
    
    // Text appearance
    Color textColor = Color::Black();
    Color shadowColor = Color::Transparent();
    Vector2 shadowOffset = Vector2(0.0f, 0.0f);
    
    // Text wrapping and overflow
    bool wordWrap = false;
    bool autoSize = false;        // Automatically resize to fit text
    
    enum class OverflowMode {
        Visible,    // Text extends beyond bounds
        Hidden,     // Text is clipped
        Ellipsis    // Show "..." when text is too long
    };
    
    OverflowMode overflowMode = OverflowMode::Hidden;
    
    // Line spacing
    float lineSpacing = 1.0f;     // Multiplier for line height
    float characterSpacing = 0.0f; // Additional spacing between characters
    
    // Rich text support
    bool richText = false;        // Enable markup parsing
    
    UIText() = default;
    UIText(const std::string& txt) : text(txt) {}
    UIText(const std::string& txt, float size) : text(txt), fontSize(size) {}
    
    // Helper methods
    void SetText(const std::string& newText) { text = newText; }
    void SetFontSize(float size) { fontSize = size; }
    void SetColor(const Color& color) { textColor = color; }
    void SetAlignment(TextAlignment horizontal, VerticalAlignment vertical = VerticalAlignment::Top) {
        horizontalAlignment = horizontal;
        verticalAlignment = vertical;
    }
};

// ============================================================================
// Image Components
// ============================================================================

// UI Image Component - Handles image/sprite rendering
struct UIImage {
    // Image source
    std::string imagePath;
    std::string spriteName;       // For sprite atlases
    
    // Image properties
    Color tintColor = Color::White();
    
    // Image scaling and fitting
    enum class ScaleMode {
        StretchToFill,    // Stretch to fill the entire area
        ScaleAndCrop,     // Scale uniformly and crop if necessary
        ScaleToFit,       // Scale uniformly to fit entirely within area
        Center,           // Center image at original size
        Tile              // Tile the image
    };
    
    ScaleMode scaleMode = ScaleMode::StretchToFill;
    
    // UV coordinates for texture sampling
    Rect uvRect = Rect(0.0f, 0.0f, 1.0f, 1.0f);
    
    // Tiling properties (for Tile mode)
    Vector2 tileSize = Vector2(1.0f, 1.0f);
    Vector2 tileOffset = Vector2(0.0f, 0.0f);
    
    // Image effects
    bool preserveAspect = true;
    float rotation = 0.0f;        // Rotation in degrees
    bool flipHorizontal = false;
    bool flipVertical = false;
    
    UIImage() = default;
    UIImage(const std::string& path) : imagePath(path) {}
    UIImage(const std::string& path, const Color& tint) : imagePath(path), tintColor(tint) {}
    
    // Helper methods
    void SetImage(const std::string& path) { imagePath = path; }
    void SetTint(const Color& tint) { tintColor = tint; }
    void SetScaleMode(ScaleMode mode) { scaleMode = mode; }
    void SetUVRect(const Rect& rect) { uvRect = rect; }
};

// ============================================================================
// Input Field Components
// ============================================================================

// UI Input Field Component - Handles text input
struct UIInputField {
    // Input content
    std::string text;
    std::string placeholder = "Enter text...";
    
    // Input properties
    bool readOnly = false;
    bool multiline = false;
    int maxLength = 0;            // 0 = unlimited
    
    // Input validation
    enum class InputType {
        Text,           // Any text
        Number,         // Numbers only
        Email,          // Email format
        Password,       // Hidden text
        URL,            // URL format
        Custom          // Custom validation function
    };
    
    InputType inputType = InputType::Text;
    std::function<bool(const std::string&)> customValidator;
    
    // Cursor and selection
    int cursorPosition = 0;
    int selectionStart = 0;
    int selectionEnd = 0;
    bool hasFocus = false;
    
    // Visual properties
    Color cursorColor = Color::Black();
    Color selectionColor = Color(0.3f, 0.6f, 1.0f, 0.5f);
    float cursorBlinkRate = 1.0f; // Blinks per second
    
    // Input events
    std::function<void(const std::string&)> onTextChanged;
    std::function<void(const std::string&)> onSubmit;
    std::function<void()> onFocusGained;
    std::function<void()> onFocusLost;
    
    UIInputField() = default;
    UIInputField(const std::string& placeholderText) : placeholder(placeholderText) {}
    
    // Helper methods
    void SetText(const std::string& newText);
    void InsertText(const std::string& insertText);
    void DeleteSelection();
    void SetCursorPosition(int position);
    void SetSelection(int start, int end);
    void SelectAll();
    bool HasSelection() const { return selectionStart != selectionEnd; }
    std::string GetSelectedText() const;
};

// ============================================================================
// Progress and Slider Components
// ============================================================================

// UI Progress Bar Component
struct UIProgressBar {
    // Progress value (0.0 to 1.0)
    float value = 0.0f;
    float minValue = 0.0f;
    float maxValue = 1.0f;
    
    // Visual properties
    Color fillColor = Color::Green();
    Color backgroundColor = Color(0.2f, 0.2f, 0.2f, 1.0f);
    
    // Progress bar style
    enum class FillDirection {
        LeftToRight,
        RightToLeft,
        BottomToTop,
        TopToBottom
    };
    
    FillDirection fillDirection = FillDirection::LeftToRight;
    
    // Animation
    bool animateChanges = true;
    float animationSpeed = 2.0f;  // Units per second
    
    UIProgressBar() = default;
    UIProgressBar(float val) : value(val) {}
    
    // Helper methods
    void SetValue(float val);
    void SetRange(float min, float max);
    float GetNormalizedValue() const;
};

// UI Slider Component
struct UISlider {
    // Slider value
    float value = 0.0f;
    float minValue = 0.0f;
    float maxValue = 1.0f;
    float step = 0.0f;            // 0 = continuous, >0 = discrete steps
    
    // Visual properties
    Color handleColor = Color::White();
    Color trackColor = Color(0.3f, 0.3f, 0.3f, 1.0f);
    Color fillColor = Color::Blue();
    
    // Slider style
    enum class Direction {
        Horizontal,
        Vertical
    };
    
    Direction direction = Direction::Horizontal;
    
    // Handle properties
    float handleSize = 20.0f;     // Size of the draggable handle
    bool showFill = true;         // Show filled portion of track
    
    // Interaction
    bool isDragging = false;
    Vector2 dragStartPosition;
    
    // Events
    std::function<void(float)> onValueChanged;
    std::function<void(float)> onDragStart;
    std::function<void(float)> onDragEnd;
    
    UISlider() = default;
    UISlider(float val, float min, float max) : value(val), minValue(min), maxValue(max) {}
    
    // Helper methods
    void SetValue(float val);
    void SetRange(float min, float max);
    float GetNormalizedValue() const;
    void SetNormalizedValue(float normalizedVal);
};

} // namespace GUI
} // namespace Engine
