//
// UIManager.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of the GUI manager
//

#include "UIManager.h"
#include <iostream>
#include <chrono>

namespace Engine {
namespace GUI {

// ============================================================================
// Static Member Initialization
// ============================================================================

UIManager* UIManager::s_instance = nullptr;

// ============================================================================
// UICanvas Implementation
// ============================================================================

UICanvas::UICanvas(const std::string& name) : m_name(name), m_size(1920.0f, 1080.0f) {
}

UICanvas::~UICanvas() {
    ClearEntities();
}

void UICanvas::SetSize(const Vector2& size) {
    m_size = size;
}

void UICanvas::AddEntity(Entity entity) {
    auto it = std::find(m_entities.begin(), m_entities.end(), entity);
    if (it == m_entities.end()) {
        m_entities.push_back(entity);
    }
}

void UICanvas::RemoveEntity(Entity entity) {
    auto it = std::find(m_entities.begin(), m_entities.end(), entity);
    if (it != m_entities.end()) {
        m_entities.erase(it);
    }
}

void UICanvas::ClearEntities() {
    m_entities.clear();
}

Vector2 UICanvas::ScreenToCanvas(const Vector2& screenPos) const {
    // Simple 1:1 mapping for now
    return screenPos;
}

Vector2 UICanvas::CanvasToScreen(const Vector2& canvasPos) const {
    // Simple 1:1 mapping for now
    return canvasPos;
}

// ============================================================================
// UIManager Implementation
// ============================================================================

UIManager::UIManager() {
    // Initialize default configuration
    m_config = UIConfig();
}

UIManager::~UIManager() {
    if (m_initialized) {
        Shutdown();
    }
}

bool UIManager::Initialize(ECS* ecs, Input::InputSystem* inputSystem) {
    if (m_initialized) {
        std::cout << "⚠️  UIManager already initialized" << std::endl;
        return true;
    }
    
    if (!ecs) {
        std::cerr << "❌ Cannot initialize UIManager with null ECS" << std::endl;
        return false;
    }
    
    if (!inputSystem) {
        std::cerr << "❌ Cannot initialize UIManager with null InputSystem" << std::endl;
        return false;
    }
    
    m_ecs = ecs;
    m_inputSystemRef = inputSystem;
    
    // Register UI components with ECS
    RegisterUIComponents();
    
    // Initialize widget factory
    UIWidgetFactory::Initialize(ecs);
    
    // Initialize systems
    if (!InitializeSystems()) {
        std::cerr << "❌ Failed to initialize UI systems" << std::endl;
        return false;
    }
    
    // Create default canvas
    CreateCanvas("MainCanvas");
    SetActiveCanvas("MainCanvas");
    
    // Setup default styles
    SetupDefaultStyles();
    
    m_initialized = true;
    std::cout << "✅ UIManager initialized" << std::endl;
    
    return true;
}

void UIManager::Shutdown() {
    if (!m_initialized) return;
    
    // Shutdown systems
    ShutdownSystems();
    
    // Shutdown widget factory
    UIWidgetFactory::Shutdown();
    
    // Clear canvases
    m_canvases.clear();
    m_activeCanvas = nullptr;
    
    // Clear references
    m_ecs = nullptr;
    m_inputSystemRef = nullptr;
    
    m_initialized = false;
    std::cout << "🔧 UIManager shutdown complete" << std::endl;
}

bool UIManager::InitializeSystems() {
    // Create and initialize layout system
    m_layoutSystem = std::make_unique<UILayoutSystem>();
    m_layoutSystem->Initialize();
    m_layoutSystem->SetCanvasSize(m_config.canvasSize);
    m_layoutSystem->SetScreenSize(m_config.screenSize);
    
    // Create and initialize input system
    m_inputSystem = std::make_unique<UIInputSystem>();
    m_inputSystem->Initialize();
    m_inputSystem->SetInputSystem(m_inputSystemRef);
    m_inputSystem->SetDoubleClickTime(m_config.doubleClickTime);
    m_inputSystem->SetDragThreshold(m_config.dragThreshold);
    
    // Create and initialize render system
    m_renderSystem = std::make_unique<UIRenderSystem>();
    m_renderSystem->Initialize();
    m_renderSystem->SetCanvasSize(m_config.canvasSize);
    m_renderSystem->SetScreenSize(m_config.screenSize);
    
    // Register systems with ECS if needed
    if (m_ecs) {
        // Note: These systems don't need to be registered with ECS as they're managed by UIManager
        // But we could register them if we want them to be part of the main update loop
    }
    
    return true;
}

void UIManager::ShutdownSystems() {
    if (m_layoutSystem) {
        m_layoutSystem->Shutdown();
        m_layoutSystem.reset();
    }
    
    if (m_inputSystem) {
        m_inputSystem->Shutdown();
        m_inputSystem.reset();
    }
    
    if (m_renderSystem) {
        m_renderSystem->Shutdown();
        m_renderSystem.reset();
    }
}

void UIManager::RegisterUIComponents() {
    if (!m_ecs) return;
    
    // Register core UI components
    m_ecs->RegisterComponent<UITransform>();
    m_ecs->RegisterComponent<UIRenderer>();
    m_ecs->RegisterComponent<UIInteractable>();
    m_ecs->RegisterComponent<UILayout>();
    
    // Register text components
    m_ecs->RegisterComponent<UIText>();
    m_ecs->RegisterComponent<UIImage>();
    m_ecs->RegisterComponent<UIInputField>();
    m_ecs->RegisterComponent<UIProgressBar>();
    m_ecs->RegisterComponent<UISlider>();
    
    // Register widget components
    m_ecs->RegisterComponent<UIButton>();
    m_ecs->RegisterComponent<UIPanel>();
    m_ecs->RegisterComponent<UIListItem>();
    m_ecs->RegisterComponent<UIList>();
    m_ecs->RegisterComponent<UIDropdown>();
    m_ecs->RegisterComponent<UITab>();
    m_ecs->RegisterComponent<UITabGroup>();
    m_ecs->RegisterComponent<UITooltip>();
    
    std::cout << "✅ UI components registered with ECS" << std::endl;
}

void UIManager::SetupDefaultStyles() {
    // Setup default widget factory styles
    UIWidgetFactory::SetDefaultButtonStyle(
        Color(0.8f, 0.8f, 0.8f, 1.0f),  // Normal
        Color(0.9f, 0.9f, 0.9f, 1.0f),  // Hover
        Color(0.7f, 0.7f, 0.7f, 1.0f)   // Pressed
    );
    
    UIWidgetFactory::SetDefaultTextStyle(
        m_config.defaultFont,
        m_config.defaultFontSize,
        m_config.defaultTextColor
    );
    
    UIWidgetFactory::SetDefaultPanelStyle(
        m_config.defaultBackgroundColor,
        Color::Black(),
        1.0f
    );
}

void UIManager::Update(DeltaTime deltaTime) {
    if (!m_initialized) return;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Update systems
    UpdateSystems(deltaTime);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    m_stats.updateTime = std::chrono::duration<float>(endTime - startTime).count();
}

void UIManager::UpdateSystems(DeltaTime deltaTime) {
    // Update layout system
    if (m_layoutSystem) {
        m_layoutSystem->Update(deltaTime);
    }
    
    // Update input system
    if (m_inputSystem) {
        m_inputSystem->Update(deltaTime);
    }
    
    // Update render system
    if (m_renderSystem) {
        m_renderSystem->Update(deltaTime);
    }
    
    // Update statistics
    if (m_ecs) {
        m_stats.totalEntities = static_cast<int>(m_ecs->GetEntitiesWithComponents<UITransform>().size());
        m_stats.visibleEntities = static_cast<int>(m_ecs->GetEntitiesWithComponents<UITransform, UIRenderer>().size());
        m_stats.interactableEntities = static_cast<int>(m_ecs->GetEntitiesWithComponents<UIInteractable>().size());
    }
}

void UIManager::Render(id<MTLCommandBuffer> commandBuffer) {
    if (!m_initialized || !m_renderSystem) return;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Render UI
    m_renderSystem->Render(commandBuffer);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    m_stats.renderTime = std::chrono::duration<float>(endTime - startTime).count();
}

void UIManager::SetMetalDevice(id<MTLDevice> device) {
    if (m_renderSystem) {
        m_renderSystem->SetMetalDevice(device);
    }
}

void UIManager::SetCommandQueue(id<MTLCommandQueue> commandQueue) {
    if (m_renderSystem) {
        m_renderSystem->SetCommandQueue(commandQueue);
    }
}

void UIManager::SetRenderPassDescriptor(MTLRenderPassDescriptor* renderPassDescriptor) {
    if (m_renderSystem) {
        m_renderSystem->SetRenderPassDescriptor(renderPassDescriptor);
    }
}

// ============================================================================
// Canvas Management
// ============================================================================

UICanvas* UIManager::CreateCanvas(const std::string& name) {
    auto it = m_canvases.find(name);
    if (it != m_canvases.end()) {
        std::cout << "⚠️  Canvas '" << name << "' already exists" << std::endl;
        return it->second.get();
    }
    
    auto canvas = std::make_unique<UICanvas>(name);
    canvas->SetSize(m_config.canvasSize);
    
    UICanvas* canvasPtr = canvas.get();
    m_canvases[name] = std::move(canvas);
    
    std::cout << "✅ Created canvas: " << name << std::endl;
    return canvasPtr;
}

UICanvas* UIManager::GetCanvas(const std::string& name) {
    auto it = m_canvases.find(name);
    return (it != m_canvases.end()) ? it->second.get() : nullptr;
}

void UIManager::DestroyCanvas(const std::string& name) {
    auto it = m_canvases.find(name);
    if (it != m_canvases.end()) {
        if (m_activeCanvas == it->second.get()) {
            m_activeCanvas = nullptr;
        }
        m_canvases.erase(it);
        std::cout << "🗑️  Destroyed canvas: " << name << std::endl;
    }
}

void UIManager::SetActiveCanvas(const std::string& name) {
    UICanvas* canvas = GetCanvas(name);
    if (canvas) {
        m_activeCanvas = canvas;
        std::cout << "🎯 Set active canvas: " << name << std::endl;
    } else {
        std::cerr << "❌ Cannot set active canvas - canvas not found: " << name << std::endl;
    }
}

// ============================================================================
// Configuration
// ============================================================================

void UIManager::SetConfig(const UIConfig& config) {
    m_config = config;
    
    // Update systems with new configuration
    if (m_layoutSystem) {
        m_layoutSystem->SetCanvasSize(config.canvasSize);
        m_layoutSystem->SetScreenSize(config.screenSize);
        m_layoutSystem->SetDebugMode(config.enableLayoutDebug);
    }
    
    if (m_inputSystem) {
        m_inputSystem->SetDoubleClickTime(config.doubleClickTime);
        m_inputSystem->SetDragThreshold(config.dragThreshold);
        m_inputSystem->SetDebugMode(config.enableInputDebug);
    }
    
    if (m_renderSystem) {
        m_renderSystem->SetCanvasSize(config.canvasSize);
        m_renderSystem->SetScreenSize(config.screenSize);
        m_renderSystem->SetDebugMode(config.enableDebugRendering);
    }
    
    // Update canvas sizes
    for (auto& pair : m_canvases) {
        pair.second->SetSize(config.canvasSize);
    }
}

void UIManager::SetScreenSize(const Vector2& size) {
    m_config.screenSize = size;
    
    if (m_layoutSystem) {
        m_layoutSystem->SetScreenSize(size);
    }
    
    if (m_renderSystem) {
        m_renderSystem->SetScreenSize(size);
    }
}

void UIManager::SetCanvasSize(const Vector2& size) {
    m_config.canvasSize = size;
    
    if (m_layoutSystem) {
        m_layoutSystem->SetCanvasSize(size);
    }
    
    if (m_renderSystem) {
        m_renderSystem->SetCanvasSize(size);
    }
    
    // Update all canvases
    for (auto& pair : m_canvases) {
        pair.second->SetSize(size);
    }
}

// ============================================================================
// Singleton Management
// ============================================================================

UIManager& UIManager::GetInstance() {
    if (!s_instance) {
        static UIManager instance;
        s_instance = &instance;
    }
    return *s_instance;
}

void UIManager::SetInstance(UIManager* instance) {
    s_instance = instance;
}

void UIManager::ResetStats() {
    m_stats = UIStats();
}

// ============================================================================
// High-Level UI Creation Methods
// ============================================================================

Entity UIManager::CreateUI(const std::string& canvasName) {
    if (!m_ecs) return INVALID_ENTITY;

    Entity entity = m_ecs->CreateEntity();
    SetupEntityDefaults(entity);
    AddEntityToCanvas(entity, canvasName);

    return entity;
}

Entity UIManager::CreateButton(const std::string& text, const Vector2& position, const Vector2& size,
                              std::function<void()> onClick, const std::string& canvasName) {
    Entity button = UIWidgetFactory::CreateButton(text, position, size, onClick);
    if (button != INVALID_ENTITY) {
        AddEntityToCanvas(button, canvasName);
    }
    return button;
}

Entity UIManager::CreateLabel(const std::string& text, const Vector2& position, float fontSize,
                             const Color& color, const std::string& canvasName) {
    Entity label = UIWidgetFactory::CreateLabel(text, position, fontSize, color);
    if (label != INVALID_ENTITY) {
        AddEntityToCanvas(label, canvasName);
    }
    return label;
}

Entity UIManager::CreatePanel(const Vector2& position, const Vector2& size, const Color& color,
                             const std::string& canvasName) {
    Entity panel = UIWidgetFactory::CreatePanel(position, size, color);
    if (panel != INVALID_ENTITY) {
        AddEntityToCanvas(panel, canvasName);
    }
    return panel;
}

Entity UIManager::CreateTextField(const Vector2& position, const Vector2& size, const std::string& placeholder,
                                 const std::string& canvasName) {
    Entity textField = UIWidgetFactory::CreateTextField(position, size, placeholder);
    if (textField != INVALID_ENTITY) {
        AddEntityToCanvas(textField, canvasName);
    }
    return textField;
}

Entity UIManager::CreateSlider(const Vector2& position, const Vector2& size, float minValue, float maxValue,
                              float initialValue, const std::string& canvasName) {
    Entity slider = UIWidgetFactory::CreateSlider(position, size, minValue, maxValue, initialValue);
    if (slider != INVALID_ENTITY) {
        AddEntityToCanvas(slider, canvasName);
    }
    return slider;
}

Entity UIManager::CreateHorizontalLayout(const Vector2& position, const Vector2& size, float spacing,
                                        const std::string& canvasName) {
    Entity layout = CreateUI(canvasName);
    if (layout != INVALID_ENTITY && m_ecs) {
        // Set up transform
        auto& transform = m_ecs->GetComponent<UITransform>(layout);
        transform.position = position;
        transform.size = size;
        transform.worldPosition = position;
        transform.worldSize = size;
        transform.worldRect = Rect(position.x, position.y, size.x, size.y);

        // Set up layout group
        if (m_layoutSystem) {
            LayoutGroup group = CreateHorizontalLayoutGroup(spacing);
            m_layoutSystem->RegisterLayoutGroup(layout, group);
        }
    }
    return layout;
}

Entity UIManager::CreateVerticalLayout(const Vector2& position, const Vector2& size, float spacing,
                                      const std::string& canvasName) {
    Entity layout = CreateUI(canvasName);
    if (layout != INVALID_ENTITY && m_ecs) {
        // Set up transform
        auto& transform = m_ecs->GetComponent<UITransform>(layout);
        transform.position = position;
        transform.size = size;
        transform.worldPosition = position;
        transform.worldSize = size;
        transform.worldRect = Rect(position.x, position.y, size.x, size.y);

        // Set up layout group
        if (m_layoutSystem) {
            LayoutGroup group = CreateVerticalLayoutGroup(spacing);
            m_layoutSystem->RegisterLayoutGroup(layout, group);
        }
    }
    return layout;
}

Entity UIManager::CreateGridLayout(const Vector2& position, const Vector2& size, int columns, int rows,
                                  const Vector2& spacing, const std::string& canvasName) {
    Entity layout = CreateUI(canvasName);
    if (layout != INVALID_ENTITY && m_ecs) {
        // Set up transform
        auto& transform = m_ecs->GetComponent<UITransform>(layout);
        transform.position = position;
        transform.size = size;
        transform.worldPosition = position;
        transform.worldSize = size;
        transform.worldRect = Rect(position.x, position.y, size.x, size.y);

        // Set up layout group
        if (m_layoutSystem) {
            LayoutGroup group = CreateGridLayoutGroup(columns, rows, spacing);
            m_layoutSystem->RegisterLayoutGroup(layout, group);
        }
    }
    return layout;
}

// ============================================================================
// Helper Methods
// ============================================================================

void UIManager::AddEntityToCanvas(Entity entity, const std::string& canvasName) {
    UICanvas* canvas = GetCanvas(canvasName);
    if (!canvas) {
        canvas = CreateCanvas(canvasName);
    }

    if (canvas) {
        canvas->AddEntity(entity);
    }
}

void UIManager::SetupEntityDefaults(Entity entity) {
    if (!m_ecs) return;

    // Add basic UI components if not already present
    if (!m_ecs->HasComponent<UITransform>(entity)) {
        m_ecs->AddComponent(entity, UITransform());
    }

    if (!m_ecs->HasComponent<UIRenderer>(entity)) {
        m_ecs->AddComponent(entity, UIRenderer());
    }
}

void UIManager::SetDebugMode(bool enabled) {
    m_debugMode = enabled;

    if (m_layoutSystem) {
        m_layoutSystem->SetDebugMode(enabled);
    }

    if (m_inputSystem) {
        m_inputSystem->SetDebugMode(enabled);
    }

    if (m_renderSystem) {
        m_renderSystem->SetDebugMode(enabled);
    }
}

void UIManager::RegisterGlobalEventHandler(std::function<void(const UIInputEvent&)> handler) {
    m_globalEventHandler = handler;
}

void UIManager::UnregisterGlobalEventHandler() {
    m_globalEventHandler = nullptr;
}

// ============================================================================
// Convenience Functions
// ============================================================================

Entity CreateButton(const std::string& text, float x, float y, float width, float height,
                   std::function<void()> onClick) {
    return UIManager::GetInstance().CreateButton(text, Vector2(x, y), Vector2(width, height), onClick);
}

Entity CreateLabel(const std::string& text, float x, float y, float fontSize) {
    return UIManager::GetInstance().CreateLabel(text, Vector2(x, y), fontSize);
}

Entity CreatePanel(float x, float y, float width, float height, const Color& color) {
    return UIManager::GetInstance().CreatePanel(Vector2(x, y), Vector2(width, height), color);
}

Entity CreateTextField(float x, float y, float width, float height, const std::string& placeholder) {
    return UIManager::GetInstance().CreateTextField(Vector2(x, y), Vector2(width, height), placeholder);
}

Entity CreateSlider(float x, float y, float width, float height, float minValue,
                   float maxValue, float initialValue) {
    return UIManager::GetInstance().CreateSlider(Vector2(x, y), Vector2(width, height), minValue, maxValue, initialValue);
}

Entity CreateHorizontalLayout(float x, float y, float width, float height, float spacing) {
    return UIManager::GetInstance().CreateHorizontalLayout(Vector2(x, y), Vector2(width, height), spacing);
}

Entity CreateVerticalLayout(float x, float y, float width, float height, float spacing) {
    return UIManager::GetInstance().CreateVerticalLayout(Vector2(x, y), Vector2(width, height), spacing);
}

Entity CreateGridLayout(float x, float y, float width, float height, int columns, int rows) {
    return UIManager::GetInstance().CreateGridLayout(Vector2(x, y), Vector2(width, height), columns, rows);
}

} // namespace GUI
} // namespace Engine
