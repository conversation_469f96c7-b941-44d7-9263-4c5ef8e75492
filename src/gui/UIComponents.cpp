//
// UIComponents.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of core GUI components
//

#include "UIComponents.h"
#include <algorithm>
#include <iostream>

namespace Engine {
namespace GUI {

// ============================================================================
// UITransform Implementation
// ============================================================================

void UITransform::SetAnchor(AnchorType anchor) {
    switch (anchor) {
        case AnchorType::TopLeft:
            anchorMin = Vector2(0.0f, 0.0f);
            anchorMax = Vector2(0.0f, 0.0f);
            pivot = Vector2(0.0f, 0.0f);
            break;
            
        case AnchorType::TopCenter:
            anchorMin = Vector2(0.5f, 0.0f);
            anchorMax = Vector2(0.5f, 0.0f);
            pivot = Vector2(0.5f, 0.0f);
            break;
            
        case AnchorType::TopRight:
            anchorMin = Vector2(1.0f, 0.0f);
            anchorMax = Vector2(1.0f, 0.0f);
            pivot = Vector2(1.0f, 0.0f);
            break;
            
        case AnchorType::MiddleLeft:
            anchorMin = Vector2(0.0f, 0.5f);
            anchorMax = Vector2(0.0f, 0.5f);
            pivot = Vector2(0.0f, 0.5f);
            break;
            
        case AnchorType::MiddleCenter:
            anchorMin = Vector2(0.5f, 0.5f);
            anchorMax = Vector2(0.5f, 0.5f);
            pivot = Vector2(0.5f, 0.5f);
            break;
            
        case AnchorType::MiddleRight:
            anchorMin = Vector2(1.0f, 0.5f);
            anchorMax = Vector2(1.0f, 0.5f);
            pivot = Vector2(1.0f, 0.5f);
            break;
            
        case AnchorType::BottomLeft:
            anchorMin = Vector2(0.0f, 1.0f);
            anchorMax = Vector2(0.0f, 1.0f);
            pivot = Vector2(0.0f, 1.0f);
            break;
            
        case AnchorType::BottomCenter:
            anchorMin = Vector2(0.5f, 1.0f);
            anchorMax = Vector2(0.5f, 1.0f);
            pivot = Vector2(0.5f, 1.0f);
            break;
            
        case AnchorType::BottomRight:
            anchorMin = Vector2(1.0f, 1.0f);
            anchorMax = Vector2(1.0f, 1.0f);
            pivot = Vector2(1.0f, 1.0f);
            break;
            
        case AnchorType::Stretch:
            anchorMin = Vector2(0.0f, 0.0f);
            anchorMax = Vector2(1.0f, 1.0f);
            pivot = Vector2(0.5f, 0.5f);
            break;
            
        case AnchorType::Custom:
            // Don't change anything for custom anchors
            break;
    }
}

void UITransform::AddChild(Entity child) {
    // Check if child is already in the list
    auto it = std::find(children.begin(), children.end(), child);
    if (it == children.end()) {
        children.push_back(child);
    }
}

void UITransform::RemoveChild(Entity child) {
    auto it = std::find(children.begin(), children.end(), child);
    if (it != children.end()) {
        children.erase(it);
    }
}

bool UITransform::IsChildOf(Entity potentialParent) const {
    Entity currentParent = parent;
    while (currentParent != INVALID_ENTITY) {
        if (currentParent == potentialParent) {
            return true;
        }
        // Note: To fully implement this, we'd need access to the ECS
        // to get the parent's UITransform component. For now, we'll
        // just check the immediate parent.
        break;
    }
    return false;
}

// ============================================================================
// UIInteractable Implementation
// ============================================================================

void UIInteractable::SetState(UIState newState) {
    if (currentState != newState) {
        previousState = currentState;
        currentState = newState;
        
        // Trigger appropriate callbacks based on state change
        switch (newState) {
            case UIState::Hovered:
                if (previousState != UIState::Hovered && onHover) {
                    // We'd need the entity ID here, but this is just the component
                    // The actual callback triggering will be handled by the UI system
                }
                break;
                
            case UIState::Normal:
                if (previousState == UIState::Hovered && onUnhover) {
                    // Same as above - handled by UI system
                }
                break;
                
            case UIState::Pressed:
                if (onPress) {
                    // Handled by UI system
                }
                break;
                
            default:
                break;
        }
    }
}

} // namespace GUI
} // namespace Engine
