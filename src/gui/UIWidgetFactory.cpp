//
// UIWidgetFactory.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of the GUI widget factory
//

#include "UIWidgetFactory.h"
#include <iostream>

namespace Engine {
namespace GUI {

// ============================================================================
// Static Member Initialization
// ============================================================================

ECS* UIWidgetFactory::s_ecs = nullptr;
UIWidgetFactory::DefaultStyles UIWidgetFactory::s_defaultStyles;
std::unordered_map<std::string, std::function<Entity(const Vector2&, const Vector2&)>> UIWidgetFactory::s_customWidgets;
bool UIWidgetFactory::s_initialized = false;

// ============================================================================
// Factory Initialization
// ============================================================================

void UIWidgetFactory::Initialize(ECS* ecs) {
    if (s_initialized) {
        std::cout << "⚠️  UIWidgetFactory already initialized" << std::endl;
        return;
    }
    
    if (!ecs) {
        std::cerr << "❌ Cannot initialize UIWidgetFactory with null ECS" << std::endl;
        return;
    }
    
    s_ecs = ecs;
    s_initialized = true;
    
    std::cout << "✅ UIWidgetFactory initialized" << std::endl;
}

void UIWidgetFactory::Shutdown() {
    if (!s_initialized) return;
    
    s_ecs = nullptr;
    s_customWidgets.clear();
    s_initialized = false;
    
    std::cout << "🔧 UIWidgetFactory shutdown complete" << std::endl;
}

UIWidgetFactory& UIWidgetFactory::GetInstance() {
    static UIWidgetFactory instance;
    return instance;
}

// ============================================================================
// Basic Widget Creation
// ============================================================================

Entity UIWidgetFactory::CreatePanel(const Vector2& position, const Vector2& size, const Color& backgroundColor) {
    if (!s_initialized || !s_ecs) {
        std::cerr << "❌ UIWidgetFactory not initialized" << std::endl;
        return INVALID_ENTITY;
    }
    
    Entity panel = CreateBaseWidget(position, size);
    
    // Add panel-specific components
    s_ecs->AddComponent(panel, UIPanel(UIPanel::PanelType::Solid));
    
    // Set up renderer with background color
    auto& renderer = s_ecs->GetComponent<UIRenderer>(panel);
    renderer.backgroundColor = backgroundColor;
    renderer.color = backgroundColor;
    
    return panel;
}

Entity UIWidgetFactory::CreateLabel(const std::string& text, const Vector2& position, 
                                   float fontSize, const Color& textColor) {
    if (!s_initialized || !s_ecs) {
        std::cerr << "❌ UIWidgetFactory not initialized" << std::endl;
        return INVALID_ENTITY;
    }
    
    Entity label = CreateBaseWidget(position, Vector2(100.0f, 30.0f)); // Default size, will auto-size
    
    // Add text component
    UIText textComponent(text, fontSize);
    textComponent.textColor = textColor;
    textComponent.autoSize = true;
    s_ecs->AddComponent(label, textComponent);
    
    // Set up renderer for text
    auto& renderer = s_ecs->GetComponent<UIRenderer>(label);
    renderer.color = textColor;
    renderer.backgroundColor = Color::Transparent();
    
    return label;
}

Entity UIWidgetFactory::CreateButton(const std::string& text, const Vector2& position, const Vector2& size,
                                    std::function<void()> onClick) {
    if (!s_initialized || !s_ecs) {
        std::cerr << "❌ UIWidgetFactory not initialized" << std::endl;
        return INVALID_ENTITY;
    }
    
    Entity button = CreateBaseWidget(position, size);
    
    // Add button component
    UIButton buttonComponent;
    buttonComponent.SetColors(s_defaultStyles.buttonNormalColor, 
                             s_defaultStyles.buttonHoverColor, 
                             s_defaultStyles.buttonPressedColor);
    if (onClick) {
        buttonComponent.onClick = onClick;
    }
    s_ecs->AddComponent(button, buttonComponent);
    
    // Add text component
    UIText textComponent(text);
    textComponent.horizontalAlignment = UIText::TextAlignment::Center;
    textComponent.verticalAlignment = UIText::VerticalAlignment::Middle;
    textComponent.textColor = s_defaultStyles.defaultTextColor;
    s_ecs->AddComponent(button, textComponent);
    
    // Set up interactable
    SetupInteractable(button, onClick);
    
    return button;
}

Entity UIWidgetFactory::CreateImage(const std::string& imagePath, const Vector2& position, const Vector2& size,
                                   const Color& tintColor) {
    if (!s_initialized || !s_ecs) {
        std::cerr << "❌ UIWidgetFactory not initialized" << std::endl;
        return INVALID_ENTITY;
    }
    
    Entity image = CreateBaseWidget(position, size);
    
    // Add image component
    UIImage imageComponent(imagePath, tintColor);
    s_ecs->AddComponent(image, imageComponent);
    
    // Set up renderer
    auto& renderer = s_ecs->GetComponent<UIRenderer>(image);
    renderer.texturePath = imagePath;
    renderer.color = tintColor;
    
    return image;
}

Entity UIWidgetFactory::CreateTextField(const Vector2& position, const Vector2& size,
                                       const std::string& placeholder) {
    if (!s_initialized || !s_ecs) {
        std::cerr << "❌ UIWidgetFactory not initialized" << std::endl;
        return INVALID_ENTITY;
    }
    
    Entity textField = CreateBaseWidget(position, size);
    
    // Add input field component
    UIInputField inputComponent(placeholder);
    s_ecs->AddComponent(textField, inputComponent);
    
    // Add text component for display
    UIText textComponent;
    textComponent.text = placeholder;
    textComponent.textColor = Color(0.6f, 0.6f, 0.6f, 1.0f); // Gray for placeholder
    textComponent.verticalAlignment = UIText::VerticalAlignment::Middle;
    s_ecs->AddComponent(textField, textComponent);
    
    // Set up renderer with input field styling
    auto& renderer = s_ecs->GetComponent<UIRenderer>(textField);
    renderer.backgroundColor = s_defaultStyles.inputFieldBackgroundColor;
    renderer.hasBorder = true;
    renderer.borderColor = s_defaultStyles.inputFieldBorderColor;
    renderer.borderWidth = s_defaultStyles.defaultBorderWidth;
    
    // Set up interactable for focus handling
    SetupInteractable(textField);
    
    return textField;
}

Entity UIWidgetFactory::CreateSlider(const Vector2& position, const Vector2& size,
                                     float minValue, float maxValue, float initialValue) {
    if (!s_initialized || !s_ecs) {
        std::cerr << "❌ UIWidgetFactory not initialized" << std::endl;
        return INVALID_ENTITY;
    }
    
    Entity slider = CreateBaseWidget(position, size);
    
    // Add slider component
    UISlider sliderComponent(initialValue, minValue, maxValue);
    sliderComponent.trackColor = s_defaultStyles.sliderTrackColor;
    sliderComponent.fillColor = s_defaultStyles.sliderFillColor;
    sliderComponent.handleColor = s_defaultStyles.sliderHandleColor;
    s_ecs->AddComponent(slider, sliderComponent);
    
    // Set up interactable for dragging
    SetupInteractable(slider);
    
    return slider;
}

Entity UIWidgetFactory::CreateProgressBar(const Vector2& position, const Vector2& size, float initialValue) {
    if (!s_initialized || !s_ecs) {
        std::cerr << "❌ UIWidgetFactory not initialized" << std::endl;
        return INVALID_ENTITY;
    }
    
    Entity progressBar = CreateBaseWidget(position, size);
    
    // Add progress bar component
    UIProgressBar progressComponent(initialValue);
    s_ecs->AddComponent(progressBar, progressComponent);
    
    // Set up renderer for background
    auto& renderer = s_ecs->GetComponent<UIRenderer>(progressBar);
    renderer.backgroundColor = progressComponent.backgroundColor;
    
    return progressBar;
}

// ============================================================================
// Helper Methods
// ============================================================================

Entity UIWidgetFactory::CreateBaseWidget(const Vector2& position, const Vector2& size) {
    Entity widget = s_ecs->CreateEntity();
    
    // Add basic transform
    UITransform transform(position, size);
    s_ecs->AddComponent(widget, transform);
    
    // Add basic renderer
    UIRenderer renderer;
    s_ecs->AddComponent(widget, renderer);
    
    return widget;
}

void UIWidgetFactory::SetupInteractable(Entity entity, std::function<void()> onClick) {
    UIInteractable interactable;
    if (onClick) {
        interactable.onClick = [onClick](Entity) { onClick(); };
    }
    s_ecs->AddComponent(entity, interactable);
}

void UIWidgetFactory::SetupRenderer(Entity entity, const Color& color) {
    auto& renderer = s_ecs->GetComponent<UIRenderer>(entity);
    renderer.color = color;
}

void UIWidgetFactory::SetupText(Entity entity, const std::string& text, float fontSize) {
    UIText textComponent(text, fontSize);
    textComponent.fontName = s_defaultStyles.defaultFont;
    textComponent.textColor = s_defaultStyles.defaultTextColor;
    s_ecs->AddComponent(entity, textComponent);
}

// ============================================================================
// Style Configuration
// ============================================================================

void UIWidgetFactory::SetDefaultButtonStyle(const Color& normal, const Color& hover, const Color& pressed) {
    s_defaultStyles.buttonNormalColor = normal;
    s_defaultStyles.buttonHoverColor = hover;
    s_defaultStyles.buttonPressedColor = pressed;
}

void UIWidgetFactory::SetDefaultTextStyle(const std::string& fontName, float fontSize, const Color& color) {
    s_defaultStyles.defaultFont = fontName;
    s_defaultStyles.defaultFontSize = fontSize;
    s_defaultStyles.defaultTextColor = color;
}

void UIWidgetFactory::SetDefaultPanelStyle(const Color& backgroundColor, const Color& borderColor, float borderWidth) {
    s_defaultStyles.defaultPanelColor = backgroundColor;
    s_defaultStyles.defaultBorderColor = borderColor;
    s_defaultStyles.defaultBorderWidth = borderWidth;
}

} // namespace GUI
} // namespace Engine
