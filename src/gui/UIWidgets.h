//
// UIWidgets.h
// macOS 3D Looter-Shooter Game Engine
//
// Widget-specific GUI components for common UI elements
//

#pragma once

#include "UIComponents.h"
#include "UITextComponents.h"
#include <vector>
#include <functional>

namespace Engine {
namespace GUI {

// ============================================================================
// Button Components
// ============================================================================

// UI Button Component - Interactive button element
struct UIButton {
    // Button states and colors
    Color normalColor = Color(0.8f, 0.8f, 0.8f, 1.0f);
    Color hoverColor = Color(0.9f, 0.9f, 0.9f, 1.0f);
    Color pressedColor = Color(0.7f, 0.7f, 0.7f, 1.0f);
    Color disabledColor = Color(0.5f, 0.5f, 0.5f, 0.5f);
    
    // Button style
    enum class ButtonType {
        Normal,         // Standard button
        Toggle,         // Toggle on/off button
        Radio           // Radio button (part of group)
    };
    
    ButtonType buttonType = ButtonType::Normal;
    
    // Toggle state (for Toggle and Radio buttons)
    bool isToggled = false;
    std::string radioGroup;       // Group name for radio buttons
    
    // Visual effects
    bool useTransitions = true;
    float transitionDuration = 0.1f;
    
    // Sound effects
    std::string clickSoundPath;
    std::string hoverSoundPath;
    
    // Events
    std::function<void()> onClick;
    std::function<void(bool)> onToggle;  // For toggle buttons
    
    UIButton() = default;
    UIButton(ButtonType type) : buttonType(type) {}
    
    // Helper methods
    void SetColors(const Color& normal, const Color& hover, const Color& pressed);
    void SetToggleState(bool toggled);
    Color GetCurrentColor(UIState state) const;
};

// ============================================================================
// Panel Components
// ============================================================================

// UI Panel Component - Container/background panel
struct UIPanel {
    // Panel style
    enum class PanelType {
        Solid,          // Solid color background
        Gradient,       // Gradient background
        Image,          // Image background
        NineSlice       // Nine-slice sprite background
    };
    
    PanelType panelType = PanelType::Solid;
    
    // Gradient properties (for Gradient type)
    Color gradientStartColor = Color::White();
    Color gradientEndColor = Color::Black();
    
    enum class GradientDirection {
        Horizontal,
        Vertical,
        Diagonal,
        Radial
    };
    
    GradientDirection gradientDirection = GradientDirection::Vertical;
    
    // Nine-slice properties (for NineSlice type)
    std::string nineSliceSprite;
    Vector2 sliceBorders = Vector2(10.0f, 10.0f);  // Border sizes for slicing
    
    // Panel effects
    bool castShadow = false;
    Color shadowColor = Color(0.0f, 0.0f, 0.0f, 0.3f);
    Vector2 shadowOffset = Vector2(2.0f, 2.0f);
    float shadowBlur = 4.0f;
    
    UIPanel() = default;
    UIPanel(PanelType type) : panelType(type) {}
    UIPanel(const Color& color) : gradientStartColor(color), gradientEndColor(color) {}
};

// ============================================================================
// List and Menu Components
// ============================================================================

// UI List Item Component - Individual item in a list
struct UIListItem {
    // Item data
    std::string text;
    std::string iconPath;
    bool selectable = true;
    bool selected = false;
    
    // Visual properties
    Color normalColor = Color::Transparent();
    Color selectedColor = Color(0.3f, 0.6f, 1.0f, 0.3f);
    Color hoverColor = Color(0.5f, 0.5f, 0.5f, 0.2f);
    
    // Item events
    std::function<void()> onSelect;
    std::function<void()> onDeselect;
    std::function<void()> onDoubleClick;
    
    UIListItem() = default;
    UIListItem(const std::string& itemText) : text(itemText) {}
};

// UI List Component - Scrollable list of items
struct UIList {
    // List properties
    bool multiSelect = false;
    bool allowDeselect = true;
    std::vector<Entity> items;    // List item entities
    std::vector<Entity> selectedItems;
    
    // Scrolling
    float scrollPosition = 0.0f;
    float itemHeight = 30.0f;
    bool showScrollbar = true;
    
    // Visual properties
    Color backgroundColor = Color::White();
    Color borderColor = Color::Black();
    float borderWidth = 1.0f;
    
    // List events
    std::function<void(Entity)> onItemSelected;
    std::function<void(Entity)> onItemDeselected;
    std::function<void(const std::vector<Entity>&)> onSelectionChanged;
    
    UIList() = default;
    
    // Helper methods
    void AddItem(Entity item);
    void RemoveItem(Entity item);
    void ClearItems();
    void SelectItem(Entity item);
    void DeselectItem(Entity item);
    void ClearSelection();
    bool IsItemSelected(Entity item) const;
};

// ============================================================================
// Dropdown and Menu Components
// ============================================================================

// UI Dropdown Option Component
struct UIDropdownOption {
    std::string text;
    std::string value;
    bool enabled = true;
    std::string iconPath;
    
    UIDropdownOption() = default;
    UIDropdownOption(const std::string& txt, const std::string& val = "") 
        : text(txt), value(val.empty() ? txt : val) {}
};

// UI Dropdown Component
struct UIDropdown {
    // Dropdown options
    std::vector<UIDropdownOption> options;
    int selectedIndex = -1;
    std::string placeholder = "Select an option...";
    
    // Dropdown state
    bool isOpen = false;
    float maxDropdownHeight = 200.0f;
    
    // Visual properties
    Color dropdownColor = Color::White();
    Color optionHoverColor = Color(0.9f, 0.9f, 0.9f, 1.0f);
    Color selectedOptionColor = Color(0.3f, 0.6f, 1.0f, 0.3f);
    
    // Arrow properties
    bool showArrow = true;
    std::string arrowSprite = "dropdown_arrow";
    
    // Events
    std::function<void(int, const std::string&)> onSelectionChanged;
    std::function<void()> onDropdownOpen;
    std::function<void()> onDropdownClose;
    
    UIDropdown() = default;
    
    // Helper methods
    void AddOption(const UIDropdownOption& option);
    void RemoveOption(int index);
    void SetSelectedIndex(int index);
    void SetSelectedValue(const std::string& value);
    std::string GetSelectedText() const;
    std::string GetSelectedValue() const;
    void OpenDropdown();
    void CloseDropdown();
};

// ============================================================================
// Tab Components
// ============================================================================

// UI Tab Component - Individual tab in a tab group
struct UITab {
    std::string title;
    std::string iconPath;
    bool active = false;
    bool closable = false;
    
    // Tab content (entity containing the tab's UI elements)
    Entity contentEntity = INVALID_ENTITY;
    
    // Visual properties
    Color activeColor = Color::White();
    Color inactiveColor = Color(0.8f, 0.8f, 0.8f, 1.0f);
    Color hoverColor = Color(0.9f, 0.9f, 0.9f, 1.0f);
    
    UITab() = default;
    UITab(const std::string& tabTitle) : title(tabTitle) {}
};

// UI Tab Group Component - Container for multiple tabs
struct UITabGroup {
    std::vector<Entity> tabs;     // Tab entities
    int activeTabIndex = 0;
    
    // Tab group properties
    enum class TabPosition {
        Top,
        Bottom,
        Left,
        Right
    };
    
    TabPosition tabPosition = TabPosition::Top;
    float tabHeight = 30.0f;
    float tabMinWidth = 80.0f;
    float tabMaxWidth = 200.0f;
    
    // Visual properties
    Color tabBarColor = Color(0.9f, 0.9f, 0.9f, 1.0f);
    Color contentAreaColor = Color::White();
    
    // Events
    std::function<void(int)> onTabChanged;
    std::function<void(int)> onTabClosed;
    
    UITabGroup() = default;
    
    // Helper methods
    void AddTab(Entity tab);
    void RemoveTab(int index);
    void SetActiveTab(int index);
    void CloseTab(int index);
    Entity GetActiveTabContent() const;
};

// ============================================================================
// Tooltip Component
// ============================================================================

// UI Tooltip Component - Hover tooltip
struct UITooltip {
    std::string text;
    bool showOnHover = true;
    float showDelay = 0.5f;       // Delay before showing tooltip
    float hideDelay = 0.1f;       // Delay before hiding tooltip
    
    // Tooltip positioning
    enum class TooltipPosition {
        Auto,           // Automatically position to stay on screen
        Top,
        Bottom,
        Left,
        Right,
        TopLeft,
        TopRight,
        BottomLeft,
        BottomRight
    };
    
    TooltipPosition position = TooltipPosition::Auto;
    Vector2 offset = Vector2(10.0f, 10.0f);
    
    // Visual properties
    Color backgroundColor = Color(0.1f, 0.1f, 0.1f, 0.9f);
    Color textColor = Color::White();
    float cornerRadius = 4.0f;
    Vector2 padding = Vector2(8.0f, 6.0f);
    
    // State
    bool isVisible = false;
    float currentShowTime = 0.0f;
    
    UITooltip() = default;
    UITooltip(const std::string& tooltipText) : text(tooltipText) {}
};

} // namespace GUI
} // namespace Engine
