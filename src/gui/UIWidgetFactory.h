//
// UIWidgetFactory.h
// macOS 3D Looter-Shooter Game Engine
//
// Factory for creating common GUI widgets with default configurations
//

#pragma once

#include "UIComponents.h"
#include "UITextComponents.h"
#include "UIWidgets.h"
#include "engine/ECS.h"
#include <unordered_map>

namespace Engine {
namespace GUI {

// ============================================================================
// Widget Factory Class
// ============================================================================

class UIWidgetFactory {
public:
    // Initialize the factory with an ECS reference
    static void Initialize(ECS* ecs);
    static void Shutdown();
    
    // Get the singleton instance
    static UIWidgetFactory& GetInstance();
    
    // ========================================================================
    // Basic Widget Creation
    // ========================================================================
    
    // Create a basic panel (container)
    static Entity CreatePanel(const Vector2& position, const Vector2& size, 
                             const Color& backgroundColor = Color(0.9f, 0.9f, 0.9f, 1.0f));
    
    // Create a text label
    static Entity CreateLabel(const std::string& text, const Vector2& position, 
                             float fontSize = 16.0f, const Color& textColor = Color::Black());
    
    // Create a button
    static Entity CreateButton(const std::string& text, const Vector2& position, const Vector2& size,
                              std::function<void()> onClick = nullptr);
    
    // Create an image
    static Entity CreateImage(const std::string& imagePath, const Vector2& position, const Vector2& size,
                             const Color& tintColor = Color::White());
    
    // Create a text input field
    static Entity CreateTextField(const Vector2& position, const Vector2& size,
                                 const std::string& placeholder = "Enter text...");
    
    // Create a slider
    static Entity CreateSlider(const Vector2& position, const Vector2& size,
                              float minValue = 0.0f, float maxValue = 1.0f, float initialValue = 0.5f);
    
    // Create a progress bar
    static Entity CreateProgressBar(const Vector2& position, const Vector2& size,
                                   float initialValue = 0.0f);
    
    // ========================================================================
    // Complex Widget Creation
    // ========================================================================
    
    // Create a dropdown menu
    static Entity CreateDropdown(const Vector2& position, const Vector2& size,
                                const std::vector<std::string>& options,
                                const std::string& placeholder = "Select...");
    
    // Create a list view
    static Entity CreateListView(const Vector2& position, const Vector2& size,
                                const std::vector<std::string>& items,
                                bool multiSelect = false);
    
    // Create a tab group
    static Entity CreateTabGroup(const Vector2& position, const Vector2& size,
                                const std::vector<std::string>& tabTitles);
    
    // Create a scroll view
    static Entity CreateScrollView(const Vector2& position, const Vector2& size,
                                  const Vector2& contentSize);
    
    // ========================================================================
    // Layout Containers
    // ========================================================================
    
    // Create a horizontal layout group
    static Entity CreateHorizontalLayout(const Vector2& position, const Vector2& size,
                                        float spacing = 5.0f);
    
    // Create a vertical layout group
    static Entity CreateVerticalLayout(const Vector2& position, const Vector2& size,
                                      float spacing = 5.0f);
    
    // Create a grid layout
    static Entity CreateGridLayout(const Vector2& position, const Vector2& size,
                                  int columns, int rows, const Vector2& spacing = Vector2(5.0f, 5.0f));
    
    // ========================================================================
    // Dialog and Window Creation
    // ========================================================================
    
    // Create a modal dialog
    static Entity CreateDialog(const std::string& title, const std::string& message,
                              const std::vector<std::string>& buttonLabels = {"OK"});
    
    // Create a window with title bar
    static Entity CreateWindow(const std::string& title, const Vector2& position, const Vector2& size,
                              bool resizable = true, bool closable = true);
    
    // Create a tooltip
    static Entity CreateTooltip(const std::string& text, Entity targetElement);
    
    // ========================================================================
    // Utility Methods
    // ========================================================================
    
    // Set default styles for different widget types
    static void SetDefaultButtonStyle(const Color& normal, const Color& hover, const Color& pressed);
    static void SetDefaultTextStyle(const std::string& fontName, float fontSize, const Color& color);
    static void SetDefaultPanelStyle(const Color& backgroundColor, const Color& borderColor, float borderWidth);
    
    // Apply theme to existing widgets
    static void ApplyTheme(const std::string& themeName);
    
    // Register custom widget creation function
    template<typename WidgetType>
    static void RegisterCustomWidget(const std::string& name, 
                                   std::function<Entity(const Vector2&, const Vector2&)> creator);

private:
    UIWidgetFactory() = default;
    ~UIWidgetFactory() = default;
    UIWidgetFactory(const UIWidgetFactory&) = delete;
    UIWidgetFactory& operator=(const UIWidgetFactory&) = delete;
    
    // Internal helper methods
    static Entity CreateBaseWidget(const Vector2& position, const Vector2& size);
    static void SetupInteractable(Entity entity, std::function<void()> onClick = nullptr);
    static void SetupRenderer(Entity entity, const Color& color = Color::White());
    static void SetupText(Entity entity, const std::string& text, float fontSize = 16.0f);
    
    // Default styles
    struct DefaultStyles {
        // Button styles
        Color buttonNormalColor = Color(0.8f, 0.8f, 0.8f, 1.0f);
        Color buttonHoverColor = Color(0.9f, 0.9f, 0.9f, 1.0f);
        Color buttonPressedColor = Color(0.7f, 0.7f, 0.7f, 1.0f);
        
        // Text styles
        std::string defaultFont = "Arial";
        float defaultFontSize = 16.0f;
        Color defaultTextColor = Color::Black();
        
        // Panel styles
        Color defaultPanelColor = Color(0.9f, 0.9f, 0.9f, 1.0f);
        Color defaultBorderColor = Color::Black();
        float defaultBorderWidth = 1.0f;
        
        // Input field styles
        Color inputFieldBackgroundColor = Color::White();
        Color inputFieldBorderColor = Color(0.6f, 0.6f, 0.6f, 1.0f);
        Color inputFieldFocusColor = Color(0.3f, 0.6f, 1.0f, 1.0f);
        
        // Slider styles
        Color sliderTrackColor = Color(0.3f, 0.3f, 0.3f, 1.0f);
        Color sliderFillColor = Color(0.3f, 0.6f, 1.0f, 1.0f);
        Color sliderHandleColor = Color::White();
    };
    
    static ECS* s_ecs;
    static DefaultStyles s_defaultStyles;
    static std::unordered_map<std::string, std::function<Entity(const Vector2&, const Vector2&)>> s_customWidgets;
    static bool s_initialized;
};

// ============================================================================
// Template Implementation
// ============================================================================

template<typename WidgetType>
void UIWidgetFactory::RegisterCustomWidget(const std::string& name, 
                                          std::function<Entity(const Vector2&, const Vector2&)> creator) {
    s_customWidgets[name] = creator;
}

// ============================================================================
// Convenience Macros for Widget Creation
// ============================================================================

#define CREATE_BUTTON(text, x, y, w, h, callback) \
    UIWidgetFactory::CreateButton(text, Vector2(x, y), Vector2(w, h), callback)

#define CREATE_LABEL(text, x, y, size) \
    UIWidgetFactory::CreateLabel(text, Vector2(x, y), size)

#define CREATE_PANEL(x, y, w, h, color) \
    UIWidgetFactory::CreatePanel(Vector2(x, y), Vector2(w, h), color)

#define CREATE_TEXTFIELD(x, y, w, h, placeholder) \
    UIWidgetFactory::CreateTextField(Vector2(x, y), Vector2(w, h), placeholder)

#define CREATE_SLIDER(x, y, w, h, min, max, initial) \
    UIWidgetFactory::CreateSlider(Vector2(x, y), Vector2(w, h), min, max, initial)

} // namespace GUI
} // namespace Engine
