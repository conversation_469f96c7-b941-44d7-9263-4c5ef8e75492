//
// UIWidgets.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of widget-specific GUI components
//

#include "UIWidgets.h"
#include <algorithm>

namespace Engine {
namespace GUI {

// ============================================================================
// UIButton Implementation
// ============================================================================

void UIButton::SetColors(const Color& normal, const Color& hover, const Color& pressed) {
    normalColor = normal;
    hoverColor = hover;
    pressedColor = pressed;
}

void UIButton::SetToggleState(bool toggled) {
    if (buttonType == ButtonType::Toggle || buttonType == ButtonType::Radio) {
        bool oldState = isToggled;
        isToggled = toggled;
        
        if (oldState != isToggled && onToggle) {
            onToggle(isToggled);
        }
    }
}

Color UIButton::GetCurrentColor(UIState state) const {
    switch (state) {
        case UIState::Hovered:
            return hoverColor;
        case UIState::Pressed:
            return pressedColor;
        case UIState::Disabled:
            return disabledColor;
        case UIState::Normal:
        default:
            return normalColor;
    }
}

// ============================================================================
// UIList Implementation
// ============================================================================

void UIList::AddItem(Entity item) {
    auto it = std::find(items.begin(), items.end(), item);
    if (it == items.end()) {
        items.push_back(item);
    }
}

void UIList::RemoveItem(Entity item) {
    // Remove from items list
    auto it = std::find(items.begin(), items.end(), item);
    if (it != items.end()) {
        items.erase(it);
    }
    
    // Remove from selected items if present
    auto selectedIt = std::find(selectedItems.begin(), selectedItems.end(), item);
    if (selectedIt != selectedItems.end()) {
        selectedItems.erase(selectedIt);
        if (onSelectionChanged) {
            onSelectionChanged(selectedItems);
        }
    }
}

void UIList::ClearItems() {
    items.clear();
    ClearSelection();
}

void UIList::SelectItem(Entity item) {
    // Check if item exists in the list
    auto it = std::find(items.begin(), items.end(), item);
    if (it == items.end()) return;
    
    // Handle single vs multi-select
    if (!multiSelect) {
        ClearSelection();
    }
    
    // Add to selection if not already selected
    auto selectedIt = std::find(selectedItems.begin(), selectedItems.end(), item);
    if (selectedIt == selectedItems.end()) {
        selectedItems.push_back(item);
        
        if (onItemSelected) {
            onItemSelected(item);
        }
        
        if (onSelectionChanged) {
            onSelectionChanged(selectedItems);
        }
    }
}

void UIList::DeselectItem(Entity item) {
    auto selectedIt = std::find(selectedItems.begin(), selectedItems.end(), item);
    if (selectedIt != selectedItems.end()) {
        selectedItems.erase(selectedIt);
        
        if (onItemDeselected) {
            onItemDeselected(item);
        }
        
        if (onSelectionChanged) {
            onSelectionChanged(selectedItems);
        }
    }
}

void UIList::ClearSelection() {
    if (!selectedItems.empty()) {
        selectedItems.clear();
        if (onSelectionChanged) {
            onSelectionChanged(selectedItems);
        }
    }
}

bool UIList::IsItemSelected(Entity item) const {
    return std::find(selectedItems.begin(), selectedItems.end(), item) != selectedItems.end();
}

// ============================================================================
// UIDropdown Implementation
// ============================================================================

void UIDropdown::AddOption(const UIDropdownOption& option) {
    options.push_back(option);
}

void UIDropdown::RemoveOption(int index) {
    if (index >= 0 && index < static_cast<int>(options.size())) {
        options.erase(options.begin() + index);
        
        // Adjust selected index if necessary
        if (selectedIndex == index) {
            selectedIndex = -1;
        } else if (selectedIndex > index) {
            selectedIndex--;
        }
    }
}

void UIDropdown::SetSelectedIndex(int index) {
    if (index >= -1 && index < static_cast<int>(options.size())) {
        int oldIndex = selectedIndex;
        selectedIndex = index;
        
        if (oldIndex != selectedIndex && onSelectionChanged) {
            std::string value = (selectedIndex >= 0) ? options[selectedIndex].value : "";
            onSelectionChanged(selectedIndex, value);
        }
    }
}

void UIDropdown::SetSelectedValue(const std::string& value) {
    for (int i = 0; i < static_cast<int>(options.size()); ++i) {
        if (options[i].value == value) {
            SetSelectedIndex(i);
            return;
        }
    }
    SetSelectedIndex(-1);  // Value not found
}

std::string UIDropdown::GetSelectedText() const {
    if (selectedIndex >= 0 && selectedIndex < static_cast<int>(options.size())) {
        return options[selectedIndex].text;
    }
    return placeholder;
}

std::string UIDropdown::GetSelectedValue() const {
    if (selectedIndex >= 0 && selectedIndex < static_cast<int>(options.size())) {
        return options[selectedIndex].value;
    }
    return "";
}

void UIDropdown::OpenDropdown() {
    if (!isOpen) {
        isOpen = true;
        if (onDropdownOpen) {
            onDropdownOpen();
        }
    }
}

void UIDropdown::CloseDropdown() {
    if (isOpen) {
        isOpen = false;
        if (onDropdownClose) {
            onDropdownClose();
        }
    }
}

// ============================================================================
// UITabGroup Implementation
// ============================================================================

void UITabGroup::AddTab(Entity tab) {
    auto it = std::find(tabs.begin(), tabs.end(), tab);
    if (it == tabs.end()) {
        tabs.push_back(tab);
        
        // If this is the first tab, make it active
        if (tabs.size() == 1) {
            activeTabIndex = 0;
        }
    }
}

void UITabGroup::RemoveTab(int index) {
    if (index >= 0 && index < static_cast<int>(tabs.size())) {
        tabs.erase(tabs.begin() + index);
        
        // Adjust active tab index
        if (activeTabIndex == index) {
            // If we removed the active tab, activate the previous one (or first if it was the first)
            activeTabIndex = std::max(0, std::min(activeTabIndex - 1, static_cast<int>(tabs.size()) - 1));
            if (!tabs.empty() && onTabChanged) {
                onTabChanged(activeTabIndex);
            }
        } else if (activeTabIndex > index) {
            activeTabIndex--;
        }
        
        // If no tabs left, reset active index
        if (tabs.empty()) {
            activeTabIndex = -1;
        }
    }
}

void UITabGroup::SetActiveTab(int index) {
    if (index >= 0 && index < static_cast<int>(tabs.size()) && index != activeTabIndex) {
        activeTabIndex = index;
        if (onTabChanged) {
            onTabChanged(activeTabIndex);
        }
    }
}

void UITabGroup::CloseTab(int index) {
    if (index >= 0 && index < static_cast<int>(tabs.size())) {
        if (onTabClosed) {
            onTabClosed(index);
        }
        RemoveTab(index);
    }
}

Entity UITabGroup::GetActiveTabContent() const {
    if (activeTabIndex >= 0 && activeTabIndex < static_cast<int>(tabs.size())) {
        return tabs[activeTabIndex];  // This would need to be resolved to the actual content entity
    }
    return INVALID_ENTITY;
}

} // namespace GUI
} // namespace Engine
