# GUI System for macOS 3D Looter-Shooter Game Engine

A comprehensive, Metal-based GUI system designed specifically for the macOS 3D Looter-Shooter Game Engine. This system provides a complete solution for creating game user interfaces with modern design patterns and high performance.

## 🎯 Features

### Core Features
- **ECS Integration**: Seamlessly integrates with the existing Entity-Component-System architecture
- **Metal Rendering**: High-performance GPU-accelerated rendering using Apple's Metal API
- **Modular Design**: Clean separation of concerns with dedicated systems for layout, input, and rendering
- **Widget Factory**: Easy-to-use factory pattern for creating common UI elements
- **Layout System**: Automatic positioning and sizing with support for horizontal, vertical, and grid layouts
- **Input Handling**: Complete mouse and keyboard input support with focus management
- **Theme Support**: Customizable themes and styling system

### Widget Library
- **Basic Widgets**: Buttons, Labels, Panels, Images
- **Input Widgets**: Text Fields, Sliders, Progress Bars
- **Container Widgets**: Lists, Dropdowns, Tab Groups
- **Layout Containers**: Horizontal/Vertical layouts, Grid layouts
- **Interactive Elements**: Tooltips, Dialogs, Menus

### Advanced Features
- **Canvas System**: Multiple canvas support for different UI layers
- **Event System**: Comprehensive event handling with callbacks
- **Debug Mode**: Visual debugging tools for layout and interaction
- **Performance Monitoring**: Built-in statistics and profiling
- **Demo System**: Complete demonstration of all GUI features

## 🏗️ Architecture

### System Components

1. **UIManager**: Central coordinator for all GUI systems
2. **UILayoutSystem**: Handles automatic positioning and sizing
3. **UIInputSystem**: Manages mouse and keyboard interactions
4. **UIRenderSystem**: Metal-based rendering pipeline
5. **UIWidgetFactory**: Factory for creating common UI elements

### Component Types

- **Core Components**: UITransform, UIRenderer, UIInteractable, UILayout
- **Text Components**: UIText, UIInputField
- **Widget Components**: UIButton, UIPanel, UISlider, UIProgressBar
- **Container Components**: UIList, UIDropdown, UITabGroup

## 🚀 Quick Start

### Basic Setup

```cpp
#include "gui/GUI.h"

// Initialize the GUI system
bool success = Engine::GUI::InitializeGUISystem(
    &ecs,                    // ECS instance
    inputSystem,             // Input system
    metalDevice,             // Metal device
    commandQueue,            // Metal command queue
    renderPassDescriptor    // Metal render pass descriptor
);

// Create a simple button
Entity button = Engine::GUI::CreateButton(
    "Click Me!",             // Text
    100, 100,               // Position (x, y)
    200, 50,                // Size (width, height)
    []() {                  // Click callback
        std::cout << "Button clicked!" << std::endl;
    }
);
```

### Creating UI Elements

```cpp
// Create various UI elements
Entity label = GUI_LABEL("Hello World", 50, 50, 24);
Entity panel = GUI_PANEL(0, 0, 800, 600, GUI_COLOR_DARK);
Entity textField = GUI_TEXTFIELD(100, 200, 300, 40, "Enter text...");
Entity slider = GUI_SLIDER(100, 300, 200, 30, 0.0f, 100.0f, 50.0f);

// Create layouts
Entity horizontalLayout = GUI_HORIZONTAL_LAYOUT(50, 400, 700, 100, 10);
Entity verticalLayout = GUI_VERTICAL_LAYOUT(800, 50, 200, 500, 5);
```

### Event Handling

```cpp
// Register global event handler
Engine::GUI::RegisterGUIEventHandler([](const Engine::GUI::UIInputEvent& event) {
    switch (event.type) {
        case Engine::GUI::UIInputEvent::Type::MouseClick:
            std::cout << "Mouse clicked on entity: " << event.targetEntity << std::endl;
            break;
        case Engine::GUI::UIInputEvent::Type::TextInput:
            std::cout << "Text input: " << event.textInput << std::endl;
            break;
    }
});
```

## 🎮 Demo System

The GUI system includes a comprehensive demo that showcases all features:

### Available Demos

1. **Main Menu**: Complete game menu with navigation
2. **Game HUD**: Health bars, ammo counters, minimap
3. **Settings Menu**: Audio/video settings with sliders and toggles
4. **Inventory**: Grid-based inventory system
5. **Dialog System**: Conversation interface with multiple choice
6. **Layout Demo**: Showcase of different layout systems
7. **Widget Showcase**: All available widgets in action
8. **Interaction Demo**: Input handling and focus management

### Running Demos

```cpp
// Enable GUI demo mode
./game --gui-demo

// Or programmatically
auto* guiDemo = Engine::GUI::GetGUIDemo();
guiDemo->ShowDemo("MainMenu");
```

## 🎨 Theming and Styling

### Built-in Themes

```cpp
// Load predefined themes
Engine::GUI::LoadGUITheme("dark");
Engine::GUI::LoadGUITheme("light");

// Set custom colors
Engine::GUI::SetGUIColors(
    GUI_COLOR_PRIMARY,      // Primary color
    GUI_COLOR_SECONDARY,    // Secondary color
    GUI_COLOR_SUCCESS,      // Accent color
    GUI_COLOR_DARK,         // Background color
    GUI_COLOR_LIGHT         // Text color
);
```

### Custom Styling

```cpp
// Set default styles
UIWidgetFactory::SetDefaultButtonStyle(
    Color(0.8f, 0.8f, 0.8f, 1.0f),  // Normal
    Color(0.9f, 0.9f, 0.9f, 1.0f),  // Hover
    Color(0.7f, 0.7f, 0.7f, 1.0f)   // Pressed
);

UIWidgetFactory::SetDefaultTextStyle("Arial", 16.0f, Color::Black());
```

## 📊 Performance

### Optimization Features

- **Batched Rendering**: Automatic batching of similar UI elements
- **Frustum Culling**: Only render visible UI elements
- **Dirty Flagging**: Only update layouts when necessary
- **Memory Pooling**: Efficient memory management for UI components
- **GPU Acceleration**: All rendering performed on GPU using Metal

### Performance Monitoring

```cpp
// Get rendering statistics
const auto& renderStats = Engine::GUI::GetGUIRenderStats();
std::cout << "Draw Calls: " << renderStats.drawCalls << std::endl;
std::cout << "Triangles: " << renderStats.triangles << std::endl;

// Get update statistics
const auto& updateStats = Engine::GUI::GetGUIUpdateStats();
std::cout << "Update Time: " << updateStats.updateTime << " ms" << std::endl;
```

## 🔧 Configuration

### GUI Configuration

```cpp
Engine::GUI::UIConfig config;
config.canvasSize = Vector2(1920, 1080);
config.enableDebugRendering = true;
config.enableBatching = true;
config.maxVerticesPerFrame = 10000;

Engine::GUI::SetGUIConfig(config);
```

### Debug Mode

```cpp
// Enable debug visualization
Engine::GUI::SetGUIDebugMode(true);

// Print statistics
Engine::GUI::PrintGUIStats();
```

## 📁 File Structure

```
src/gui/
├── GUI.h                    # Main header - includes everything
├── GUI.cpp                  # Main implementation
├── UIComponents.h           # Core UI components
├── UIComponents.cpp         # Core UI implementation
├── UITextComponents.h       # Text and input components
├── UITextComponents.cpp     # Text implementation
├── UIWidgets.h             # Widget-specific components
├── UIWidgets.cpp           # Widget implementation
├── UILayoutSystem.h        # Layout management system
├── UILayoutSystem.cpp      # Layout implementation
├── UIInputSystem.h         # Input handling system
├── UIInputSystem.cpp       # Input implementation
├── UIRenderSystem.h        # Metal rendering system
├── UIRenderSystem.mm       # Metal rendering implementation
├── UIManager.h             # GUI system coordinator
├── UIManager.cpp           # Manager implementation
├── UIWidgetFactory.h       # Widget creation factory
├── UIWidgetFactory.cpp     # Factory implementation
├── UIDemo.h                # Demonstration system
├── UIDemo.cpp              # Demo implementation
└── README.md               # This file
```

## 🎯 Integration with Game Engine

The GUI system is designed to integrate seamlessly with the existing game engine:

1. **ECS Integration**: All UI elements are entities with components
2. **Input System**: Uses the existing input management system
3. **Graphics Pipeline**: Integrates with the Metal-based rendering system
4. **Asset Management**: Compatible with the asset loading system
5. **Memory Management**: Follows engine memory management patterns

## 🚀 Future Enhancements

- **Animation System**: Smooth transitions and animations
- **Rich Text**: Markup support for formatted text
- **Accessibility**: Screen reader and keyboard navigation support
- **Localization**: Multi-language text support
- **Custom Shaders**: Support for custom UI shaders
- **Vector Graphics**: SVG-like vector drawing support

## 📝 License

This GUI system is part of the macOS 3D Looter-Shooter Game Engine and follows the same licensing terms.
