//
// UITextComponents.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of text and image GUI components
//

#include "UITextComponents.h"
#include <algorithm>
#include <cmath>

namespace Engine {
namespace GUI {

// ============================================================================
// UIInputField Implementation
// ============================================================================

void UIInputField::SetText(const std::string& newText) {
    if (readOnly) return;
    
    std::string oldText = text;
    
    // Apply max length constraint
    if (maxLength > 0 && newText.length() > static_cast<size_t>(maxLength)) {
        text = newText.substr(0, maxLength);
    } else {
        text = newText;
    }
    
    // Validate input based on type
    bool isValid = true;
    switch (inputType) {
        case InputType::Number:
            // Check if text contains only numbers, decimal point, and minus sign
            for (char c : text) {
                if (!std::isdigit(c) && c != '.' && c != '-') {
                    isValid = false;
                    break;
                }
            }
            break;
            
        case InputType::Email:
            // Basic email validation (contains @ and .)
            isValid = text.find('@') != std::string::npos && text.find('.') != std::string::npos;
            break;
            
        case InputType::URL:
            // Basic URL validation (starts with http:// or https://)
            isValid = text.find("http://") == 0 || text.find("https://") == 0;
            break;
            
        case InputType::Custom:
            if (customValidator) {
                isValid = customValidator(text);
            }
            break;
            
        default:
            break;
    }
    
    if (!isValid) {
        text = oldText;  // Revert to old text if validation fails
        return;
    }
    
    // Update cursor position if it's beyond the new text length
    cursorPosition = std::min(cursorPosition, static_cast<int>(text.length()));
    
    // Clear selection if text changed
    selectionStart = selectionEnd = cursorPosition;
    
    // Trigger change event
    if (text != oldText && onTextChanged) {
        onTextChanged(text);
    }
}

void UIInputField::InsertText(const std::string& insertText) {
    if (readOnly) return;
    
    // Delete selected text first
    if (HasSelection()) {
        DeleteSelection();
    }
    
    // Insert new text at cursor position
    std::string newText = text.substr(0, cursorPosition) + insertText + text.substr(cursorPosition);
    
    // Check max length
    if (maxLength > 0 && newText.length() > static_cast<size_t>(maxLength)) {
        return;  // Don't insert if it would exceed max length
    }
    
    text = newText;
    cursorPosition += static_cast<int>(insertText.length());
    
    // Trigger change event
    if (onTextChanged) {
        onTextChanged(text);
    }
}

void UIInputField::DeleteSelection() {
    if (!HasSelection()) return;
    
    int start = std::min(selectionStart, selectionEnd);
    int end = std::max(selectionStart, selectionEnd);
    
    text = text.substr(0, start) + text.substr(end);
    cursorPosition = start;
    selectionStart = selectionEnd = cursorPosition;
    
    // Trigger change event
    if (onTextChanged) {
        onTextChanged(text);
    }
}

void UIInputField::SetCursorPosition(int position) {
    cursorPosition = std::max(0, std::min(position, static_cast<int>(text.length())));
    selectionStart = selectionEnd = cursorPosition;
}

void UIInputField::SetSelection(int start, int end) {
    int textLength = static_cast<int>(text.length());
    selectionStart = std::max(0, std::min(start, textLength));
    selectionEnd = std::max(0, std::min(end, textLength));
    cursorPosition = selectionEnd;
}

void UIInputField::SelectAll() {
    selectionStart = 0;
    selectionEnd = static_cast<int>(text.length());
    cursorPosition = selectionEnd;
}

std::string UIInputField::GetSelectedText() const {
    if (!HasSelection()) return "";
    
    int start = std::min(selectionStart, selectionEnd);
    int end = std::max(selectionStart, selectionEnd);
    
    return text.substr(start, end - start);
}

// ============================================================================
// UIProgressBar Implementation
// ============================================================================

void UIProgressBar::SetValue(float val) {
    float newValue = std::max(minValue, std::min(val, maxValue));
    if (newValue != value) {
        value = newValue;
    }
}

void UIProgressBar::SetRange(float min, float max) {
    minValue = min;
    maxValue = max;
    SetValue(value);  // Clamp current value to new range
}

float UIProgressBar::GetNormalizedValue() const {
    if (maxValue == minValue) return 0.0f;
    return (value - minValue) / (maxValue - minValue);
}

// ============================================================================
// UISlider Implementation
// ============================================================================

void UISlider::SetValue(float val) {
    float newValue = val;
    
    // Apply step constraint
    if (step > 0.0f) {
        float steps = std::round((val - minValue) / step);
        newValue = minValue + steps * step;
    }
    
    // Clamp to range
    newValue = std::max(minValue, std::min(newValue, maxValue));
    
    if (newValue != value) {
        value = newValue;
        if (onValueChanged) {
            onValueChanged(value);
        }
    }
}

void UISlider::SetRange(float min, float max) {
    minValue = min;
    maxValue = max;
    SetValue(value);  // Clamp current value to new range
}

float UISlider::GetNormalizedValue() const {
    if (maxValue == minValue) return 0.0f;
    return (value - minValue) / (maxValue - minValue);
}

void UISlider::SetNormalizedValue(float normalizedVal) {
    float clampedNormalized = std::max(0.0f, std::min(normalizedVal, 1.0f));
    float newValue = minValue + clampedNormalized * (maxValue - minValue);
    SetValue(newValue);
}

} // namespace GUI
} // namespace Engine
