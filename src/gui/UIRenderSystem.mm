//
// UIRenderSystem.mm
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of the GUI rendering system using Metal
//

#include "UIRenderSystem.h"
#include "engine/ECS.h"
#include <iostream>
#include <algorithm>

#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>
#import <ImageIO/ImageIO.h>

namespace Engine {
namespace GUI {

// ============================================================================
// UIRenderBatch Implementation
// ============================================================================

void UIRenderBatch::AddQuad(const Rect& rect, const Rect& uvRect, const Color& color) {
    uint16_t baseIndex = static_cast<uint16_t>(vertices.size());
    
    // Add vertices (clockwise from top-left)
    vertices.emplace_back(Vector2(rect.x, rect.y), Vector2(uvRect.x, uvRect.y), color);
    vertices.emplace_back(Vector2(rect.x + rect.width, rect.y), Vector2(uvRect.x + uvRect.width, uvRect.y), color);
    vertices.emplace_back(Vector2(rect.x + rect.width, rect.y + rect.height), Vector2(uvRect.x + uvRect.width, uvRect.y + uvRect.height), color);
    vertices.emplace_back(Vector2(rect.x, rect.y + rect.height), Vector2(uvRect.x, uvRect.y + uvRect.height), color);
    
    // Add indices for two triangles
    indices.insert(indices.end(), {
        baseIndex, static_cast<uint16_t>(baseIndex + 1), static_cast<uint16_t>(baseIndex + 2),
        baseIndex, static_cast<uint16_t>(baseIndex + 2), static_cast<uint16_t>(baseIndex + 3)
    });
}

// ============================================================================
// UIFont Implementation
// ============================================================================

UIFont::UIFont(const std::string& fontName, float fontSize) : name(fontName), size(fontSize) {
    // Create Core Text font
    CFStringRef fontNameRef = CFStringCreateWithCString(kCFAllocatorDefault, fontName.c_str(), kCFStringEncodingUTF8);
    ctFont = CTFontCreateWithName(fontNameRef, fontSize, nullptr);
    CFRelease(fontNameRef);
    
    if (!ctFont) {
        std::cerr << "❌ Failed to create font: " << fontName << std::endl;
    }
}

UIFont::~UIFont() {
    if (ctFont) {
        CFRelease(ctFont);
        ctFont = nullptr;
    }
    
    // Release glyph textures
    for (auto& pair : glyphTextures) {
        if (pair.second) {
            [pair.second release];
        }
    }
    glyphTextures.clear();
}

// ============================================================================
// Matrix4 Implementation
// ============================================================================

Matrix4::Matrix4() {
    for (int i = 0; i < 16; ++i) {
        m[i] = 0.0f;
    }
}

Matrix4::Matrix4(float diagonal) : Matrix4() {
    m[0] = m[5] = m[10] = m[15] = diagonal;
}

Matrix4 Matrix4::Identity() {
    return Matrix4(1.0f);
}

Matrix4 Matrix4::Orthographic(float left, float right, float bottom, float top, float near, float far) {
    Matrix4 result;
    
    result.m[0] = 2.0f / (right - left);
    result.m[5] = 2.0f / (top - bottom);
    result.m[10] = -2.0f / (far - near);
    result.m[12] = -(right + left) / (right - left);
    result.m[13] = -(top + bottom) / (top - bottom);
    result.m[14] = -(far + near) / (far - near);
    result.m[15] = 1.0f;
    
    return result;
}

// ============================================================================
// UIRenderSystem Implementation
// ============================================================================

UIRenderSystem::UIRenderSystem() {
    // Initialize projection matrix
    UpdateProjectionMatrix();
}

UIRenderSystem::~UIRenderSystem() {
    if (m_initialized) {
        Shutdown();
    }
}

void UIRenderSystem::Initialize() {
    if (m_initialized) {
        std::cout << "⚠️  UIRenderSystem already initialized" << std::endl;
        return;
    }
    
    if (!m_device) {
        std::cerr << "❌ Metal device not set for UIRenderSystem" << std::endl;
        return;
    }
    
    if (!InitializeMetalResources()) {
        std::cerr << "❌ Failed to initialize Metal resources for UIRenderSystem" << std::endl;
        return;
    }
    
    m_initialized = true;
    std::cout << "✅ UIRenderSystem initialized" << std::endl;
}

void UIRenderSystem::Update(DeltaTime deltaTime) {
    if (!m_initialized) return;
    
    // Update render statistics
    m_stats.frameTime = deltaTime;
}

void UIRenderSystem::Shutdown() {
    if (!m_initialized) return;
    
    // Release Metal resources
    if (m_uiPipelineState) {
        [m_uiPipelineState release];
        m_uiPipelineState = nil;
    }
    
    if (m_textPipelineState) {
        [m_textPipelineState release];
        m_textPipelineState = nil;
    }
    
    if (m_depthStencilState) {
        [m_depthStencilState release];
        m_depthStencilState = nil;
    }
    
    if (m_vertexBuffer) {
        [m_vertexBuffer release];
        m_vertexBuffer = nil;
    }
    
    if (m_indexBuffer) {
        [m_indexBuffer release];
        m_indexBuffer = nil;
    }
    
    if (m_uniformBuffer) {
        [m_uniformBuffer release];
        m_uniformBuffer = nil;
    }
    
    if (m_whiteTexture) {
        [m_whiteTexture release];
        m_whiteTexture = nil;
    }
    
    // Clear caches
    for (auto& pair : m_textureCache) {
        if (pair.second) {
            [pair.second release];
        }
    }
    m_textureCache.clear();
    
    m_fontCache.clear();
    
    m_initialized = false;
    std::cout << "🔧 UIRenderSystem shutdown complete" << std::endl;
}

void UIRenderSystem::SetMetalDevice(id<MTLDevice> device) {
    m_device = device;
}

void UIRenderSystem::SetCommandQueue(id<MTLCommandQueue> commandQueue) {
    m_commandQueue = commandQueue;
}

void UIRenderSystem::SetRenderPassDescriptor(MTLRenderPassDescriptor* renderPassDescriptor) {
    m_renderPassDescriptor = renderPassDescriptor;
}

bool UIRenderSystem::InitializeMetalResources() {
    CreateShaders();
    CreateBuffers();
    CreateDefaultTextures();
    
    return m_uiPipelineState != nil && m_vertexBuffer != nil;
}

void UIRenderSystem::CreateShaders() {
    // Create shader library
    NSError* error = nil;
    
    // UI Vertex Shader
    NSString* uiVertexShaderSource = @R"(
        #include <metal_stdlib>
        using namespace metal;
        
        struct VertexIn {
            float2 position [[attribute(0)]];
            float2 texCoord [[attribute(1)]];
            float4 color [[attribute(2)]];
        };
        
        struct VertexOut {
            float4 position [[position]];
            float2 texCoord;
            float4 color;
        };
        
        struct Uniforms {
            float4x4 projectionMatrix;
        };
        
        vertex VertexOut ui_vertex_main(VertexIn in [[stage_in]],
                                       constant Uniforms& uniforms [[buffer(1)]]) {
            VertexOut out;
            out.position = uniforms.projectionMatrix * float4(in.position, 0.0, 1.0);
            out.texCoord = in.texCoord;
            out.color = in.color;
            return out;
        }
    )";
    
    // UI Fragment Shader
    NSString* uiFragmentShaderSource = @R"(
        #include <metal_stdlib>
        using namespace metal;
        
        struct VertexOut {
            float4 position [[position]];
            float2 texCoord;
            float4 color;
        };
        
        fragment float4 ui_fragment_main(VertexOut in [[stage_in]],
                                        texture2d<float> texture [[texture(0)]],
                                        sampler textureSampler [[sampler(0)]]) {
            float4 texColor = texture.sample(textureSampler, in.texCoord);
            return texColor * in.color;
        }
    )";
    
    // Compile shaders
    id<MTLLibrary> library = [m_device newLibraryWithSource:uiVertexShaderSource options:nil error:&error];
    if (error) {
        NSLog(@"❌ Failed to compile UI vertex shader: %@", error.localizedDescription);
        return;
    }
    
    id<MTLFunction> vertexFunction = [library newFunctionWithName:@"ui_vertex_main"];
    id<MTLFunction> fragmentFunction = [library newFunctionWithName:@"ui_fragment_main"];
    
    // Create render pipeline descriptor
    MTLRenderPipelineDescriptor* pipelineDescriptor = [[MTLRenderPipelineDescriptor alloc] init];
    pipelineDescriptor.vertexFunction = vertexFunction;
    pipelineDescriptor.fragmentFunction = fragmentFunction;
    pipelineDescriptor.colorAttachments[0].pixelFormat = MTLPixelFormatBGRA8Unorm;
    
    // Enable blending for transparency
    pipelineDescriptor.colorAttachments[0].blendingEnabled = YES;
    pipelineDescriptor.colorAttachments[0].rgbBlendOperation = MTLBlendOperationAdd;
    pipelineDescriptor.colorAttachments[0].alphaBlendOperation = MTLBlendOperationAdd;
    pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = MTLBlendFactorSourceAlpha;
    pipelineDescriptor.colorAttachments[0].sourceAlphaBlendFactor = MTLBlendFactorSourceAlpha;
    pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = MTLBlendFactorOneMinusSourceAlpha;
    pipelineDescriptor.colorAttachments[0].destinationAlphaBlendFactor = MTLBlendFactorOneMinusSourceAlpha;
    
    // Create vertex descriptor
    MTLVertexDescriptor* vertexDescriptor = [[MTLVertexDescriptor alloc] init];
    
    // Position attribute
    vertexDescriptor.attributes[0].format = MTLVertexFormatFloat2;
    vertexDescriptor.attributes[0].offset = 0;
    vertexDescriptor.attributes[0].bufferIndex = 0;
    
    // Texture coordinate attribute
    vertexDescriptor.attributes[1].format = MTLVertexFormatFloat2;
    vertexDescriptor.attributes[1].offset = sizeof(Vector2);
    vertexDescriptor.attributes[1].bufferIndex = 0;
    
    // Color attribute
    vertexDescriptor.attributes[2].format = MTLVertexFormatFloat4;
    vertexDescriptor.attributes[2].offset = sizeof(Vector2) * 2;
    vertexDescriptor.attributes[2].bufferIndex = 0;
    
    // Layout
    vertexDescriptor.layouts[0].stride = sizeof(UIVertex);
    vertexDescriptor.layouts[0].stepRate = 1;
    vertexDescriptor.layouts[0].stepFunction = MTLVertexStepFunctionPerVertex;
    
    pipelineDescriptor.vertexDescriptor = vertexDescriptor;
    
    // Create pipeline state
    m_uiPipelineState = [m_device newRenderPipelineStateWithDescriptor:pipelineDescriptor error:&error];
    if (error) {
        NSLog(@"❌ Failed to create UI render pipeline state: %@", error.localizedDescription);
    }
    
    // Create depth stencil state (disabled for UI)
    MTLDepthStencilDescriptor* depthStencilDescriptor = [[MTLDepthStencilDescriptor alloc] init];
    depthStencilDescriptor.depthCompareFunction = MTLCompareFunctionAlways;
    depthStencilDescriptor.depthWriteEnabled = NO;
    m_depthStencilState = [m_device newDepthStencilStateWithDescriptor:depthStencilDescriptor];
    
    [library release];
    [vertexFunction release];
    [fragmentFunction release];
    [pipelineDescriptor release];
    [vertexDescriptor release];
    [depthStencilDescriptor release];
}

void UIRenderSystem::CreateBuffers() {
    // Create vertex buffer (will be updated each frame)
    m_vertexBuffer = [m_device newBufferWithLength:sizeof(UIVertex) * 10000 options:MTLResourceStorageModeShared];
    
    // Create index buffer
    m_indexBuffer = [m_device newBufferWithLength:sizeof(uint16_t) * 15000 options:MTLResourceStorageModeShared];
    
    // Create uniform buffer
    m_uniformBuffer = [m_device newBufferWithLength:sizeof(Matrix4) options:MTLResourceStorageModeShared];
}

void UIRenderSystem::CreateDefaultTextures() {
    // Create white texture
    uint32_t whitePixel = 0xFFFFFFFF;
    
    MTLTextureDescriptor* textureDescriptor = [MTLTextureDescriptor texture2DDescriptorWithPixelFormat:MTLPixelFormatRGBA8Unorm
                                                                                                 width:1
                                                                                                height:1
                                                                                             mipmapped:NO];
    textureDescriptor.usage = MTLTextureUsageShaderRead;
    
    m_whiteTexture = [m_device newTextureWithDescriptor:textureDescriptor];
    
    MTLRegion region = MTLRegionMake2D(0, 0, 1, 1);
    [m_whiteTexture replaceRegion:region mipmapLevel:0 withBytes:&whitePixel bytesPerRow:4];
    
    [textureDescriptor release];
}

// ============================================================================
// Rendering Implementation
// ============================================================================

void UIRenderSystem::Render(id<MTLCommandBuffer> commandBuffer) {
    if (!m_initialized || !m_renderPassDescriptor) return;

    // Reset statistics
    ResetStats();

    // Collect render data from all UI entities
    CollectRenderData();

    // Sort batches by sorting order
    SortBatches();

    // Create render encoder
    id<MTLRenderCommandEncoder> renderEncoder = [commandBuffer renderCommandEncoderWithDescriptor:m_renderPassDescriptor];
    if (!renderEncoder) return;

    [renderEncoder setLabel:@"UI Render Pass"];

    // Render UI
    RenderUI(renderEncoder);

    [renderEncoder endEncoding];

    // Clear batches for next frame
    ClearBatches();
}

void UIRenderSystem::RenderUI(id<MTLRenderCommandEncoder> renderEncoder) {
    if (!m_uiPipelineState) return;

    // Set pipeline state
    [renderEncoder setRenderPipelineState:m_uiPipelineState];
    [renderEncoder setDepthStencilState:m_depthStencilState];

    // Update and set uniforms
    Matrix4* uniformData = (Matrix4*)[m_uniformBuffer contents];
    *uniformData = m_projectionMatrix;
    [renderEncoder setVertexBuffer:m_uniformBuffer offset:0 atIndex:1];

    // Render batches
    RenderBatches(renderEncoder);
    RenderTextBatches(renderEncoder);
}

void UIRenderSystem::CollectRenderData() {
    if (!GetECS()) return;

    // Get all entities with UI components
    auto entities = GetECS()->GetEntitiesWithComponents<UITransform, UIRenderer>();

    for (Entity entity : entities) {
        const auto& renderer = GetECS()->GetComponent<UIRenderer>(entity);
        if (!renderer.visible || renderer.alpha <= 0.0f) continue;

        CollectEntityRenderData(entity);
    }
}

void UIRenderSystem::CollectEntityRenderData(Entity entity) {
    if (!GetECS()) return;

    const auto& transform = GetECS()->GetComponent<UITransform>(entity);
    const auto& renderer = GetECS()->GetComponent<UIRenderer>(entity);

    // Render different component types
    if (GetECS()->HasComponent<UIPanel>(entity)) {
        const auto& panel = GetECS()->GetComponent<UIPanel>(entity);
        RenderPanel(entity, transform, renderer, panel);
    }

    if (GetECS()->HasComponent<UIImage>(entity)) {
        const auto& image = GetECS()->GetComponent<UIImage>(entity);
        RenderImage(entity, transform, renderer, image);
    }

    if (GetECS()->HasComponent<UIText>(entity)) {
        const auto& text = GetECS()->GetComponent<UIText>(entity);
        RenderText(entity, transform, renderer, text);
    }

    if (GetECS()->HasComponent<UIButton>(entity)) {
        const auto& button = GetECS()->GetComponent<UIButton>(entity);
        RenderButton(entity, transform, renderer, button);
    }

    if (GetECS()->HasComponent<UISlider>(entity)) {
        const auto& slider = GetECS()->GetComponent<UISlider>(entity);
        RenderSlider(entity, transform, renderer, slider);
    }

    if (GetECS()->HasComponent<UIProgressBar>(entity)) {
        const auto& progressBar = GetECS()->GetComponent<UIProgressBar>(entity);
        RenderProgressBar(entity, transform, renderer, progressBar);
    }
}

void UIRenderSystem::RenderPanel(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIPanel& panel) {
    UIRenderBatch* batch = GetBatch(GetWhiteTexture(), renderer.sortingOrder);
    if (!batch) return;

    Color finalColor = renderer.backgroundColor;
    finalColor.a *= renderer.alpha;

    batch->AddQuad(transform.worldRect, Rect(0, 0, 1, 1), finalColor);

    // Render border if enabled
    if (renderer.hasBorder && renderer.borderWidth > 0.0f) {
        Color borderColor = renderer.borderColor;
        borderColor.a *= renderer.alpha;

        float borderWidth = renderer.borderWidth;
        Rect rect = transform.worldRect;

        // Top border
        batch->AddQuad(Rect(rect.x, rect.y, rect.width, borderWidth), Rect(0, 0, 1, 1), borderColor);
        // Bottom border
        batch->AddQuad(Rect(rect.x, rect.y + rect.height - borderWidth, rect.width, borderWidth), Rect(0, 0, 1, 1), borderColor);
        // Left border
        batch->AddQuad(Rect(rect.x, rect.y, borderWidth, rect.height), Rect(0, 0, 1, 1), borderColor);
        // Right border
        batch->AddQuad(Rect(rect.x + rect.width - borderWidth, rect.y, borderWidth, rect.height), Rect(0, 0, 1, 1), borderColor);
    }
}

void UIRenderSystem::RenderImage(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIImage& image) {
    id<MTLTexture> texture = LoadTexture(image.imagePath);
    if (!texture) texture = GetWhiteTexture();

    UIRenderBatch* batch = GetBatch(texture, renderer.sortingOrder);
    if (!batch) return;

    Color finalColor = image.tintColor;
    finalColor.a *= renderer.alpha;

    batch->AddQuad(transform.worldRect, image.uvRect, finalColor);
}

void UIRenderSystem::RenderButton(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIButton& button) {
    // Get current button state color
    UIState currentState = UIState::Normal;
    if (GetECS()->HasComponent<UIInteractable>(entity)) {
        currentState = GetECS()->GetComponent<UIInteractable>(entity).currentState;
    }

    Color buttonColor = button.GetCurrentColor(currentState);

    UIRenderBatch* batch = GetBatch(GetWhiteTexture(), renderer.sortingOrder);
    if (!batch) return;

    Color finalColor = buttonColor;
    finalColor.a *= renderer.alpha;

    batch->AddQuad(transform.worldRect, Rect(0, 0, 1, 1), finalColor);
}

void UIRenderSystem::RenderSlider(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UISlider& slider) {
    UIRenderBatch* batch = GetBatch(GetWhiteTexture(), renderer.sortingOrder);
    if (!batch) return;

    Rect sliderRect = transform.worldRect;

    // Render track
    Color trackColor = slider.trackColor;
    trackColor.a *= renderer.alpha;
    batch->AddQuad(sliderRect, Rect(0, 0, 1, 1), trackColor);

    // Render fill
    if (slider.showFill) {
        float fillAmount = slider.GetNormalizedValue();
        Rect fillRect = sliderRect;

        if (slider.direction == UISlider::Direction::Horizontal) {
            fillRect.width *= fillAmount;
        } else {
            fillRect.height *= fillAmount;
            fillRect.y += sliderRect.height * (1.0f - fillAmount);
        }

        Color fillColor = slider.fillColor;
        fillColor.a *= renderer.alpha;
        batch->AddQuad(fillRect, Rect(0, 0, 1, 1), fillColor);
    }

    // Render handle
    Vector2 handlePos;
    float handleSize = slider.handleSize;

    if (slider.direction == UISlider::Direction::Horizontal) {
        handlePos.x = sliderRect.x + (sliderRect.width - handleSize) * slider.GetNormalizedValue();
        handlePos.y = sliderRect.y + (sliderRect.height - handleSize) * 0.5f;
    } else {
        handlePos.x = sliderRect.x + (sliderRect.width - handleSize) * 0.5f;
        handlePos.y = sliderRect.y + (sliderRect.height - handleSize) * (1.0f - slider.GetNormalizedValue());
    }

    Rect handleRect(handlePos.x, handlePos.y, handleSize, handleSize);
    Color handleColor = slider.handleColor;
    handleColor.a *= renderer.alpha;
    batch->AddQuad(handleRect, Rect(0, 0, 1, 1), handleColor);
}

void UIRenderSystem::RenderProgressBar(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIProgressBar& progressBar) {
    UIRenderBatch* batch = GetBatch(GetWhiteTexture(), renderer.sortingOrder);
    if (!batch) return;

    Rect barRect = transform.worldRect;

    // Render background
    Color bgColor = progressBar.backgroundColor;
    bgColor.a *= renderer.alpha;
    batch->AddQuad(barRect, Rect(0, 0, 1, 1), bgColor);

    // Render fill
    float fillAmount = progressBar.GetNormalizedValue();
    if (fillAmount > 0.0f) {
        Rect fillRect = barRect;

        switch (progressBar.fillDirection) {
            case UIProgressBar::FillDirection::LeftToRight:
                fillRect.width *= fillAmount;
                break;
            case UIProgressBar::FillDirection::RightToLeft:
                fillRect.width *= fillAmount;
                fillRect.x += barRect.width * (1.0f - fillAmount);
                break;
            case UIProgressBar::FillDirection::BottomToTop:
                fillRect.height *= fillAmount;
                fillRect.y += barRect.height * (1.0f - fillAmount);
                break;
            case UIProgressBar::FillDirection::TopToBottom:
                fillRect.height *= fillAmount;
                break;
        }

        Color fillColor = progressBar.fillColor;
        fillColor.a *= renderer.alpha;
        batch->AddQuad(fillRect, Rect(0, 0, 1, 1), fillColor);
    }
}

// ============================================================================
// Helper Methods
// ============================================================================

UIRenderBatch* UIRenderSystem::GetBatch(id<MTLTexture> texture, int sortingOrder) {
    // Find existing batch with same texture and sorting order
    for (auto& batch : m_renderBatches) {
        if (batch.texture == texture && batch.sortingOrder == sortingOrder) {
            return &batch;
        }
    }

    // Create new batch
    m_renderBatches.emplace_back();
    UIRenderBatch& newBatch = m_renderBatches.back();
    newBatch.texture = texture;
    newBatch.sortingOrder = sortingOrder;
    return &newBatch;
}

void UIRenderSystem::ClearBatches() {
    for (auto& batch : m_renderBatches) {
        batch.Clear();
    }
    m_renderBatches.clear();

    for (auto& batch : m_textBatches) {
        batch.Clear();
    }
    m_textBatches.clear();
}

void UIRenderSystem::SortBatches() {
    // Sort batches by sorting order (lower values render first)
    std::sort(m_renderBatches.begin(), m_renderBatches.end(),
              [](const UIRenderBatch& a, const UIRenderBatch& b) {
                  return a.sortingOrder < b.sortingOrder;
              });

    std::sort(m_textBatches.begin(), m_textBatches.end(),
              [](const UIRenderBatch& a, const UIRenderBatch& b) {
                  return a.sortingOrder < b.sortingOrder;
              });
}

id<MTLTexture> UIRenderSystem::GetWhiteTexture() {
    return m_whiteTexture;
}

void UIRenderSystem::SetCanvasSize(const Vector2& size) {
    m_canvasSize = size;
    UpdateProjectionMatrix();
}

void UIRenderSystem::SetScreenSize(const Vector2& size) {
    m_screenSize = size;
    UpdateProjectionMatrix();
}

void UIRenderSystem::UpdateProjectionMatrix() {
    m_projectionMatrix = Matrix4::Orthographic(0.0f, m_canvasSize.x, m_canvasSize.y, 0.0f, -1.0f, 1.0f);
}

void UIRenderSystem::ResetStats() {
    m_stats.drawCalls = 0;
    m_stats.triangles = 0;
    m_stats.vertices = 0;
    m_stats.batches = 0;
    m_stats.textureBinds = 0;
}

void UIRenderSystem::RenderBatches(id<MTLRenderCommandEncoder> renderEncoder) {
    if (m_renderBatches.empty()) return;

    // Combine all vertices and indices
    std::vector<UIVertex> allVertices;
    std::vector<uint16_t> allIndices;

    for (const auto& batch : m_renderBatches) {
        if (batch.vertices.empty()) continue;

        uint16_t vertexOffset = static_cast<uint16_t>(allVertices.size());

        // Add vertices
        allVertices.insert(allVertices.end(), batch.vertices.begin(), batch.vertices.end());

        // Add indices with offset
        for (uint16_t index : batch.indices) {
            allIndices.push_back(index + vertexOffset);
        }
    }

    if (allVertices.empty()) return;

    // Update vertex buffer
    memcpy([m_vertexBuffer contents], allVertices.data(), allVertices.size() * sizeof(UIVertex));

    // Update index buffer
    memcpy([m_indexBuffer contents], allIndices.data(), allIndices.size() * sizeof(uint16_t));

    // Set buffers
    [renderEncoder setVertexBuffer:m_vertexBuffer offset:0 atIndex:0];

    // Render each batch
    uint32_t indexOffset = 0;
    id<MTLTexture> currentTexture = nil;

    for (const auto& batch : m_renderBatches) {
        if (batch.vertices.empty()) continue;

        // Bind texture if different
        if (batch.texture != currentTexture) {
            [renderEncoder setFragmentTexture:batch.texture atIndex:0];
            currentTexture = batch.texture;
            m_stats.textureBinds++;
        }

        // Draw
        [renderEncoder drawIndexedPrimitives:MTLPrimitiveTypeTriangle
                                  indexCount:batch.indices.size()
                                   indexType:MTLIndexTypeUInt16
                                 indexBuffer:m_indexBuffer
                           indexBufferOffset:indexOffset * sizeof(uint16_t)];

        indexOffset += static_cast<uint32_t>(batch.indices.size());
        m_stats.drawCalls++;
        m_stats.triangles += static_cast<int>(batch.indices.size() / 3);
    }

    m_stats.vertices = static_cast<int>(allVertices.size());
    m_stats.batches = static_cast<int>(m_renderBatches.size());
}

void UIRenderSystem::RenderTextBatches(id<MTLRenderCommandEncoder> renderEncoder) {
    // Text rendering would be similar to RenderBatches but with text-specific pipeline
    // For now, we'll use the same pipeline
    if (m_textBatches.empty()) return;

    // Similar implementation to RenderBatches
    // This would use the text pipeline state and handle glyph textures
}

void UIRenderSystem::RenderText(Entity entity, const UITransform& transform, const UIRenderer& renderer, const UIText& text) {
    if (text.text.empty()) return;

    // For now, render text as a simple colored quad
    // In a full implementation, this would render individual glyphs
    UIRenderBatch* batch = GetBatch(GetWhiteTexture(), renderer.sortingOrder);
    if (!batch) return;

    Color textColor = text.textColor;
    textColor.a *= renderer.alpha;

    batch->AddQuad(transform.worldRect, Rect(0, 0, 1, 1), textColor);
}

id<MTLTexture> UIRenderSystem::LoadTexture(const std::string& path) {
    if (path.empty()) return GetWhiteTexture();

    // Check cache first
    auto it = m_textureCache.find(path);
    if (it != m_textureCache.end()) {
        return it->second;
    }

    // Load texture from file
    NSString* nsPath = [NSString stringWithUTF8String:path.c_str()];
    NSURL* url = [NSURL fileURLWithPath:nsPath];

    CGImageSourceRef imageSource = CGImageSourceCreateWithURL((__bridge CFURLRef)url, nullptr);
    if (!imageSource) {
        std::cerr << "❌ Failed to load image: " << path << std::endl;
        return GetWhiteTexture();
    }

    CGImageRef image = CGImageSourceCreateImageAtIndex(imageSource, 0, nullptr);
    CFRelease(imageSource);

    if (!image) {
        std::cerr << "❌ Failed to create image from source: " << path << std::endl;
        return GetWhiteTexture();
    }

    // Get image properties
    size_t width = CGImageGetWidth(image);
    size_t height = CGImageGetHeight(image);

    // Create texture
    MTLTextureDescriptor* textureDescriptor = [MTLTextureDescriptor texture2DDescriptorWithPixelFormat:MTLPixelFormatRGBA8Unorm
                                                                                                 width:width
                                                                                                height:height
                                                                                             mipmapped:NO];
    textureDescriptor.usage = MTLTextureUsageShaderRead;

    id<MTLTexture> texture = [m_device newTextureWithDescriptor:textureDescriptor];

    // Create bitmap context
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(nullptr, width, height, 8, width * 4, colorSpace,
                                               kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);

    if (context) {
        // Draw image to context
        CGContextDrawImage(context, CGRectMake(0, 0, width, height), image);

        // Copy data to texture
        void* data = CGBitmapContextGetData(context);
        MTLRegion region = MTLRegionMake2D(0, 0, width, height);
        [texture replaceRegion:region mipmapLevel:0 withBytes:data bytesPerRow:width * 4];

        CGContextRelease(context);
    }

    CGColorSpaceRelease(colorSpace);
    CGImageRelease(image);
    [textureDescriptor release];

    // Cache texture
    m_textureCache[path] = texture;

    return texture;
}

UIFont* UIRenderSystem::LoadFont(const std::string& name, float size) {
    std::string key = name + "_" + std::to_string(size);

    auto it = m_fontCache.find(key);
    if (it != m_fontCache.end()) {
        return it->second.get();
    }

    auto font = std::make_unique<UIFont>(name, size);
    UIFont* fontPtr = font.get();
    m_fontCache[key] = std::move(font);

    return fontPtr;
}

UIFont* UIRenderSystem::GetFont(const std::string& name, float size) {
    return LoadFont(name, size);
}

void UIRenderSystem::UnloadFont(const std::string& name, float size) {
    std::string key = name + "_" + std::to_string(size);
    auto it = m_fontCache.find(key);
    if (it != m_fontCache.end()) {
        m_fontCache.erase(it);
    }
}

} // namespace GUI
} // namespace Engine
