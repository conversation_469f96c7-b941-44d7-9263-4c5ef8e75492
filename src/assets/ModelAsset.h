//
// ModelAsset.h
// macOS 3D Looter-Shooter Game Engine
//
// 3D model asset management with Metal rendering support
//

#pragma once

#include "AssetManager.h"
#include "TextureAsset.h"
#include "engine/Types.h"
#include "engine/Components.h"
#include <vector>
#include <string>

// Forward declarations for Metal types
#ifdef __OBJC__
#import <Metal/Metal.h>
#else
typedef void* id;
#endif

namespace Engine {
namespace Assets {

// ============================================================================
// Vertex Structures
// ============================================================================

struct Vertex {
    Vector3 position;
    Vector3 normal;
    Vector2 texCoord;
    Vector3 tangent;
    Vector3 bitangent;
    Vector4 color;
    
    // Bone weights for skeletal animation
    Vector4 boneWeights;
    uint32_t boneIndices[4];
};

struct VertexPBR {
    Vector3 position;
    Vector3 normal;
    Vector3 tangent;
    Vector2 texCoord;
    Vector4 color;
};

// ============================================================================
// Material Definition
// ============================================================================

struct Material {
    std::string name;
    
    // PBR properties
    Vector3 albedo = {1.0f, 1.0f, 1.0f};
    float metallic = 0.0f;
    float roughness = 0.5f;
    float ao = 1.0f;
    Vector3 emissive = {0.0f, 0.0f, 0.0f};
    float emissiveStrength = 1.0f;
    float normalScale = 1.0f;
    
    // Texture handles
    AssetHandle<TextureAsset> albedoTexture;
    AssetHandle<TextureAsset> normalTexture;
    AssetHandle<TextureAsset> metallicTexture;
    AssetHandle<TextureAsset> roughnessTexture;
    AssetHandle<TextureAsset> aoTexture;
    AssetHandle<TextureAsset> emissiveTexture;
    
    // Rendering properties
    bool doubleSided = false;
    bool transparent = false;
    float alphaThreshold = 0.5f;
};

// ============================================================================
// Mesh Definition
// ============================================================================

class Mesh {
public:
    Mesh(const std::string& name = "");
    ~Mesh();
    
    // Mesh data
    std::vector<Vertex> vertices;
    std::vector<uint32_t> indices;
    Material material;
    std::string name;
    
    // Bounding information
    Vector3 boundingBoxMin;
    Vector3 boundingBoxMax;
    Vector3 boundingSphereCenter;
    float boundingSphereRadius;
    
    // Metal resources
    bool CreateMetalBuffers(void* device);
    void DestroyMetalBuffers();

    void* GetVertexBuffer() const { return m_vertexBuffer; }
    void* GetIndexBuffer() const { return m_indexBuffer; }
    size_t GetVertexCount() const { return vertices.size(); }
    size_t GetIndexCount() const { return indices.size(); }
    
    // Mesh operations
    void CalculateBounds();
    void CalculateNormals();
    void CalculateTangents();
    void OptimizeIndices();
    
private:
    void* m_vertexBuffer;
    void* m_indexBuffer;
    void* m_device;
};

// ============================================================================
// Animation Support
// ============================================================================

struct Bone {
    std::string name;
    int32_t parentIndex;
    Matrix4 offsetMatrix;
    Matrix4 localTransform;
};

struct AnimationChannel {
    std::string boneName;
    std::vector<float> positionTimes;
    std::vector<Vector3> positions;
    std::vector<float> rotationTimes;
    std::vector<Quaternion> rotations;
    std::vector<float> scaleTimes;
    std::vector<Vector3> scales;
};

struct Animation {
    std::string name;
    float duration;
    float ticksPerSecond;
    std::vector<AnimationChannel> channels;
};

// ============================================================================
// Model Asset
// ============================================================================

class ModelAsset : public Asset {
public:
    ModelAsset(const std::string& path);
    virtual ~ModelAsset();
    
    // Asset interface
    bool Load() override;
    void Unload() override;
    
    // Model data access
    const std::vector<std::shared_ptr<Mesh>>& GetMeshes() const { return m_meshes; }
    const std::vector<Material>& GetMaterials() const { return m_materials; }
    const std::vector<Bone>& GetBones() const { return m_bones; }
    const std::vector<Animation>& GetAnimations() const { return m_animations; }
    
    // Bounding information
    const Vector3& GetBoundingBoxMin() const { return m_boundingBoxMin; }
    const Vector3& GetBoundingBoxMax() const { return m_boundingBoxMax; }
    const Vector3& GetBoundingSphereCenter() const { return m_boundingSphereCenter; }
    float GetBoundingSphereRadius() const { return m_boundingSphereRadius; }
    
    // Animation queries
    const Animation* FindAnimation(const std::string& name) const;
    int32_t FindBoneIndex(const std::string& name) const;
    
    // Rendering support
    bool CreateMetalResources(void* device);
    void DestroyMetalResources();
    
    // Model optimization
    void OptimizeMeshes();
    void MergeMeshes();
    void GenerateLODs(const std::vector<float>& lodDistances);

private:
    // Model data
    std::vector<std::shared_ptr<Mesh>> m_meshes;
    std::vector<Material> m_materials;
    std::vector<Bone> m_bones;
    std::vector<Animation> m_animations;
    
    // Bounding information
    Vector3 m_boundingBoxMin;
    Vector3 m_boundingBoxMax;
    Vector3 m_boundingSphereCenter;
    float m_boundingSphereRadius;
    
    // Metal resources
    void* m_device;
    
    // Loading helpers
    bool LoadGLTF();
    bool LoadOBJ();
    bool LoadFBX();
    bool LoadAssimpModel();
    
    void CalculateModelBounds();
    void ProcessMaterials();
    void ProcessAnimations();
    
    void CalculateMemoryUsage();
};

// ============================================================================
// Model Loader
// ============================================================================

class ModelLoader : public AssetLoader {
public:
    ModelLoader(void* device);
    
    std::shared_ptr<Asset> LoadAsset(const std::string& path) override;
    std::vector<std::string> GetSupportedExtensions() const override;
    AssetType GetAssetType() const override { return AssetType::Model; }

private:
    void* m_device;
};

// ============================================================================
// Primitive Generators
// ============================================================================

namespace PrimitiveGenerator {
    
    std::shared_ptr<Mesh> CreateCube(float size = 1.0f);
    std::shared_ptr<Mesh> CreateSphere(float radius = 1.0f, uint32_t segments = 32);
    std::shared_ptr<Mesh> CreateCylinder(float radius = 1.0f, float height = 2.0f, uint32_t segments = 32);
    std::shared_ptr<Mesh> CreateCone(float radius = 1.0f, float height = 2.0f, uint32_t segments = 32);
    std::shared_ptr<Mesh> CreatePlane(float width = 1.0f, float height = 1.0f, uint32_t widthSegments = 1, uint32_t heightSegments = 1);
    std::shared_ptr<Mesh> CreateTorus(float majorRadius = 1.0f, float minorRadius = 0.3f, uint32_t majorSegments = 32, uint32_t minorSegments = 16);
    
    // Utility functions
    void AddQuad(std::vector<Vertex>& vertices, std::vector<uint32_t>& indices,
                 const Vector3& v0, const Vector3& v1, const Vector3& v2, const Vector3& v3,
                 const Vector3& normal, const Vector2& uv0, const Vector2& uv1, 
                 const Vector2& uv2, const Vector2& uv3);
    
} // namespace PrimitiveGenerator

// ============================================================================
// Model Cache
// ============================================================================

class ModelCache {
public:
    static ModelCache& GetInstance();
    
    void Initialize(void* device);
    void Shutdown();
    
    // Primitive models
    AssetHandle<ModelAsset> GetCube();
    AssetHandle<ModelAsset> GetSphere();
    AssetHandle<ModelAsset> GetPlane();
    AssetHandle<ModelAsset> GetCylinder();
    
    // Model loading
    AssetHandle<ModelAsset> LoadModel(const std::string& path);
    
    // Model creation
    AssetHandle<ModelAsset> CreateFromMesh(std::shared_ptr<Mesh> mesh, const std::string& name);

private:
    ModelCache() = default;
    ~ModelCache() = default;
    ModelCache(const ModelCache&) = delete;
    ModelCache& operator=(const ModelCache&) = delete;
    
    void* m_device;
    
    // Primitive models
    AssetHandle<ModelAsset> m_cube;
    AssetHandle<ModelAsset> m_sphere;
    AssetHandle<ModelAsset> m_plane;
    AssetHandle<ModelAsset> m_cylinder;
    
    void CreatePrimitiveModels();
};

} // namespace Assets
} // namespace Engine
