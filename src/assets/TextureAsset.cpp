//
// TextureAsset.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Texture asset implementation
//

#include "TextureAsset.h"
#include <iostream>
#include <fstream>

namespace Engine {
namespace Assets {

// ============================================================================
// TextureAsset Implementation
// ============================================================================

TextureAsset::TextureAsset(const std::string& path)
    : Asset(path, AssetType::Texture)
    , m_metalTexture(nullptr)
    , m_device(nullptr)
    , m_width(0)
    , m_height(0)
    , m_depth(1)
    , m_mipLevels(1)
    , m_format(TextureFormat::RGBA8)
    , m_type(TextureType::Texture2D) {
}

TextureAsset::~TextureAsset() {
    Unload();
}

bool TextureAsset::Load() {
    if (m_loaded) {
        return true;
    }
    
    std::cout << "📷 Loading texture: " << m_path << std::endl;
    
    // For now, create a simple placeholder texture without Metal
    m_width = 2;
    m_height = 2;
    m_format = TextureFormat::RGBA8;

    // Create a placeholder texture (no actual Metal texture for now)
    m_metalTexture = nullptr;
    
    CalculateMemoryUsage();
    m_loaded = true;
    
    std::cout << "✅ Texture loaded: " << m_width << "x" << m_height << std::endl;
    return true;
}

void TextureAsset::Unload() {
    if (!m_loaded) {
        return;
    }
    
    if (m_metalTexture) {
        m_metalTexture = nullptr;
    }
    
    m_loaded = false;
    m_memoryUsage = 0;
    
    std::cout << "🗑️ Texture unloaded: " << m_path << std::endl;
}

bool TextureAsset::GenerateMipmaps() {
    // TODO: Implement mipmap generation
    return false;
}

bool TextureAsset::Resize(uint32_t width, uint32_t height) {
    // TODO: Implement texture resizing
    return false;
}

std::shared_ptr<TextureAsset> TextureAsset::CreateFromData(
    const void* data, 
    uint32_t width, 
    uint32_t height, 
    TextureFormat format,
    const std::string& name) {
    
    auto texture = std::make_shared<TextureAsset>(name);
    texture->m_width = width;
    texture->m_height = height;
    texture->m_format = format;
    
    // TODO: Implement data-based texture creation
    
    return texture;
}

std::shared_ptr<TextureAsset> TextureAsset::CreateRenderTarget(
    uint32_t width, 
    uint32_t height, 
    TextureFormat format,
    bool withDepth,
    const std::string& name) {
    
    auto texture = std::make_shared<TextureAsset>(name);
    texture->m_width = width;
    texture->m_height = height;
    texture->m_format = format;
    
    // TODO: Implement render target creation
    
    return texture;
}

std::shared_ptr<TextureAsset> TextureAsset::CreateCubemap(
    const std::vector<std::string>& facePaths,
    const std::string& name) {
    
    auto texture = std::make_shared<TextureAsset>(name);
    texture->m_type = TextureType::TextureCube;
    
    // TODO: Implement cubemap creation
    
    return texture;
}

int TextureAsset::GetMetalPixelFormat(TextureFormat format) const {
    // Return placeholder values for now (would be MTLPixelFormat values)
    switch (format) {
        case TextureFormat::RGBA8:      return 70;  // MTLPixelFormatRGBA8Unorm
        case TextureFormat::RGBA16F:    return 115; // MTLPixelFormatRGBA16Float
        case TextureFormat::RGBA32F:    return 125; // MTLPixelFormatRGBA32Float
        case TextureFormat::RGB8:       return 70;  // No RGB8 in Metal, use RGBA8
        case TextureFormat::RG8:        return 30;  // MTLPixelFormatRG8Unorm
        case TextureFormat::R8:         return 10;  // MTLPixelFormatR8Unorm
        case TextureFormat::Depth32F:   return 252; // MTLPixelFormatDepth32Float
        default:                        return 70;  // MTLPixelFormatRGBA8Unorm
    }
}

size_t TextureAsset::GetBytesPerPixel(TextureFormat format) const {
    switch (format) {
        case TextureFormat::RGBA8:      return 4;
        case TextureFormat::RGBA16F:    return 8;
        case TextureFormat::RGBA32F:    return 16;
        case TextureFormat::RGB8:       return 3;
        case TextureFormat::RG8:        return 2;
        case TextureFormat::R8:         return 1;
        case TextureFormat::Depth32F:   return 4;
        default:                        return 4;
    }
}

bool TextureAsset::IsCompressedFormat(TextureFormat format) const {
    switch (format) {
        case TextureFormat::BC1:
        case TextureFormat::BC3:
        case TextureFormat::BC5:
        case TextureFormat::BC7:
            return true;
        default:
            return false;
    }
}

void TextureAsset::CalculateMemoryUsage() {
    if (!IsCompressedFormat(m_format)) {
        m_memoryUsage = m_width * m_height * m_depth * GetBytesPerPixel(m_format);
    } else {
        // Compressed formats have different calculations
        m_memoryUsage = (m_width * m_height * m_depth) / 2; // Rough estimate
    }
    
    // Add mipmap memory if applicable
    if (m_mipLevels > 1) {
        m_memoryUsage = m_memoryUsage * 4 / 3; // Rough estimate for mipmap chain
    }
}

// ============================================================================
// TextureLoader Implementation
// ============================================================================

TextureLoader::TextureLoader(void* device)
    : m_device(device) {
}

std::shared_ptr<Asset> TextureLoader::LoadAsset(const std::string& path) {
    auto texture = std::make_shared<TextureAsset>(path);
    texture->SetDevice(m_device);
    
    if (texture->Load()) {
        return texture;
    }
    
    return nullptr;
}

std::vector<std::string> TextureLoader::GetSupportedExtensions() const {
    return {
        "png", "jpg", "jpeg", "tga", "bmp", "gif",
        "dds", "ktx", "hdr", "exr"
    };
}

// ============================================================================
// TextureCache Implementation
// ============================================================================

TextureCache& TextureCache::GetInstance() {
    static TextureCache instance;
    return instance;
}

void TextureCache::Initialize(void* device) {
    m_device = device;
    CreateDefaultTextures();
    
    std::cout << "✅ TextureCache initialized" << std::endl;
}

void TextureCache::Shutdown() {
    m_whiteTexture = AssetHandle<TextureAsset>();
    m_blackTexture = AssetHandle<TextureAsset>();
    m_normalTexture = AssetHandle<TextureAsset>();
    m_noiseTexture = AssetHandle<TextureAsset>();
    
    std::cout << "🔄 TextureCache shutdown" << std::endl;
}

AssetHandle<TextureAsset> TextureCache::GetWhiteTexture() {
    return m_whiteTexture;
}

AssetHandle<TextureAsset> TextureCache::GetBlackTexture() {
    return m_blackTexture;
}

AssetHandle<TextureAsset> TextureCache::GetNormalTexture() {
    return m_normalTexture;
}

AssetHandle<TextureAsset> TextureCache::GetNoiseTexture() {
    return m_noiseTexture;
}

AssetHandle<TextureAsset> TextureCache::LoadTexture(const std::string& path) {
    return AssetManager::GetInstance().LoadAsset<TextureAsset>(path);
}

AssetHandle<TextureAsset> TextureCache::LoadCubemap(const std::vector<std::string>& faces) {
    // TODO: Implement cubemap loading
    return AssetHandle<TextureAsset>();
}

AssetHandle<TextureAsset> TextureCache::CreateRenderTarget(
    const std::string& name,
    uint32_t width, 
    uint32_t height, 
    TextureFormat format) {
    
    // TODO: Implement render target creation
    return AssetHandle<TextureAsset>();
}

void TextureCache::CreateDefaultTextures() {
    // Create default textures using the asset manager
    auto& assetManager = AssetManager::GetInstance();
    
    // Register texture loader
    assetManager.RegisterLoader(std::make_unique<TextureLoader>(m_device));
    
    // Load default textures (these will be created as placeholders)
    m_whiteTexture = assetManager.LoadAsset<TextureAsset>("default_white");
    m_blackTexture = assetManager.LoadAsset<TextureAsset>("default_black");
    m_normalTexture = assetManager.LoadAsset<TextureAsset>("default_normal");
    m_noiseTexture = assetManager.LoadAsset<TextureAsset>("default_noise");
}

std::shared_ptr<TextureAsset> TextureCache::CreateSolidColorTexture(
    uint8_t r, uint8_t g, uint8_t b, uint8_t a,
    const std::string& name) {

    auto texture = std::make_shared<TextureAsset>(name);
    texture->SetDevice(m_device);
    
    // TODO: Implement solid color texture creation
    
    return texture;
}

// ============================================================================
// TextureUtils Implementation
// ============================================================================

namespace TextureUtils {

TextureFormat GetFormatFromString(const std::string& formatStr) {
    if (formatStr == "RGBA8") return TextureFormat::RGBA8;
    if (formatStr == "RGBA16F") return TextureFormat::RGBA16F;
    if (formatStr == "RGBA32F") return TextureFormat::RGBA32F;
    if (formatStr == "RGB8") return TextureFormat::RGB8;
    if (formatStr == "RG8") return TextureFormat::RG8;
    if (formatStr == "R8") return TextureFormat::R8;
    if (formatStr == "Depth32F") return TextureFormat::Depth32F;
    return TextureFormat::Unknown;
}

std::string GetStringFromFormat(TextureFormat format) {
    switch (format) {
        case TextureFormat::RGBA8:      return "RGBA8";
        case TextureFormat::RGBA16F:    return "RGBA16F";
        case TextureFormat::RGBA32F:    return "RGBA32F";
        case TextureFormat::RGB8:       return "RGB8";
        case TextureFormat::RG8:        return "RG8";
        case TextureFormat::R8:         return "R8";
        case TextureFormat::Depth32F:   return "Depth32F";
        default:                        return "Unknown";
    }
}

size_t CalculateTextureSize(uint32_t width, uint32_t height, uint32_t depth, 
                           TextureFormat format, uint32_t mipLevels) {
    // TODO: Implement proper texture size calculation
    return width * height * depth * 4; // Rough estimate
}

uint32_t CalculateMipLevels(uint32_t width, uint32_t height) {
    uint32_t levels = 1;
    uint32_t size = std::max(width, height);
    
    while (size > 1) {
        size /= 2;
        levels++;
    }
    
    return levels;
}

bool IsValidTextureSize(uint32_t width, uint32_t height) {
    return width > 0 && height > 0 && width <= 16384 && height <= 16384;
}

bool IsPowerOfTwo(uint32_t value) {
    return value > 0 && (value & (value - 1)) == 0;
}

void ConvertSRGBToLinear(float* data, size_t pixelCount) {
    for (size_t i = 0; i < pixelCount * 3; i += 3) {
        data[i] = std::pow(data[i], 2.2f);
        data[i + 1] = std::pow(data[i + 1], 2.2f);
        data[i + 2] = std::pow(data[i + 2], 2.2f);
    }
}

void ConvertLinearToSRGB(float* data, size_t pixelCount) {
    for (size_t i = 0; i < pixelCount * 3; i += 3) {
        data[i] = std::pow(data[i], 1.0f / 2.2f);
        data[i + 1] = std::pow(data[i + 1], 1.0f / 2.2f);
        data[i + 2] = std::pow(data[i + 2], 1.0f / 2.2f);
    }
}

} // namespace TextureUtils

} // namespace Assets
} // namespace Engine
