//
// TextureAsset.h
// macOS 3D Looter-Shooter Game Engine
//
// Texture asset management for Metal rendering
//

#pragma once

#include "AssetManager.h"

// Forward declarations for Metal types
#ifdef __OBJC__
#import <Metal/Metal.h>
#import <MetalKit/MetalKit.h>
#else
typedef void* id;
#endif

namespace Engine {
namespace Assets {

// ============================================================================
// Texture Format Support
// ============================================================================

enum class TextureFormat {
    Unknown = 0,
    RGBA8,
    RGBA16F,
    RGBA32F,
    RGB8,
    RG8,
    R8,
    Depth32F,
    BC1,    // DXT1
    BC3,    // DXT5
    BC5,    // Normal maps
    BC7     // High quality
};

enum class TextureType {
    Texture2D = 0,
    TextureCube,
    Texture3D,
    TextureArray
};

// ============================================================================
// Texture Asset
// ============================================================================

class TextureAsset : public Asset {
public:
    TextureAsset(const std::string& path);
    virtual ~TextureAsset();
    
    // Asset interface
    bool Load() override;
    void Unload() override;
    
    // Texture properties
    void* GetMetalTexture() const { return m_metalTexture; }
    uint32_t GetWidth() const { return m_width; }
    uint32_t GetHeight() const { return m_height; }
    uint32_t GetDepth() const { return m_depth; }
    uint32_t GetMipLevels() const { return m_mipLevels; }
    TextureFormat GetFormat() const { return m_format; }
    TextureType GetTextureType() const { return m_type; }
    
    // Texture operations
    bool GenerateMipmaps();
    bool Resize(uint32_t width, uint32_t height);

    // Metal device management
    void SetDevice(void* device) { m_device = device; }
    
    // Static creation methods
    static std::shared_ptr<TextureAsset> CreateFromData(
        const void* data, 
        uint32_t width, 
        uint32_t height, 
        TextureFormat format = TextureFormat::RGBA8,
        const std::string& name = "Generated");
    
    static std::shared_ptr<TextureAsset> CreateRenderTarget(
        uint32_t width, 
        uint32_t height, 
        TextureFormat format = TextureFormat::RGBA8,
        bool withDepth = false,
        const std::string& name = "RenderTarget");
    
    static std::shared_ptr<TextureAsset> CreateCubemap(
        const std::vector<std::string>& facePaths,
        const std::string& name = "Cubemap");

private:
    // Metal resources (stored as void* to avoid Metal includes in header)
    void* m_metalTexture;
    void* m_device;
    
    // Texture properties
    uint32_t m_width;
    uint32_t m_height;
    uint32_t m_depth;
    uint32_t m_mipLevels;
    TextureFormat m_format;
    TextureType m_type;
    
    // Loading helpers
    bool LoadFromFile();
    bool LoadDDS();
    bool LoadKTX();
    bool LoadSTB(); // For common formats (PNG, JPG, etc.)
    
    int GetMetalPixelFormat(TextureFormat format) const;
    size_t GetBytesPerPixel(TextureFormat format) const;
    bool IsCompressedFormat(TextureFormat format) const;
    
    void CalculateMemoryUsage();
};

// ============================================================================
// Texture Loader
// ============================================================================

class TextureLoader : public AssetLoader {
public:
    TextureLoader(void* device);
    
    std::shared_ptr<Asset> LoadAsset(const std::string& path) override;
    std::vector<std::string> GetSupportedExtensions() const override;
    AssetType GetAssetType() const override { return AssetType::Texture; }

private:
    void* m_device;
};

// ============================================================================
// Texture Cache
// ============================================================================

class TextureCache {
public:
    static TextureCache& GetInstance();
    
    void Initialize(void* device);
    void Shutdown();
    
    // Common textures
    AssetHandle<TextureAsset> GetWhiteTexture();
    AssetHandle<TextureAsset> GetBlackTexture();
    AssetHandle<TextureAsset> GetNormalTexture();
    AssetHandle<TextureAsset> GetNoiseTexture();
    
    // Texture utilities
    AssetHandle<TextureAsset> LoadTexture(const std::string& path);
    AssetHandle<TextureAsset> LoadCubemap(const std::vector<std::string>& faces);
    
    // Render targets
    AssetHandle<TextureAsset> CreateRenderTarget(
        const std::string& name,
        uint32_t width, 
        uint32_t height, 
        TextureFormat format = TextureFormat::RGBA8);

private:
    TextureCache() = default;
    ~TextureCache() = default;
    TextureCache(const TextureCache&) = delete;
    TextureCache& operator=(const TextureCache&) = delete;
    
    void* m_device;
    
    // Default textures
    AssetHandle<TextureAsset> m_whiteTexture;
    AssetHandle<TextureAsset> m_blackTexture;
    AssetHandle<TextureAsset> m_normalTexture;
    AssetHandle<TextureAsset> m_noiseTexture;
    
    void CreateDefaultTextures();
    std::shared_ptr<TextureAsset> CreateSolidColorTexture(
        uint8_t r, uint8_t g, uint8_t b, uint8_t a, 
        const std::string& name);
};

// ============================================================================
// Texture Utilities
// ============================================================================

namespace TextureUtils {
    
    // Format conversion
    TextureFormat GetFormatFromString(const std::string& formatStr);
    std::string GetStringFromFormat(TextureFormat format);
    
    // Size calculations
    size_t CalculateTextureSize(uint32_t width, uint32_t height, uint32_t depth, 
                               TextureFormat format, uint32_t mipLevels = 1);
    
    uint32_t CalculateMipLevels(uint32_t width, uint32_t height);
    
    // Validation
    bool IsValidTextureSize(uint32_t width, uint32_t height);
    bool IsPowerOfTwo(uint32_t value);
    
    // Color space conversion
    void ConvertSRGBToLinear(float* data, size_t pixelCount);
    void ConvertLinearToSRGB(float* data, size_t pixelCount);
    
} // namespace TextureUtils

} // namespace Assets
} // namespace Engine
