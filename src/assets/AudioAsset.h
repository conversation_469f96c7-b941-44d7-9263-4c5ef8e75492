//
// AudioAsset.h
// macOS 3D Looter-Shooter Game Engine
//
// Audio asset management with Core Audio support
//

#pragma once

#include "AssetManager.h"
#include "engine/Types.h"
#include <AudioToolbox/AudioToolbox.h>
#include <AVFoundation/AVFoundation.h>
#include <vector>
#include <memory>

namespace Engine {
namespace Assets {

// ============================================================================
// Audio Format Support
// ============================================================================

enum class AudioFormat {
    Unknown = 0,
    PCM16,      // 16-bit PCM
    PCM24,      // 24-bit PCM
    PCM32,      // 32-bit PCM
    Float32,    // 32-bit float
    MP3,        // MPEG Layer 3
    AAC,        // Advanced Audio Coding
    OGG,        // Ogg Vorbis
    FLAC,       // Free Lossless Audio Codec
    WAV,        // Wave format
    AIFF        // Audio Interchange File Format
};

enum class AudioType {
    Effect = 0,     // Short sound effects
    Music,          // Background music
    Voice,          // Voice/dialog
    Ambient         // Ambient sounds
};

// ============================================================================
// Audio Buffer
// ============================================================================

class AudioBuffer {
public:
    AudioBuffer();
    ~AudioBuffer();
    
    // Buffer properties
    uint32_t GetSampleRate() const { return m_sampleRate; }
    uint32_t GetChannels() const { return m_channels; }
    uint32_t GetFrameCount() const { return m_frameCount; }
    float GetDuration() const { return static_cast<float>(m_frameCount) / m_sampleRate; }
    AudioFormat GetFormat() const { return m_format; }
    
    // Data access
    const void* GetData() const { return m_data; }
    size_t GetDataSize() const { return m_dataSize; }
    
    // Buffer operations
    bool LoadFromFile(const std::string& path);
    bool LoadFromMemory(const void* data, size_t size, AudioFormat format);
    void Clear();
    
    // Audio processing
    bool ConvertToFormat(AudioFormat targetFormat);
    bool Resample(uint32_t targetSampleRate);
    bool ConvertToMono();
    bool ConvertToStereo();
    
    // Analysis
    float GetPeakAmplitude() const;
    float GetRMSAmplitude() const;
    std::vector<float> GetSpectrum(uint32_t fftSize = 1024) const;

private:
    void* m_data;
    size_t m_dataSize;
    uint32_t m_sampleRate;
    uint32_t m_channels;
    uint32_t m_frameCount;
    AudioFormat m_format;
    
    // Core Audio resources
    AudioFileID m_audioFile;
    ExtAudioFileRef m_extAudioFile;
    
    bool LoadWithCoreAudio(const std::string& path);
    bool LoadWithAVFoundation(const std::string& path);
    void CalculateFrameCount();
    size_t GetBytesPerFrame() const;
};

// ============================================================================
// Audio Asset
// ============================================================================

class AudioAsset : public Asset {
public:
    AudioAsset(const std::string& path, AudioType type = AudioType::Effect);
    virtual ~AudioAsset();
    
    // Asset interface
    bool Load() override;
    void Unload() override;
    
    // Audio properties
    const AudioBuffer& GetBuffer() const { return m_buffer; }
    AudioType GetAudioType() const { return m_audioType; }
    float GetVolume() const { return m_volume; }
    float getPitch() const { return m_pitch; }
    bool IsLooping() const { return m_looping; }
    
    // Audio settings
    void SetVolume(float volume) { m_volume = std::clamp(volume, 0.0f, 1.0f); }
    void SetPitch(float pitch) { m_pitch = std::clamp(pitch, 0.1f, 4.0f); }
    void SetLooping(bool looping) { m_looping = looping; }
    
    // 3D audio properties
    void Set3D(bool is3D) { m_is3D = is3D; }
    bool Is3D() const { return m_is3D; }
    void SetAttenuationDistance(float minDistance, float maxDistance);
    float GetMinDistance() const { return m_minDistance; }
    float GetMaxDistance() const { return m_maxDistance; }
    
    // Streaming support
    bool IsStreaming() const { return m_streaming; }
    void SetStreaming(bool streaming) { m_streaming = streaming; }
    
    // Audio analysis
    float GetDuration() const { return m_buffer.GetDuration(); }
    uint32_t GetSampleRate() const { return m_buffer.GetSampleRate(); }
    uint32_t GetChannels() const { return m_buffer.GetChannels(); }

private:
    AudioBuffer m_buffer;
    AudioType m_audioType;
    
    // Playback properties
    float m_volume;
    float m_pitch;
    bool m_looping;
    
    // 3D audio properties
    bool m_is3D;
    float m_minDistance;
    float m_maxDistance;
    
    // Streaming
    bool m_streaming;
    
    void CalculateMemoryUsage();
};

// ============================================================================
// Audio Loader
// ============================================================================

class AudioLoader : public AssetLoader {
public:
    AudioLoader();
    
    std::shared_ptr<Asset> LoadAsset(const std::string& path) override;
    std::vector<std::string> GetSupportedExtensions() const override;
    AssetType GetAssetType() const override { return AssetType::Audio; }

private:
    AudioType DetermineAudioType(const std::string& path) const;
};

// ============================================================================
// Audio Bank
// ============================================================================

class AudioBank {
public:
    AudioBank(const std::string& name);
    ~AudioBank();
    
    // Bank management
    const std::string& GetName() const { return m_name; }
    bool LoadFromFile(const std::string& path);
    void Unload();
    
    // Audio access
    AssetHandle<AudioAsset> GetAudio(const std::string& name) const;
    std::vector<std::string> GetAudioNames() const;
    
    // Bank properties
    size_t GetAudioCount() const { return m_audioAssets.size(); }
    size_t GetMemoryUsage() const;
    
    // Preloading
    void PreloadAll();
    void UnloadAll();

private:
    std::string m_name;
    std::unordered_map<std::string, AssetHandle<AudioAsset>> m_audioAssets;
};

// ============================================================================
// Audio Cache
// ============================================================================

class AudioCache {
public:
    static AudioCache& GetInstance();
    
    void Initialize();
    void Shutdown();
    
    // Audio loading
    AssetHandle<AudioAsset> LoadAudio(const std::string& path, AudioType type = AudioType::Effect);
    AssetHandle<AudioAsset> LoadMusic(const std::string& path);
    AssetHandle<AudioAsset> LoadVoice(const std::string& path);
    
    // Audio banks
    std::shared_ptr<AudioBank> LoadAudioBank(const std::string& path);
    std::shared_ptr<AudioBank> GetAudioBank(const std::string& name) const;
    void UnloadAudioBank(const std::string& name);
    
    // Memory management
    void SetMemoryBudget(size_t budgetBytes);
    void PreloadAudioType(AudioType type);
    void UnloadAudioType(AudioType type);
    
    // Audio utilities
    AssetHandle<AudioAsset> CreateSilence(float duration, uint32_t sampleRate = 44100);
    AssetHandle<AudioAsset> CreateTone(float frequency, float duration, uint32_t sampleRate = 44100);
    AssetHandle<AudioAsset> CreateNoise(float duration, uint32_t sampleRate = 44100);

private:
    AudioCache() = default;
    ~AudioCache() = default;
    AudioCache(const AudioCache&) = delete;
    AudioCache& operator=(const AudioCache&) = delete;
    
    std::unordered_map<std::string, std::shared_ptr<AudioBank>> m_audioBanks;
    size_t m_memoryBudget;
};

// ============================================================================
// Audio Utilities
// ============================================================================

namespace AudioUtils {
    
    // Format conversion
    AudioFormat GetFormatFromString(const std::string& formatStr);
    std::string GetStringFromFormat(AudioFormat format);
    AudioFormat GetFormatFromExtension(const std::string& extension);
    
    // Audio calculations
    size_t CalculateAudioSize(uint32_t sampleRate, uint32_t channels, 
                             float duration, AudioFormat format);
    
    uint32_t GetBytesPerSample(AudioFormat format);
    bool IsCompressedFormat(AudioFormat format);
    bool IsLosslessFormat(AudioFormat format);
    
    // Audio processing
    void NormalizeAudio(float* data, size_t sampleCount);
    void ApplyFade(float* data, size_t sampleCount, float fadeInTime, float fadeOutTime, uint32_t sampleRate);
    void MixAudio(const float* source1, const float* source2, float* dest, 
                  size_t sampleCount, float volume1 = 1.0f, float volume2 = 1.0f);
    
    // 3D audio calculations
    float CalculateAttenuation(float distance, float minDistance, float maxDistance);
    float CalculateDopplerShift(const Vector3& sourceVel, const Vector3& listenerVel, 
                               const Vector3& direction, float speedOfSound = 343.0f);
    
    // File validation
    bool IsValidAudioFile(const std::string& path);
    bool GetAudioFileInfo(const std::string& path, uint32_t& sampleRate, 
                         uint32_t& channels, float& duration);
    
} // namespace AudioUtils

} // namespace Assets
} // namespace Engine
