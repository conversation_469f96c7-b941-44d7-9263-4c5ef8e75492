//
// AssetManager.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Asset management system implementation
//

#include "AssetManager.h"
#include <filesystem>
#include <iostream>
#include <algorithm>
#include <fstream>

namespace Engine {
namespace Assets {

// ============================================================================
// Asset Base Class Implementation
// ============================================================================

Asset::Asset(const std::string& path, AssetType type)
    : m_path(path)
    , m_type(type)
    , m_loaded(false)
    , m_memoryUsage(0)
    , m_lastAccessed(std::chrono::steady_clock::now()) {
}

bool Asset::Reload() {
    if (m_loaded) {
        Unload();
    }
    return Load();
}

void Asset::UpdateLastAccessed() {
    m_lastAccessed = std::chrono::steady_clock::now();
}

// ============================================================================
// AssetManager Static Members
// ============================================================================

AssetID AssetManager::s_nextAssetID = 1;

// ============================================================================
// AssetManager Implementation
// ============================================================================

AssetManager& AssetManager::GetInstance() {
    static AssetManager instance;
    return instance;
}

bool AssetManager::Initialize(const std::string& assetRootPath) {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    
    m_assetRootPath = assetRootPath;
    m_memoryBudget = 512 * 1024 * 1024; // 512MB default
    m_hotReloadingEnabled = false;
    
    // Reset statistics
    m_stats = {};
    
    // Ensure asset root path exists
    if (!std::filesystem::exists(m_assetRootPath)) {
        std::cerr << "Asset root path does not exist: " << m_assetRootPath << std::endl;
        return false;
    }
    
    std::cout << "✅ AssetManager initialized with root path: " << m_assetRootPath << std::endl;
    return true;
}

void AssetManager::Shutdown() {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    
    // Unload all assets
    for (auto& [id, entry] : m_assets) {
        if (entry.asset) {
            entry.asset->Unload();
        }
    }
    
    m_assets.clear();
    m_pathToId.clear();
    m_loaders.clear();
    
    std::cout << "🔄 AssetManager shutdown complete" << std::endl;
}

void AssetManager::UnloadAsset(AssetID id) {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    
    auto it = m_assets.find(id);
    if (it != m_assets.end()) {
        if (it->second.referenceCount > 0) {
            it->second.referenceCount--;
        }
        
        if (it->second.referenceCount == 0) {
            if (it->second.asset) {
                it->second.asset->Unload();
                m_stats.loadedAssets--;
            }
            
            // Remove from path mapping
            for (auto pathIt = m_pathToId.begin(); pathIt != m_pathToId.end(); ++pathIt) {
                if (pathIt->second == id) {
                    m_pathToId.erase(pathIt);
                    break;
                }
            }
            
            m_assets.erase(it);
        }
    }
}

void AssetManager::UnloadUnusedAssets() {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    
    std::vector<AssetID> toRemove;
    
    for (auto& [id, entry] : m_assets) {
        if (entry.weakRef.expired() || entry.referenceCount == 0) {
            toRemove.push_back(id);
        }
    }
    
    for (AssetID id : toRemove) {
        UnloadAsset(id);
    }
    
    std::cout << "🗑️ Unloaded " << toRemove.size() << " unused assets" << std::endl;
}

void AssetManager::ReloadAsset(AssetID id) {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    
    auto it = m_assets.find(id);
    if (it != m_assets.end() && it->second.asset) {
        it->second.asset->Reload();
        it->second.lastModified = GetFileModificationTime(it->second.asset->GetPath());
    }
}

void AssetManager::ReloadAllAssets() {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    
    for (auto& [id, entry] : m_assets) {
        if (entry.asset) {
            entry.asset->Reload();
            entry.lastModified = GetFileModificationTime(entry.asset->GetPath());
        }
    }
    
    std::cout << "🔄 Reloaded all assets" << std::endl;
}

void AssetManager::RegisterLoader(std::unique_ptr<AssetLoader> loader) {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    
    auto extensions = loader->GetSupportedExtensions();
    for (const auto& ext : extensions) {
        m_loaders[ext] = std::move(loader);
        std::cout << "📁 Registered loader for extension: " << ext << std::endl;
        break; // Move the loader only once
    }
}

std::vector<std::string> AssetManager::FindAssets(AssetType type) const {
    std::vector<std::string> results;
    
    try {
        for (const auto& entry : std::filesystem::recursive_directory_iterator(m_assetRootPath)) {
            if (entry.is_regular_file()) {
                std::string path = entry.path().string();
                AssetLoader* loader = FindLoader(path);
                if (loader && loader->GetAssetType() == type) {
                    results.push_back(path);
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "Error scanning assets: " << e.what() << std::endl;
    }
    
    return results;
}

std::vector<std::string> AssetManager::FindAssetsByExtension(const std::string& extension) const {
    std::vector<std::string> results;
    
    try {
        for (const auto& entry : std::filesystem::recursive_directory_iterator(m_assetRootPath)) {
            if (entry.is_regular_file()) {
                std::string path = entry.path().string();
                if (GetFileExtension(path) == extension) {
                    results.push_back(path);
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "Error scanning assets: " << e.what() << std::endl;
    }
    
    return results;
}

size_t AssetManager::GetTotalMemoryUsage() const {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    return GetTotalMemoryUsageInternal();
}

size_t AssetManager::GetTotalMemoryUsageInternal() const {
    size_t total = 0;
    for (const auto& [id, entry] : m_assets) {
        if (entry.asset) {
            total += entry.asset->GetMemoryUsage();
        }
    }

    return total;
}

void AssetManager::SetMemoryBudget(size_t budgetBytes) {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    m_memoryBudget = budgetBytes;
    
    std::cout << "💾 Memory budget set to " << (budgetBytes / 1024 / 1024) << " MB" << std::endl;
}

void AssetManager::GarbageCollect() {
    std::lock_guard<std::mutex> lock(m_assetMutex);

    size_t currentUsage = GetTotalMemoryUsageInternal();
    if (currentUsage <= m_memoryBudget) {
        return;
    }
    
    // Sort assets by last accessed time (oldest first)
    std::vector<std::pair<AssetID, std::chrono::steady_clock::time_point>> candidates;
    
    for (const auto& [id, entry] : m_assets) {
        if (entry.asset && entry.referenceCount == 0) {
            // Use a public method to get last accessed time
            candidates.emplace_back(id, std::chrono::steady_clock::now());
        }
    }
    
    std::sort(candidates.begin(), candidates.end(),
        [](const auto& a, const auto& b) { return a.second < b.second; });
    
    // Unload oldest assets until under budget
    for (const auto& [id, lastAccessed] : candidates) {
        UnloadAsset(id);
        currentUsage = GetTotalMemoryUsageInternal();
        if (currentUsage <= m_memoryBudget) {
            break;
        }
    }
    
    std::cout << "🗑️ Garbage collection complete. Memory usage: " 
              << (currentUsage / 1024 / 1024) << " MB" << std::endl;
}

void AssetManager::EnableHotReloading(bool enable) {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    m_hotReloadingEnabled = enable;
    
    std::cout << "🔥 Hot reloading " << (enable ? "enabled" : "disabled") << std::endl;
}

void AssetManager::CheckForFileChanges() {
    if (!m_hotReloadingEnabled) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_assetMutex);
    
    for (auto& [id, entry] : m_assets) {
        if (entry.asset) {
            auto currentModTime = GetFileModificationTime(entry.asset->GetPath());
            if (currentModTime > entry.lastModified) {
                std::cout << "🔄 Hot reloading: " << entry.asset->GetPath() << std::endl;
                entry.asset->Reload();
                entry.lastModified = currentModTime;
            }
        }
    }
}

AssetManager::Statistics AssetManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_assetMutex);

    Statistics stats = m_stats;
    stats.totalAssets = m_assets.size();
    stats.memoryUsage = GetTotalMemoryUsageInternal();
    
    return stats;
}

void AssetManager::ResetStatistics() {
    std::lock_guard<std::mutex> lock(m_assetMutex);
    m_stats = {};
}

// ============================================================================
// Private Methods
// ============================================================================

AssetID AssetManager::GenerateAssetID() {
    return s_nextAssetID++;
}

AssetLoader* AssetManager::FindLoader(const std::string& path) const {
    std::string extension = GetFileExtension(path);
    auto it = m_loaders.find(extension);
    return (it != m_loaders.end()) ? it->second.get() : nullptr;
}

std::string AssetManager::GetFileExtension(const std::string& path) const {
    size_t dotPos = path.find_last_of('.');
    if (dotPos != std::string::npos && dotPos < path.length() - 1) {
        return path.substr(dotPos + 1);
    }
    return "";
}

std::string AssetManager::ResolvePath(const std::string& path) const {
    if (path.empty()) {
        return "";
    }
    
    // If path is already absolute or starts with asset root, return as-is
    if (path[0] == '/' || path.find(m_assetRootPath) == 0) {
        return path;
    }
    
    // Otherwise, prepend asset root path
    return m_assetRootPath + "/" + path;
}

bool AssetManager::FileExists(const std::string& path) const {
    return std::filesystem::exists(path);
}

std::chrono::steady_clock::time_point AssetManager::GetFileModificationTime(const std::string& path) const {
    try {
        auto ftime = std::filesystem::last_write_time(path);
        auto sctp = std::chrono::time_point_cast<std::chrono::steady_clock::duration>(
            ftime - std::filesystem::file_time_type::clock::now() + std::chrono::steady_clock::now());
        return sctp;
    } catch (const std::filesystem::filesystem_error&) {
        return std::chrono::steady_clock::now();
    }
}

} // namespace Assets
} // namespace Engine
