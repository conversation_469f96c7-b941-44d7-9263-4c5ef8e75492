//
// ModelAsset.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Model asset implementation
//

#include "ModelAsset.h"
#include <iostream>
#include <algorithm>

namespace Engine {
namespace Assets {

// ============================================================================
// Mesh Implementation
// ============================================================================

Mesh::Mesh(const std::string& name)
    : name(name)
    , boundingBoxMin(0.0f, 0.0f, 0.0f)
    , boundingBoxMax(0.0f, 0.0f, 0.0f)
    , boundingSphereCenter(0.0f, 0.0f, 0.0f)
    , boundingSphereRadius(0.0f)
    , m_vertexBuffer(nullptr)
    , m_indexBuffer(nullptr)
    , m_device(nullptr) {
}

Mesh::~Mesh() {
    DestroyMetalBuffers();
}

bool Mesh::CreateMetalBuffers(void* device) {
    if (!device || vertices.empty()) {
        return false;
    }

    m_device = device;

    // For now, just create placeholder buffers without actual Metal calls
    m_vertexBuffer = nullptr; // Would be actual Metal buffer
    m_indexBuffer = nullptr;  // Would be actual Metal buffer

    std::cout << "✅ Metal buffers created for mesh: " << name
              << " (vertices: " << vertices.size()
              << ", indices: " << indices.size() << ")" << std::endl;

    return true;
}

void Mesh::DestroyMetalBuffers() {
    if (m_vertexBuffer) {
        m_vertexBuffer = nullptr;
    }
    if (m_indexBuffer) {
        m_indexBuffer = nullptr;
    }
    m_device = nullptr;
}

void Mesh::CalculateBounds() {
    if (vertices.empty()) {
        return;
    }
    
    boundingBoxMin = vertices[0].position;
    boundingBoxMax = vertices[0].position;
    
    for (const auto& vertex : vertices) {
        boundingBoxMin.x = std::min(boundingBoxMin.x, vertex.position.x);
        boundingBoxMin.y = std::min(boundingBoxMin.y, vertex.position.y);
        boundingBoxMin.z = std::min(boundingBoxMin.z, vertex.position.z);
        
        boundingBoxMax.x = std::max(boundingBoxMax.x, vertex.position.x);
        boundingBoxMax.y = std::max(boundingBoxMax.y, vertex.position.y);
        boundingBoxMax.z = std::max(boundingBoxMax.z, vertex.position.z);
    }
    
    // Calculate bounding sphere
    boundingSphereCenter = (boundingBoxMin + boundingBoxMax) * 0.5f;
    boundingSphereRadius = 0.0f;
    
    for (const auto& vertex : vertices) {
        Vector3 diff = vertex.position - boundingSphereCenter;
        float distance = sqrt(diff.x * diff.x + diff.y * diff.y + diff.z * diff.z);
        boundingSphereRadius = std::max(boundingSphereRadius, distance);
    }
}

void Mesh::CalculateNormals() {
    if (vertices.empty() || indices.empty()) {
        return;
    }
    
    // Reset normals
    for (auto& vertex : vertices) {
        vertex.normal = Vector3(0.0f, 0.0f, 0.0f);
    }
    
    // Calculate face normals and accumulate
    for (size_t i = 0; i < indices.size(); i += 3) {
        uint32_t i0 = indices[i];
        uint32_t i1 = indices[i + 1];
        uint32_t i2 = indices[i + 2];
        
        if (i0 >= vertices.size() || i1 >= vertices.size() || i2 >= vertices.size()) {
            continue;
        }
        
        Vector3 v0 = vertices[i0].position;
        Vector3 v1 = vertices[i1].position;
        Vector3 v2 = vertices[i2].position;
        
        Vector3 edge1 = v1 - v0;
        Vector3 edge2 = v2 - v0;
        
        // Cross product for face normal
        Vector3 normal;
        normal.x = edge1.y * edge2.z - edge1.z * edge2.y;
        normal.y = edge1.z * edge2.x - edge1.x * edge2.z;
        normal.z = edge1.x * edge2.y - edge1.y * edge2.x;
        
        // Accumulate normals
        vertices[i0].normal = vertices[i0].normal + normal;
        vertices[i1].normal = vertices[i1].normal + normal;
        vertices[i2].normal = vertices[i2].normal + normal;
    }
    
    // Normalize accumulated normals
    for (auto& vertex : vertices) {
        float length = sqrt(vertex.normal.x * vertex.normal.x + 
                           vertex.normal.y * vertex.normal.y + 
                           vertex.normal.z * vertex.normal.z);
        if (length > 0.0f) {
            vertex.normal.x /= length;
            vertex.normal.y /= length;
            vertex.normal.z /= length;
        }
    }
}

void Mesh::CalculateTangents() {
    // TODO: Implement tangent calculation for normal mapping
}

void Mesh::OptimizeIndices() {
    // TODO: Implement index optimization for better cache performance
}

// ============================================================================
// ModelAsset Implementation
// ============================================================================

ModelAsset::ModelAsset(const std::string& path)
    : Asset(path, AssetType::Model)
    , m_boundingBoxMin(0.0f, 0.0f, 0.0f)
    , m_boundingBoxMax(0.0f, 0.0f, 0.0f)
    , m_boundingSphereCenter(0.0f, 0.0f, 0.0f)
    , m_boundingSphereRadius(0.0f)
    , m_device(nullptr) {
}

ModelAsset::~ModelAsset() {
    Unload();
}

bool ModelAsset::Load() {
    if (m_loaded) {
        return true;
    }
    
    std::cout << "🎯 Loading model: " << m_path << std::endl;
    
    // For now, create a simple cube mesh as placeholder
    auto cubeMesh = PrimitiveGenerator::CreateCube(1.0f);
    if (cubeMesh) {
        m_meshes.push_back(cubeMesh);
        
        // Create a default material
        Material defaultMaterial;
        defaultMaterial.name = "DefaultMaterial";
        defaultMaterial.albedo = Vector3(0.8f, 0.8f, 0.8f);
        defaultMaterial.metallic = 0.0f;
        defaultMaterial.roughness = 0.5f;
        m_materials.push_back(defaultMaterial);
        
        cubeMesh->material = defaultMaterial;
        
        CalculateModelBounds();
        CalculateMemoryUsage();
        
        m_loaded = true;
        std::cout << "✅ Model loaded with " << m_meshes.size() << " meshes" << std::endl;
        return true;
    }
    
    std::cerr << "❌ Failed to load model: " << m_path << std::endl;
    return false;
}

void ModelAsset::Unload() {
    if (!m_loaded) {
        return;
    }
    
    DestroyMetalResources();
    
    m_meshes.clear();
    m_materials.clear();
    m_bones.clear();
    m_animations.clear();
    
    m_loaded = false;
    m_memoryUsage = 0;
    
    std::cout << "🗑️ Model unloaded: " << m_path << std::endl;
}

const Animation* ModelAsset::FindAnimation(const std::string& name) const {
    for (const auto& animation : m_animations) {
        if (animation.name == name) {
            return &animation;
        }
    }
    return nullptr;
}

int32_t ModelAsset::FindBoneIndex(const std::string& name) const {
    for (size_t i = 0; i < m_bones.size(); ++i) {
        if (m_bones[i].name == name) {
            return static_cast<int32_t>(i);
        }
    }
    return -1;
}

bool ModelAsset::CreateMetalResources(void* device) {
    if (!device) {
        return false;
    }
    
    m_device = device;
    
    for (auto& mesh : m_meshes) {
        if (!mesh->CreateMetalBuffers(device)) {
            std::cerr << "❌ Failed to create Metal buffers for mesh: " << mesh->name << std::endl;
            return false;
        }
    }
    
    std::cout << "✅ Metal resources created for model: " << m_path << std::endl;
    return true;
}

void ModelAsset::DestroyMetalResources() {
    for (auto& mesh : m_meshes) {
        mesh->DestroyMetalBuffers();
    }
    m_device = nullptr;
}

void ModelAsset::OptimizeMeshes() {
    for (auto& mesh : m_meshes) {
        mesh->OptimizeIndices();
    }
}

void ModelAsset::MergeMeshes() {
    // TODO: Implement mesh merging for performance
}

void ModelAsset::GenerateLODs(const std::vector<float>& lodDistances) {
    // TODO: Implement LOD generation
}

void ModelAsset::CalculateModelBounds() {
    if (m_meshes.empty()) {
        return;
    }
    
    bool first = true;
    for (const auto& mesh : m_meshes) {
        if (mesh->vertices.empty()) {
            continue;
        }
        
        mesh->CalculateBounds();
        
        if (first) {
            m_boundingBoxMin = mesh->boundingBoxMin;
            m_boundingBoxMax = mesh->boundingBoxMax;
            first = false;
        } else {
            m_boundingBoxMin.x = std::min(m_boundingBoxMin.x, mesh->boundingBoxMin.x);
            m_boundingBoxMin.y = std::min(m_boundingBoxMin.y, mesh->boundingBoxMin.y);
            m_boundingBoxMin.z = std::min(m_boundingBoxMin.z, mesh->boundingBoxMin.z);
            
            m_boundingBoxMax.x = std::max(m_boundingBoxMax.x, mesh->boundingBoxMax.x);
            m_boundingBoxMax.y = std::max(m_boundingBoxMax.y, mesh->boundingBoxMax.y);
            m_boundingBoxMax.z = std::max(m_boundingBoxMax.z, mesh->boundingBoxMax.z);
        }
    }
    
    // Calculate model bounding sphere
    m_boundingSphereCenter = (m_boundingBoxMin + m_boundingBoxMax) * 0.5f;
    Vector3 extent = m_boundingBoxMax - m_boundingBoxMin;
    m_boundingSphereRadius = sqrt(extent.x * extent.x + extent.y * extent.y + extent.z * extent.z) * 0.5f;
}

void ModelAsset::ProcessMaterials() {
    // TODO: Process and validate materials
}

void ModelAsset::ProcessAnimations() {
    // TODO: Process animation data
}

void ModelAsset::CalculateMemoryUsage() {
    m_memoryUsage = 0;
    
    for (const auto& mesh : m_meshes) {
        m_memoryUsage += mesh->vertices.size() * sizeof(Vertex);
        m_memoryUsage += mesh->indices.size() * sizeof(uint32_t);
    }
    
    // Add material memory usage
    m_memoryUsage += m_materials.size() * sizeof(Material);
    
    // Add bone and animation memory usage
    m_memoryUsage += m_bones.size() * sizeof(Bone);
    for (const auto& animation : m_animations) {
        for (const auto& channel : animation.channels) {
            m_memoryUsage += channel.positions.size() * sizeof(Vector3);
            m_memoryUsage += channel.rotations.size() * sizeof(Quaternion);
            m_memoryUsage += channel.scales.size() * sizeof(Vector3);
        }
    }
}

// ============================================================================
// ModelLoader Implementation
// ============================================================================

ModelLoader::ModelLoader(void* device)
    : m_device(device) {
}

std::shared_ptr<Asset> ModelLoader::LoadAsset(const std::string& path) {
    auto model = std::make_shared<ModelAsset>(path);
    
    if (model->Load()) {
        model->CreateMetalResources(m_device);
        return model;
    }
    
    return nullptr;
}

std::vector<std::string> ModelLoader::GetSupportedExtensions() const {
    return {
        "obj", "fbx", "gltf", "glb", "dae", "3ds", "ply", "stl"
    };
}

// ============================================================================
// PrimitiveGenerator Implementation
// ============================================================================

namespace PrimitiveGenerator {

std::shared_ptr<Mesh> CreateCube(float size) {
    auto mesh = std::make_shared<Mesh>("Cube");
    
    float halfSize = size * 0.5f;
    
    // Define cube vertices (24 vertices for proper normals)
    std::vector<Vector3> positions = {
        // Front face
        {-halfSize, -halfSize,  halfSize}, { halfSize, -halfSize,  halfSize},
        { halfSize,  halfSize,  halfSize}, {-halfSize,  halfSize,  halfSize},
        // Back face
        {-halfSize, -halfSize, -halfSize}, {-halfSize,  halfSize, -halfSize},
        { halfSize,  halfSize, -halfSize}, { halfSize, -halfSize, -halfSize},
        // Top face
        {-halfSize,  halfSize, -halfSize}, {-halfSize,  halfSize,  halfSize},
        { halfSize,  halfSize,  halfSize}, { halfSize,  halfSize, -halfSize},
        // Bottom face
        {-halfSize, -halfSize, -halfSize}, { halfSize, -halfSize, -halfSize},
        { halfSize, -halfSize,  halfSize}, {-halfSize, -halfSize,  halfSize},
        // Right face
        { halfSize, -halfSize, -halfSize}, { halfSize,  halfSize, -halfSize},
        { halfSize,  halfSize,  halfSize}, { halfSize, -halfSize,  halfSize},
        // Left face
        {-halfSize, -halfSize, -halfSize}, {-halfSize, -halfSize,  halfSize},
        {-halfSize,  halfSize,  halfSize}, {-halfSize,  halfSize, -halfSize}
    };
    
    std::vector<Vector3> normals = {
        // Front face
        {0, 0, 1}, {0, 0, 1}, {0, 0, 1}, {0, 0, 1},
        // Back face
        {0, 0, -1}, {0, 0, -1}, {0, 0, -1}, {0, 0, -1},
        // Top face
        {0, 1, 0}, {0, 1, 0}, {0, 1, 0}, {0, 1, 0},
        // Bottom face
        {0, -1, 0}, {0, -1, 0}, {0, -1, 0}, {0, -1, 0},
        // Right face
        {1, 0, 0}, {1, 0, 0}, {1, 0, 0}, {1, 0, 0},
        // Left face
        {-1, 0, 0}, {-1, 0, 0}, {-1, 0, 0}, {-1, 0, 0}
    };
    
    std::vector<Vector2> texCoords = {
        // Front face
        {0, 0}, {1, 0}, {1, 1}, {0, 1},
        // Back face
        {1, 0}, {1, 1}, {0, 1}, {0, 0},
        // Top face
        {0, 1}, {0, 0}, {1, 0}, {1, 1},
        // Bottom face
        {1, 1}, {0, 1}, {0, 0}, {1, 0},
        // Right face
        {1, 0}, {1, 1}, {0, 1}, {0, 0},
        // Left face
        {0, 0}, {1, 0}, {1, 1}, {0, 1}
    };
    
    // Create vertices
    mesh->vertices.reserve(24);
    for (size_t i = 0; i < positions.size(); ++i) {
        Vertex vertex;
        vertex.position = positions[i];
        vertex.normal = normals[i];
        vertex.texCoord = texCoords[i];
        vertex.tangent = Vector3(1, 0, 0);
        vertex.bitangent = Vector3(0, 1, 0);
        vertex.color = Vector4(1, 1, 1, 1);
        vertex.boneWeights = Vector4(0, 0, 0, 0);
        vertex.boneIndices[0] = vertex.boneIndices[1] = vertex.boneIndices[2] = vertex.boneIndices[3] = 0;
        
        mesh->vertices.push_back(vertex);
    }
    
    // Create indices
    std::vector<uint32_t> faceIndices = {
        0, 1, 2,  0, 2, 3,    // Front
        4, 5, 6,  4, 6, 7,    // Back
        8, 9, 10, 8, 10, 11,  // Top
        12, 13, 14, 12, 14, 15, // Bottom
        16, 17, 18, 16, 18, 19, // Right
        20, 21, 22, 20, 22, 23  // Left
    };
    
    mesh->indices = faceIndices;
    mesh->CalculateBounds();
    
    return mesh;
}

std::shared_ptr<Mesh> CreateSphere(float radius, uint32_t segments) {
    auto mesh = std::make_shared<Mesh>("Sphere");
    
    // TODO: Implement sphere generation
    // For now, return a simple cube
    return CreateCube(radius * 2.0f);
}

std::shared_ptr<Mesh> CreateCylinder(float radius, float height, uint32_t segments) {
    auto mesh = std::make_shared<Mesh>("Cylinder");
    
    // TODO: Implement cylinder generation
    return CreateCube(radius * 2.0f);
}

std::shared_ptr<Mesh> CreateCone(float radius, float height, uint32_t segments) {
    auto mesh = std::make_shared<Mesh>("Cone");
    
    // TODO: Implement cone generation
    return CreateCube(radius * 2.0f);
}

std::shared_ptr<Mesh> CreatePlane(float width, float height, uint32_t widthSegments, uint32_t heightSegments) {
    auto mesh = std::make_shared<Mesh>("Plane");
    
    // TODO: Implement plane generation
    return CreateCube(std::max(width, height));
}

std::shared_ptr<Mesh> CreateTorus(float majorRadius, float minorRadius, uint32_t majorSegments, uint32_t minorSegments) {
    auto mesh = std::make_shared<Mesh>("Torus");
    
    // TODO: Implement torus generation
    return CreateCube(majorRadius * 2.0f);
}

} // namespace PrimitiveGenerator

// ============================================================================
// ModelCache Implementation
// ============================================================================

ModelCache& ModelCache::GetInstance() {
    static ModelCache instance;
    return instance;
}

void ModelCache::Initialize(void* device) {
    m_device = device;
    CreatePrimitiveModels();
    
    std::cout << "✅ ModelCache initialized" << std::endl;
}

void ModelCache::Shutdown() {
    m_cube = AssetHandle<ModelAsset>();
    m_sphere = AssetHandle<ModelAsset>();
    m_plane = AssetHandle<ModelAsset>();
    m_cylinder = AssetHandle<ModelAsset>();
    
    std::cout << "🔄 ModelCache shutdown" << std::endl;
}

AssetHandle<ModelAsset> ModelCache::GetCube() {
    return m_cube;
}

AssetHandle<ModelAsset> ModelCache::GetSphere() {
    return m_sphere;
}

AssetHandle<ModelAsset> ModelCache::GetPlane() {
    return m_plane;
}

AssetHandle<ModelAsset> ModelCache::GetCylinder() {
    return m_cylinder;
}

AssetHandle<ModelAsset> ModelCache::LoadModel(const std::string& path) {
    return AssetManager::GetInstance().LoadAsset<ModelAsset>(path);
}

AssetHandle<ModelAsset> ModelCache::CreateFromMesh(std::shared_ptr<Mesh> mesh, const std::string& name) {
    // TODO: Implement model creation from mesh
    return AssetHandle<ModelAsset>();
}

void ModelCache::CreatePrimitiveModels() {
    auto& assetManager = AssetManager::GetInstance();
    
    // Register model loader
    assetManager.RegisterLoader(std::make_unique<ModelLoader>(m_device));
    
    // Load primitive models (these will be created as placeholders)
    m_cube = assetManager.LoadAsset<ModelAsset>("primitive_cube");
    m_sphere = assetManager.LoadAsset<ModelAsset>("primitive_sphere");
    m_plane = assetManager.LoadAsset<ModelAsset>("primitive_plane");
    m_cylinder = assetManager.LoadAsset<ModelAsset>("primitive_cylinder");
}

} // namespace Assets
} // namespace Engine
