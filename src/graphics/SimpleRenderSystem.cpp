//
// SimpleRenderSystem.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Simplified rendering system implementation
//

#include "SimpleRenderSystem.h"
#include "engine/Engine.h"
#include "engine/ECS.h"
#include <iostream>
#include <iomanip>

namespace Engine {
namespace Graphics {

// ============================================================================
// SimpleRenderSystem Implementation
// ============================================================================

SimpleRenderSystem::SimpleRenderSystem()
    : m_activeCamera(INVALID_ENTITY)
    , m_clearColor{0.2f, 0.3f, 0.4f, 1.0f}
    , m_totalTime(0.0f)
    , m_initialized(false) {
}

SimpleRenderSystem::~SimpleRenderSystem() {
    if (m_initialized) {
        Shutdown();
    }
}

void SimpleRenderSystem::Initialize() {
    std::cout << "🎨 Initializing SimpleRenderSystem..." << std::endl;
    
    m_initialized = true;
    std::cout << "✅ SimpleRenderSystem initialized successfully" << std::endl;
}

void SimpleRenderSystem::Update(DeltaTime deltaTime) {
    if (!m_initialized) {
        return;
    }
    
    m_totalTime += deltaTime;
    m_stats.frameCount++;
    m_stats.frameTime = deltaTime;
    
    // Find active camera
    SimpleCamera* activeCamera = nullptr;
    if (m_activeCamera != INVALID_ENTITY) {
        auto& ecs = Engine::Engine::GetInstance()->GetECS();
        if (ecs.HasComponent<SimpleCamera>(m_activeCamera)) {
            activeCamera = &ecs.GetComponent<SimpleCamera>(m_activeCamera);
        }
    }
    
    if (!activeCamera) {
        // No active camera, skip rendering
        return;
    }
    
    // Simulate frame begin
    if (m_stats.frameCount % 120 == 0) { // Print every 2 seconds at 60 FPS
        std::cout << "🎬 Frame " << m_stats.frameCount << " - Rendering with camera at (" 
                  << std::fixed << std::setprecision(2)
                  << activeCamera->position.x << ", " 
                  << activeCamera->position.y << ", " 
                  << activeCamera->position.z << ")" << std::endl;
    }
    
    // Clear screen (simulated)
    // In a real implementation, this would clear the Metal render target
    
    // Render all entities
    RenderEntities();
    
    // Update statistics
    UpdateStats();
}

void SimpleRenderSystem::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "🎨 Shutting down SimpleRenderSystem..." << std::endl;
    
    // Print final statistics
    std::cout << "📊 Final Render Statistics:" << std::endl;
    std::cout << "   Total Frames: " << m_stats.frameCount << std::endl;
    std::cout << "   Average FPS: " << (m_totalTime > 0.0f ? m_stats.frameCount / m_totalTime : 0.0f) << std::endl;
    
    m_initialized = false;
    std::cout << "✅ SimpleRenderSystem shutdown complete" << std::endl;
}

void SimpleRenderSystem::SetActiveCamera(Entity cameraEntity) {
    m_activeCamera = cameraEntity;
    
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    if (ecs.HasComponent<Name>(cameraEntity)) {
        auto& name = ecs.GetComponent<Name>(cameraEntity);
        std::cout << "📷 Active camera set to: " << name.name << " (Entity " << cameraEntity.GetID() << ")" << std::endl;
    } else {
        std::cout << "📷 Active camera set to Entity " << cameraEntity.GetID() << std::endl;
    }
}

void SimpleRenderSystem::RenderEntities() {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    
    m_stats.drawCalls = 0;
    m_stats.entities = 0;
    
    // Render all entities with SimpleMesh component
    for (auto& entity : m_entities) {
        if (!ecs.HasComponent<SimpleMesh>(entity) || !ecs.HasComponent<Transform>(entity)) {
            continue;
        }
        
        auto& mesh = ecs.GetComponent<SimpleMesh>(entity);
        auto& transform = ecs.GetComponent<Transform>(entity);
        
        if (!mesh.visible) {
            continue;
        }
        
        // Simulate rendering
        if (m_stats.frameCount % 180 == 0) { // Print every 3 seconds
            PrintEntityInfo(entity, transform, mesh);
        }
        
        m_stats.drawCalls++;
        m_stats.entities++;
    }
}

void SimpleRenderSystem::PrintEntityInfo(Entity entity, const Transform& transform, const SimpleMesh& mesh) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    
    std::cout << "🎯 Rendering Entity " << entity.GetID() << ": ";
    
    if (ecs.HasComponent<Name>(entity)) {
        auto& name = ecs.GetComponent<Name>(entity);
        std::cout << "'" << name.name << "' ";
    }
    
    std::cout << "mesh='" << mesh.meshName << "' "
              << "pos(" << std::fixed << std::setprecision(1)
              << transform.position.x << ", " 
              << transform.position.y << ", " 
              << transform.position.z << ") "
              << "color(" << std::setprecision(2)
              << mesh.color.x << ", " 
              << mesh.color.y << ", " 
              << mesh.color.z << ", " 
              << mesh.color.w << ")" << std::endl;
}

void SimpleRenderSystem::UpdateStats() {
    // In a real implementation, this would update GPU timing statistics
    // For now, we just track basic frame information
}

// ============================================================================
// Helper Functions
// ============================================================================

void RegisterSimpleGraphicsComponents(ECS& ecs) {
    ecs.RegisterComponent<SimpleCamera>();
    ecs.RegisterComponent<SimpleMesh>();
}

Entity CreateSimpleCameraEntity(ECS& ecs, const Vector3& position, const Vector3& target) {
    Entity camera = ecs.CreateEntity();
    ecs.AddComponent(camera, Name("SimpleCamera"));
    ecs.AddComponent(camera, Transform(position));
    ecs.AddComponent(camera, SimpleCamera(position, target));
    return camera;
}

Entity CreateSimpleMeshEntity(ECS& ecs, const std::string& meshName, const Vector4& color, const Transform& transform) {
    Entity entity = ecs.CreateEntity();
    ecs.AddComponent(entity, Name("SimpleMesh_" + meshName));
    ecs.AddComponent(entity, transform);
    ecs.AddComponent(entity, SimpleMesh(meshName, color));
    return entity;
}

} // namespace Graphics
} // namespace Engine
