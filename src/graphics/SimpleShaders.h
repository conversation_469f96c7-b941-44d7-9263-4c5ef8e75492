//
// SimpleShaders.h
// macOS 3D Looter-Shooter Game Engine
//
// Simple shader system using Metal's runtime compilation
//

#pragma once

#ifdef __OBJC__
#import <Metal/Metal.h>
#else
#include <Metal/Metal.h>
#endif

#include <string>

namespace Engine {
namespace Graphics {

// ============================================================================
// Simple Shader Manager
// ============================================================================

class SimpleShaders {
public:
    static id<MTLLibrary> CreateRuntimeLibrary(id<MTLDevice> device);
    static std::string GetVertexShaderSource();
    static std::string GetFragmentShaderSource();
    
private:
    static std::string GetShaderHeader();
};

} // namespace Graphics
} // namespace Engine
