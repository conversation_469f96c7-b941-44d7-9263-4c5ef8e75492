//
// RenderSystem.mm
// macOS 3D Looter-Shooter Game Engine
//
// Rendering system implementation
//

#include "RenderSystem.h"
#include "engine/Engine.h"
#include "engine/ECS.h"
#include <iostream>
#include <algorithm>

namespace Engine {
namespace Graphics {

// ============================================================================
// RenderSystem Implementation
// ============================================================================

RenderSystem::RenderSystem()
    : m_activeCamera(INVALID_ENTITY)
    , m_ambientColor{0.2f, 0.2f, 0.3f}
    , m_ambientIntensity(0.3f)
    , m_clearColor{0.2f, 0.3f, 0.4f, 1.0f}
    , m_wireframeMode(false)
    , m_cullingEnabled(true)
    , m_frameUniformBuffer(nil)
    , m_objectUniformBuffer(nil)
    , m_initialized(false) {
}

RenderSystem::~RenderSystem() {
    if (m_initialized) {
        Shutdown();
    }
}

void RenderSystem::Initialize() {
    std::cout << "🎨 Initializing RenderSystem..." << std::endl;
    
    // Graphics initialization will be done separately via InitializeGraphics
    // This allows the engine to control when graphics are initialized
    
    m_initialized = true;
    std::cout << "✅ RenderSystem initialized" << std::endl;
}

bool RenderSystem::InitializeGraphics(int width, int height, bool vsync) {
    std::cout << "🎨 Initializing graphics subsystems..." << std::endl;
    
    // Initialize Metal device
    m_device = std::make_unique<MetalDevice>();
    if (!m_device->Initialize()) {
        std::cerr << "❌ Failed to initialize Metal device" << std::endl;
        return false;
    }
    
    // Initialize Metal context
    m_context = std::make_unique<MetalContext>();
    if (!m_context->Initialize(width, height, vsync)) {
        std::cerr << "❌ Failed to initialize Metal context" << std::endl;
        return false;
    }
    
    // Initialize shader manager
    m_shaderManager = std::make_unique<ShaderManager>();
    if (!m_shaderManager->Initialize(m_device->GetDevice(), m_device->GetDefaultLibrary())) {
        std::cerr << "❌ Failed to initialize shader manager" << std::endl;
        return false;
    }
    
    // Initialize mesh manager
    m_meshManager = std::make_unique<MeshManager>();
    if (!m_meshManager->Initialize(m_device->GetDevice())) {
        std::cerr << "❌ Failed to initialize mesh manager" << std::endl;
        return false;
    }
    
    // Create uniform buffers
    m_frameUniformBuffer = m_shaderManager->CreateFrameUniformBuffer();
    m_objectUniformBuffer = m_shaderManager->CreateObjectUniformBuffer();
    
    if (!m_frameUniformBuffer || !m_objectUniformBuffer) {
        std::cerr << "❌ Failed to create uniform buffers" << std::endl;
        return false;
    }
    
    std::cout << "✅ Graphics subsystems initialized successfully" << std::endl;
    return true;
}

void RenderSystem::Update(DeltaTime deltaTime) {
    if (!m_device || !m_context) {
        return; // Graphics not initialized yet
    }
    
    m_frameStartTime = std::chrono::high_resolution_clock::now();
    m_stats.Reset();
    
    // Find active camera
    Camera* activeCamera = nullptr;
    if (m_activeCamera != INVALID_ENTITY) {
        auto& ecs = Engine::Engine::GetInstance()->GetECS();
        if (ecs.HasComponent<Camera>(m_activeCamera)) {
            activeCamera = &ecs.GetComponent<Camera>(m_activeCamera);
        }
    }
    
    if (!activeCamera) {
        // No active camera, skip rendering
        return;
    }
    
    // Begin frame
    BeginFrame();
    
    // Update frame uniforms
    float aspectRatio = m_context->GetAspectRatio();
    UpdateFrameUniforms(*activeCamera, aspectRatio);
    
    // Render all meshes
    RenderMeshes();
    
    // Render debug geometry
    RenderDebugGeometry();
    
    // End frame
    EndFrame();
    Present();
    
    // Update statistics
    auto frameEndTime = std::chrono::high_resolution_clock::now();
    auto frameDuration = std::chrono::duration<float>(frameEndTime - m_frameStartTime);
    m_stats.frameTime = frameDuration.count();
    m_stats.frameCount++;
}

void RenderSystem::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "🎨 Shutting down RenderSystem..." << std::endl;
    
    ShutdownGraphics();
    
    m_initialized = false;
    std::cout << "✅ RenderSystem shutdown complete" << std::endl;
}

void RenderSystem::ShutdownGraphics() {
    // Release uniform buffers
    if (m_frameUniformBuffer) {
        [m_frameUniformBuffer release];
        m_frameUniformBuffer = nil;
    }
    
    if (m_objectUniformBuffer) {
        [m_objectUniformBuffer release];
        m_objectUniformBuffer = nil;
    }
    
    // Shutdown subsystems
    if (m_meshManager) {
        m_meshManager->Shutdown();
        m_meshManager.reset();
    }
    
    if (m_shaderManager) {
        m_shaderManager->Shutdown();
        m_shaderManager.reset();
    }
    
    if (m_context) {
        m_context->Shutdown();
        m_context.reset();
    }
    
    if (m_device) {
        m_device->Shutdown();
        m_device.reset();
    }
}

void RenderSystem::BeginFrame() {
    m_context->BeginFrame();
}

void RenderSystem::EndFrame() {
    m_context->EndFrame();
}

void RenderSystem::Present() {
    m_context->Present();
}

void RenderSystem::SetActiveCamera(Entity cameraEntity) {
    m_activeCamera = cameraEntity;
}

void RenderSystem::UpdateFrameUniforms(const Camera& camera, float aspectRatio) {
    FrameUniforms uniforms;
    
    // Calculate matrices
    uniforms.viewMatrix = CalculateViewMatrix(camera);
    uniforms.projectionMatrix = CalculateProjectionMatrix(camera, aspectRatio);
    uniforms.viewProjectionMatrix = simd_mul(uniforms.projectionMatrix, uniforms.viewMatrix);
    
    // Camera position
    uniforms.cameraPosition = simd_make_float3(camera.position.x, camera.position.y, camera.position.z);
    
    // Lighting
    uniforms.lightDirection = simd_make_float3(m_directionalLight.direction.x, 
                                              m_directionalLight.direction.y, 
                                              m_directionalLight.direction.z);
    uniforms.lightColor = simd_make_float3(m_directionalLight.color.x * m_directionalLight.intensity,
                                          m_directionalLight.color.y * m_directionalLight.intensity,
                                          m_directionalLight.color.z * m_directionalLight.intensity);
    uniforms.ambientIntensity = m_ambientIntensity;
    
    // Time
    uniforms.time = static_cast<float>(Engine::Engine::GetInstance()->GetTotalTime());
    uniforms.deltaTime = Engine::Engine::GetInstance()->GetDeltaTime();
    
    // Update buffer
    m_shaderManager->UpdateFrameUniforms(m_frameUniformBuffer, uniforms);
}

void RenderSystem::UpdateObjectUniforms(const Transform& transform) {
    ObjectUniforms uniforms;
    
    // Calculate model matrix
    uniforms.modelMatrix = CalculateModelMatrix(transform);
    
    // Calculate normal matrix (inverse transpose of model matrix)
    uniforms.normalMatrix = simd_transpose(simd_inverse(uniforms.modelMatrix));
    
    // MVP matrix will be calculated in vertex shader
    uniforms.mvpMatrix = uniforms.modelMatrix; // Placeholder
    
    // Default material properties
    uniforms.color = simd_make_float4(1.0f, 1.0f, 1.0f, 1.0f);
    uniforms.metallic = 0.0f;
    uniforms.roughness = 0.5f;
    uniforms.ao = 1.0f;
    
    // Update buffer
    m_shaderManager->UpdateObjectUniforms(m_objectUniformBuffer, uniforms);
}

void RenderSystem::RenderMeshes() {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    
    // Get render command encoder
    id<MTLRenderCommandEncoder> encoder = m_context->CreateRenderEncoder(nullptr);
    if (!encoder) {
        return;
    }
    
    [encoder setLabel:@"Main Render Pass"];
    
    // Set basic pipeline state (for now, we'll use a simple pipeline)
    id<MTLRenderPipelineState> pipeline = m_shaderManager->CreateBasicPipeline(
        MTLPixelFormatBGRA8Unorm, MTLPixelFormatDepth32Float);
    [encoder setRenderPipelineState:pipeline];
    
    // Set depth state
    id<MTLDepthStencilState> depthState = m_shaderManager->CreateDepthState();
    [encoder setDepthStencilState:depthState];
    
    // Set frame uniforms
    [encoder setVertexBuffer:m_frameUniformBuffer offset:0 atIndex:VertexBufferIndexFrameUniforms];
    [encoder setFragmentBuffer:m_frameUniformBuffer offset:0 atIndex:FragmentBufferIndexFrameUniforms];
    
    // Render all entities with MeshComponent
    for (auto& entity : m_entities) {
        if (!ecs.HasComponent<MeshComponent>(entity) || !ecs.HasComponent<Transform>(entity)) {
            continue;
        }
        
        auto& meshComp = ecs.GetComponent<MeshComponent>(entity);
        auto& transform = ecs.GetComponent<Transform>(entity);
        
        if (!meshComp.visible || !meshComp.mesh) {
            continue;
        }
        
        // Update object uniforms
        UpdateObjectUniforms(transform);
        [encoder setVertexBuffer:m_objectUniformBuffer offset:0 atIndex:VertexBufferIndexObjectUniforms];
        
        // Bind material if available
        if (meshComp.material) {
            meshComp.material->Bind(encoder);
        }
        
        // Draw mesh
        meshComp.mesh->Draw(encoder);
        
        // Update statistics
        m_stats.drawCalls++;
        m_stats.triangles += meshComp.mesh->GetTriangleCount();
        m_stats.vertices += meshComp.mesh->GetVertexCount();
    }
    
    [encoder endEncoding];
}

void RenderSystem::RenderDebugGeometry() {
    // TODO: Implement debug geometry rendering
    // For now, just clear the debug lists
    m_debugLines.clear();
    m_debugLineColors.clear();
}

simd_float4x4 RenderSystem::CalculateViewMatrix(const Camera& camera) {
    return m_shaderManager->CreateViewMatrix(camera.position, camera.target, camera.up);
}

simd_float4x4 RenderSystem::CalculateProjectionMatrix(const Camera& camera, float aspectRatio) {
    if (camera.isPerspective) {
        float fovRadians = camera.fov * M_PI / 180.0f;
        return m_shaderManager->CreateProjectionMatrix(fovRadians, aspectRatio, camera.nearPlane, camera.farPlane);
    } else {
        float halfSize = camera.orthoSize * 0.5f;
        return ShaderMath::matrix_orthographic(-halfSize * aspectRatio, halfSize * aspectRatio,
                                              -halfSize, halfSize, camera.nearPlane, camera.farPlane);
    }
}

simd_float4x4 RenderSystem::CalculateModelMatrix(const Transform& transform) {
    return m_shaderManager->CreateModelMatrix(transform);
}

// ============================================================================
// Helper Functions
// ============================================================================

void RegisterGraphicsComponents(ECS& ecs) {
    ecs.RegisterComponent<Camera>();
    ecs.RegisterComponent<MeshComponent>();
    ecs.RegisterComponent<DirectionalLight>();
    ecs.RegisterComponent<PointLight>();
    ecs.RegisterComponent<SpotLight>();
}

Entity CreateCameraEntity(ECS& ecs, const Vector3& position, const Vector3& target) {
    Entity camera = ecs.CreateEntity();
    ecs.AddComponent(camera, Name("Camera"));
    ecs.AddComponent(camera, Transform(position));
    ecs.AddComponent(camera, Camera(position, target));
    return camera;
}

Entity CreateMeshEntity(ECS& ecs, std::shared_ptr<Mesh> mesh, std::shared_ptr<Material> material, const Transform& transform) {
    Entity entity = ecs.CreateEntity();
    ecs.AddComponent(entity, Name("MeshEntity"));
    ecs.AddComponent(entity, transform);
    ecs.AddComponent(entity, MeshComponent(mesh, material));
    return entity;
}

} // namespace Graphics
} // namespace Engine
