//
// WindowManager.h
// macOS 3D Looter-Shooter Game Engine
//
// Window management system for creating and managing game windows
//

#pragma once

#include <memory>
#include <string>

namespace Engine {

// Forward declarations
namespace Input {
    class InputSystem;
    class CocoaInputHandler;
}

namespace Graphics {

// Forward declarations
class MetalDevice;

// ============================================================================
// Window Configuration
// ============================================================================

struct WindowConfig {
    int width = 1920;
    int height = 1080;
    bool fullscreen = false;
    bool resizable = true;
    bool vsync = true;
    std::string title = "macOS 3D Looter-Shooter";
};

// ============================================================================
// Window Manager Class
// ============================================================================

class WindowManager {
public:
    WindowManager();
    ~WindowManager();
    
    // Window lifecycle
    bool Initialize(const WindowConfig& config);
    void Shutdown();
    
    // Window management
    void Show();
    void Hide();
    void SetTitle(const std::string& title);
    void SetSize(int width, int height);
    void SetFullscreen(bool fullscreen);
    
    // Event handling
    void PollEvents();
    bool ShouldClose() const;
    void RequestClose();

    // Input system integration
    void SetInputSystem(Input::InputSystem* inputSystem);
    Input::InputSystem* GetInputSystem() const { return m_inputSystem; }
    
    // Metal integration
    void* GetMetalView(); // Returns MTKView*
    void* GetWindow();    // Returns NSWindow*
    
    // Properties
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    bool IsFullscreen() const { return m_fullscreen; }
    bool IsVisible() const { return m_visible; }
    
    // Singleton access
    static WindowManager* GetInstance() { return s_instance; }
    
private:
    // Window properties
    int m_width;
    int m_height;
    bool m_fullscreen;
    bool m_resizable;
    bool m_vsync;
    bool m_visible;
    bool m_shouldClose;
    std::string m_title;
    
    // Cocoa objects (stored as void* to avoid Objective-C in header)
    void* m_window;     // NSWindow*
    void* m_metalView;  // MTKView*
    void* m_delegate;   // WindowDelegate*
    
    // Metal device reference
    MetalDevice* m_metalDevice;

    // Input system integration
    Input::InputSystem* m_inputSystem;
    Input::CocoaInputHandler* m_inputHandler;

    // Initialization state
    bool m_initialized;
    
    // Singleton instance
    static WindowManager* s_instance;
    
    // Private methods
    bool CreateWindow();
    bool CreateMetalView();
    void SetupWindowDelegate();
    void UpdateWindowProperties();
};

} // namespace Graphics
} // namespace Engine
