//
// TextureGenerator.h
// macOS 3D Looter-Shooter Game Engine
//
// Utility for generating basic textures programmatically
//

#pragma once

#ifdef __OBJC__
#import <Metal/Metal.h>
#else
#include <Metal/Metal.h>
#endif

#include <memory>
#include <string>

namespace Engine {
namespace Graphics {

// ============================================================================
// Texture Generator Class
// ============================================================================

class TextureGenerator {
public:
    static id<MTLTexture> CreateSolidColorTexture(id<MTLDevice> device, 
                                                  float r, float g, float b, float a = 1.0f,
                                                  int width = 64, int height = 64);
    
    static id<MTLTexture> CreateCheckerboardTexture(id<MTLDevice> device,
                                                    float r1, float g1, float b1,
                                                    float r2, float g2, float b2,
                                                    int width = 64, int height = 64,
                                                    int checkerSize = 8);
    
    static id<MTLTexture> CreateWhiteTexture(id<MTLDevice> device);
    static id<MTLTexture> CreateBlackTexture(id<MTLDevice> device);
    static id<MTLTexture> CreateNormalMapTexture(id<MTLDevice> device);
    
    static bool SaveTextureToFile(id<MTLTexture> texture, const std::string& filename);
    
private:
    static void FillTextureData(uint8_t* data, int width, int height, 
                               float r, float g, float b, float a);
    static void FillCheckerboardData(uint8_t* data, int width, int height,
                                    float r1, float g1, float b1,
                                    float r2, float g2, float b2,
                                    int checkerSize);
};

} // namespace Graphics
} // namespace Engine
