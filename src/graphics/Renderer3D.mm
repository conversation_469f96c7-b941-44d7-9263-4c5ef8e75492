//
// Renderer3D.mm
// macOS 3D Looter-Shooter Game Engine
//
// 3D renderer implementation using Metal API
//

#include "Renderer3D.h"
#include "SimpleShaders.h"
#include "engine/Engine.h"
#include <iostream>
#include <cmath>

#import <Metal/Metal.h>
#import <MetalKit/MetalKit.h>

namespace Engine {
namespace Graphics {

// ============================================================================
// Mesh3D Implementation
// ============================================================================

Mesh3D::Mesh3D()
    : m_vertexBuffer(nil)
    , m_indexBuffer(nil)
    , m_vertexCount(0)
    , m_indexCount(0)
    , m_initialized(false) {
}

Mesh3D::~Mesh3D() {
    if (m_initialized) {
        Shutdown();
    }
}

bool Mesh3D::Initialize(id<MTLDevice> device, const std::vector<Vertex3D>& vertices, const std::vector<uint16_t>& indices) {
    if (m_initialized) {
        return true;
    }
    
    m_vertexCount = vertices.size();
    m_indexCount = indices.size();
    
    // Create vertex buffer
    m_vertexBuffer = [device newBufferWithBytes:vertices.data()
                                         length:vertices.size() * sizeof(Vertex3D)
                                        options:MTLResourceStorageModeShared];
    if (!m_vertexBuffer) {
        std::cerr << "❌ Failed to create vertex buffer" << std::endl;
        return false;
    }
    [m_vertexBuffer setLabel:@"Vertex Buffer"];
    
    // Create index buffer
    m_indexBuffer = [device newBufferWithBytes:indices.data()
                                        length:indices.size() * sizeof(uint16_t)
                                       options:MTLResourceStorageModeShared];
    if (!m_indexBuffer) {
        std::cerr << "❌ Failed to create index buffer" << std::endl;
        return false;
    }
    [m_indexBuffer setLabel:@"Index Buffer"];
    
    m_initialized = true;
    return true;
}

void Mesh3D::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    if (m_indexBuffer) {
        [m_indexBuffer release];
        m_indexBuffer = nil;
    }
    
    if (m_vertexBuffer) {
        [m_vertexBuffer release];
        m_vertexBuffer = nil;
    }
    
    m_initialized = false;
}

void Mesh3D::Draw(id<MTLRenderCommandEncoder> encoder) {
    if (!m_initialized || !encoder) {
        return;
    }
    
    [encoder setVertexBuffer:m_vertexBuffer offset:0 atIndex:0];
    [encoder drawIndexedPrimitives:MTLPrimitiveTypeTriangle
                        indexCount:m_indexCount
                         indexType:MTLIndexTypeUInt16
                       indexBuffer:m_indexBuffer
                 indexBufferOffset:0];
}

// ============================================================================
// Renderer3D Implementation
// ============================================================================

Renderer3D::Renderer3D()
    : m_metalDevice(nullptr)
    , m_windowManager(nullptr)
    , m_pipelineState(nil)
    , m_depthStencilState(nil)
    , m_frameUniformBuffer(nil)
    , m_objectUniformBuffer(nil)
    , m_samplerState(nil)
    , m_activeCamera(INVALID_ENTITY)
    , m_totalTime(0.0f)
    , m_initialized(false) {
}

Renderer3D::~Renderer3D() {
    if (m_initialized) {
        Shutdown();
    }
}

void Renderer3D::Initialize() {
    if (m_initialized) {
        return;
    }
    
    std::cout << "🎨 Initializing 3D Renderer..." << std::endl;
    
    // Get Metal device and window manager
    m_metalDevice = MetalDevice::GetInstance();
    m_windowManager = WindowManager::GetInstance();
    
    if (!m_metalDevice || !m_windowManager) {
        std::cerr << "❌ MetalDevice and WindowManager must be initialized first" << std::endl;
        return;
    }
    
    if (!InitializeGraphics()) {
        std::cerr << "❌ Failed to initialize 3D graphics" << std::endl;
        return;
    }
    
    m_initialized = true;
    std::cout << "✅ 3D Renderer initialized successfully" << std::endl;
}

void Renderer3D::Update(DeltaTime deltaTime) {
    if (!m_initialized) {
        return;
    }
    
    m_totalTime += deltaTime;
    m_stats.frameTime = deltaTime;
    m_stats.Reset();
    
    // Find active camera
    Camera3D* activeCamera = nullptr;
    if (m_activeCamera != INVALID_ENTITY) {
        auto& ecs = Engine::Engine::GetInstance()->GetECS();
        if (ecs.HasComponent<Camera3D>(m_activeCamera)) {
            activeCamera = &ecs.GetComponent<Camera3D>(m_activeCamera);
        }
    }
    
    if (!activeCamera) {
        return; // No active camera, skip rendering
    }
    
    // Begin frame
    BeginFrame();
    
    // Update frame uniforms
    float aspectRatio = static_cast<float>(m_windowManager->GetWidth()) / static_cast<float>(m_windowManager->GetHeight());
    UpdateFrameUniforms(*activeCamera, aspectRatio);
    
    // Render all meshes
    RenderMeshes();
    
    // End frame and present
    EndFrame();
    Present();
    
    m_stats.frameCount++;
}

void Renderer3D::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "🎨 Shutting down 3D Renderer..." << std::endl;
    
    // Release Metal resources
    if (m_samplerState) {
        [m_samplerState release];
        m_samplerState = nil;
    }
    
    if (m_objectUniformBuffer) {
        [m_objectUniformBuffer release];
        m_objectUniformBuffer = nil;
    }
    
    if (m_frameUniformBuffer) {
        [m_frameUniformBuffer release];
        m_frameUniformBuffer = nil;
    }
    
    if (m_depthStencilState) {
        [m_depthStencilState release];
        m_depthStencilState = nil;
    }
    
    if (m_pipelineState) {
        [m_pipelineState release];
        m_pipelineState = nil;
    }
    
    m_initialized = false;
    std::cout << "✅ 3D Renderer shutdown complete" << std::endl;
}

bool Renderer3D::InitializeGraphics() {
    // Try to create shader pipeline, but continue without it if it fails
    if (!CreateShaderPipeline()) {
        std::cout << "⚠️  Failed to create shader pipeline, using fallback rendering" << std::endl;
        // Continue without shaders - we'll use a simple clear color
    }

    if (!CreateUniformBuffers()) {
        std::cerr << "❌ Failed to create uniform buffers" << std::endl;
        return false;
    }

    return true;
}

bool Renderer3D::CreateShaderPipeline() {
    id<MTLDevice> device = m_metalDevice->GetDevice();

    // Try to get the default library first
    id<MTLLibrary> library = m_metalDevice->GetDefaultLibrary();

    // If no default library, create one at runtime
    if (!library) {
        std::cout << "🔧 Creating runtime shader library..." << std::endl;
        library = SimpleShaders::CreateRuntimeLibrary(device);

        if (!library) {
            std::cerr << "❌ Failed to create runtime shader library" << std::endl;
            return false;
        }
    }
    
    // Load vertex and fragment functions
    id<MTLFunction> vertexFunction = [library newFunctionWithName:@"basic_vertex_shader"];
    id<MTLFunction> fragmentFunction = [library newFunctionWithName:@"basic_fragment_shader"];
    
    if (!vertexFunction || !fragmentFunction) {
        std::cerr << "❌ Failed to load shader functions" << std::endl;
        return false;
    }
    
    // Create render pipeline descriptor
    MTLRenderPipelineDescriptor* pipelineDescriptor = [[MTLRenderPipelineDescriptor alloc] init];
    pipelineDescriptor.label = @"Basic 3D Pipeline";
    pipelineDescriptor.vertexFunction = vertexFunction;
    pipelineDescriptor.fragmentFunction = fragmentFunction;
    pipelineDescriptor.colorAttachments[0].pixelFormat = MTLPixelFormatBGRA8Unorm;
    pipelineDescriptor.depthAttachmentPixelFormat = MTLPixelFormatDepth32Float;
    
    // Set up vertex descriptor
    MTLVertexDescriptor* vertexDescriptor = [[MTLVertexDescriptor alloc] init];
    
    // Position attribute
    vertexDescriptor.attributes[0].format = MTLVertexFormatFloat3;
    vertexDescriptor.attributes[0].offset = 0;
    vertexDescriptor.attributes[0].bufferIndex = 0;
    
    // Normal attribute
    vertexDescriptor.attributes[1].format = MTLVertexFormatFloat3;
    vertexDescriptor.attributes[1].offset = sizeof(simd_float3);
    vertexDescriptor.attributes[1].bufferIndex = 0;
    
    // Texture coordinate attribute
    vertexDescriptor.attributes[2].format = MTLVertexFormatFloat2;
    vertexDescriptor.attributes[2].offset = sizeof(simd_float3) * 2;
    vertexDescriptor.attributes[2].bufferIndex = 0;
    
    // Buffer layout
    vertexDescriptor.layouts[0].stride = sizeof(Vertex3D);
    vertexDescriptor.layouts[0].stepRate = 1;
    vertexDescriptor.layouts[0].stepFunction = MTLVertexStepFunctionPerVertex;
    
    pipelineDescriptor.vertexDescriptor = vertexDescriptor;
    
    // Create pipeline state
    NSError* error = nil;
    m_pipelineState = [device newRenderPipelineStateWithDescriptor:pipelineDescriptor error:&error];
    
    [pipelineDescriptor release];
    [vertexDescriptor release];
    
    if (error || !m_pipelineState) {
        if (error) {
            NSLog(@"❌ Pipeline creation error: %@", error.localizedDescription);
        }
        return false;
    }
    
    // Create depth stencil state
    MTLDepthStencilDescriptor* depthDescriptor = [[MTLDepthStencilDescriptor alloc] init];
    depthDescriptor.depthCompareFunction = MTLCompareFunctionLess;
    depthDescriptor.depthWriteEnabled = YES;
    
    m_depthStencilState = [device newDepthStencilStateWithDescriptor:depthDescriptor];
    [depthDescriptor release];
    
    if (!m_depthStencilState) {
        std::cerr << "❌ Failed to create depth stencil state" << std::endl;
        return false;
    }
    
    // Create sampler state
    MTLSamplerDescriptor* samplerDescriptor = [[MTLSamplerDescriptor alloc] init];
    samplerDescriptor.minFilter = MTLSamplerMinMagFilterLinear;
    samplerDescriptor.magFilter = MTLSamplerMinMagFilterLinear;
    samplerDescriptor.sAddressMode = MTLSamplerAddressModeRepeat;
    samplerDescriptor.tAddressMode = MTLSamplerAddressModeRepeat;
    
    m_samplerState = [device newSamplerStateWithDescriptor:samplerDescriptor];
    [samplerDescriptor release];
    
    if (!m_samplerState) {
        std::cerr << "❌ Failed to create sampler state" << std::endl;
        return false;
    }
    
    std::cout << "✅ Shader pipeline created successfully" << std::endl;
    return true;
}

bool Renderer3D::CreateUniformBuffers() {
    id<MTLDevice> device = m_metalDevice->GetDevice();
    
    // Create frame uniform buffer
    m_frameUniformBuffer = [device newBufferWithLength:sizeof(FrameUniforms)
                                                options:MTLResourceStorageModeShared];
    if (!m_frameUniformBuffer) {
        std::cerr << "❌ Failed to create frame uniform buffer" << std::endl;
        return false;
    }
    [m_frameUniformBuffer setLabel:@"Frame Uniforms"];
    
    // Create object uniform buffer
    m_objectUniformBuffer = [device newBufferWithLength:sizeof(ObjectUniforms)
                                                 options:MTLResourceStorageModeShared];
    if (!m_objectUniformBuffer) {
        std::cerr << "❌ Failed to create object uniform buffer" << std::endl;
        return false;
    }
    [m_objectUniformBuffer setLabel:@"Object Uniforms"];
    
    return true;
}

void Renderer3D::BeginFrame() {
    // Get the Metal view from window manager
    MTKView* metalView = (__bridge MTKView*)m_windowManager->GetMetalView();
    if (!metalView) {
        return;
    }

    // Begin frame rendering
    // The MTKView handles command buffer creation automatically
}

void Renderer3D::EndFrame() {
    // Frame ending is handled by MTKView
}

void Renderer3D::Present() {
    // Presentation is handled by MTKView
}

void Renderer3D::RenderMeshes() {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    MTKView* metalView = (__bridge MTKView*)m_windowManager->GetMetalView();

    if (!metalView) {
        return;
    }

    // Create a simple command buffer to clear the screen with a visible pattern
    id<MTLCommandBuffer> commandBuffer = [m_metalDevice->GetCommandQueue() commandBuffer];
    [commandBuffer setLabel:@"Fallback Render Commands"];

    // Get current drawable
    id<CAMetalDrawable> drawable = [metalView currentDrawable];
    if (!drawable) {
        return;
    }

    // Create render pass descriptor
    MTLRenderPassDescriptor* renderPassDescriptor = [MTLRenderPassDescriptor renderPassDescriptor];
    renderPassDescriptor.colorAttachments[0].texture = drawable.texture;
    renderPassDescriptor.colorAttachments[0].loadAction = MTLLoadActionClear;
    renderPassDescriptor.colorAttachments[0].storeAction = MTLStoreActionStore;

    // Use a nice space-like background color
    renderPassDescriptor.colorAttachments[0].clearColor = MTLClearColorMake(0.05f, 0.05f, 0.15f, 1.0f); // Dark blue space

    // Create render encoder
    id<MTLRenderCommandEncoder> encoder = [commandBuffer renderCommandEncoderWithDescriptor:renderPassDescriptor];
    [encoder setLabel:@"Fallback Clear Pass"];

    // End encoding immediately (we're just clearing)
    [encoder endEncoding];

    // Present drawable
    [commandBuffer presentDrawable:drawable];
    [commandBuffer commit];

    // If we don't have a pipeline state, we can't render meshes, but we show the animated clear color
    if (!m_pipelineState) {
        static int frameCount = 0;
        frameCount++;
        if (frameCount % 60 == 0) { // Print every second
            std::cout << "🎨 Rendering animated fallback - " << m_entities.size() << " entities tracked (no shaders)" << std::endl;
        }
        return;
    }

    // If we have shaders, use the normal rendering path
    MTLRenderPassDescriptor* shaderRenderPass = metalView.currentRenderPassDescriptor;
    if (!shaderRenderPass) {
        return;
    }

    // Create command buffer for shader rendering
    id<MTLCommandBuffer> shaderCommandBuffer = [m_metalDevice->GetCommandQueue() commandBuffer];
    [shaderCommandBuffer setLabel:@"3D Render Commands"];

    // Create render encoder for shader rendering
    id<MTLRenderCommandEncoder> shaderEncoder = [shaderCommandBuffer renderCommandEncoderWithDescriptor:shaderRenderPass];
    [shaderEncoder setLabel:@"3D Render Pass"];

    // Set pipeline state
    [shaderEncoder setRenderPipelineState:m_pipelineState];
    [shaderEncoder setDepthStencilState:m_depthStencilState];

    // Set frame uniforms
    [shaderEncoder setVertexBuffer:m_frameUniformBuffer offset:0 atIndex:1];
    [shaderEncoder setFragmentBuffer:m_frameUniformBuffer offset:0 atIndex:0];

    // Set sampler
    [shaderEncoder setFragmentSamplerState:m_samplerState atIndex:0];

    // Render all entities with Renderable3D component
    for (auto& entity : m_entities) {
        if (!ecs.HasComponent<Renderable3D>(entity) || !ecs.HasComponent<Transform>(entity)) {
            continue;
        }

        auto& renderable = ecs.GetComponent<Renderable3D>(entity);
        auto& transform = ecs.GetComponent<Transform>(entity);

        if (!renderable.visible || !renderable.mesh) {
            continue;
        }

        // Update object uniforms
        UpdateObjectUniforms(transform, renderable);
        [shaderEncoder setVertexBuffer:m_objectUniformBuffer offset:0 atIndex:2];

        // Draw mesh
        renderable.mesh->Draw(shaderEncoder);

        // Update statistics
        m_stats.drawCalls++;
        m_stats.triangles += renderable.mesh->GetTriangleCount();
        m_stats.vertices += renderable.mesh->GetVertexCount();
        m_stats.entities++;
    }

    [shaderEncoder endEncoding];

    // Present drawable
    [shaderCommandBuffer presentDrawable:metalView.currentDrawable];
    [shaderCommandBuffer commit];
}

void Renderer3D::UpdateFrameUniforms(const Camera3D& camera, float aspectRatio) {
    FrameUniforms* uniforms = (FrameUniforms*)[m_frameUniformBuffer contents];

    // Create matrices
    uniforms->viewMatrix = CreateViewMatrix(camera.position, camera.target, camera.up);
    uniforms->projectionMatrix = CreateProjectionMatrix(camera.fov, aspectRatio, camera.nearPlane, camera.farPlane);
    uniforms->viewProjectionMatrix = simd_mul(uniforms->projectionMatrix, uniforms->viewMatrix);
    uniforms->cameraPosition = camera.position;

    // Lighting
    uniforms->lightDirection = simd_normalize(m_lightDirection);
    uniforms->lightColor = m_lightColor;
    uniforms->ambientIntensity = m_ambientIntensity;

    // Time
    uniforms->time = m_totalTime;
    uniforms->deltaTime = m_stats.frameTime;
}

void Renderer3D::UpdateObjectUniforms(const Transform& transform, const Renderable3D& renderable) {
    ObjectUniforms* uniforms = (ObjectUniforms*)[m_objectUniformBuffer contents];

    // Create model matrix
    uniforms->modelMatrix = CreateModelMatrix(transform);

    // Create normal matrix (inverse transpose of model matrix)
    simd_float4x4 normalMatrix = simd_transpose(simd_inverse(uniforms->modelMatrix));
    uniforms->normalMatrix = normalMatrix;

    // Create MVP matrix
    FrameUniforms* frameUniforms = (FrameUniforms*)[m_frameUniformBuffer contents];
    uniforms->mvpMatrix = simd_mul(frameUniforms->viewProjectionMatrix, uniforms->modelMatrix);

    // Material properties
    uniforms->color = renderable.color;
    uniforms->metallic = renderable.metallic;
    uniforms->roughness = renderable.roughness;
    uniforms->ao = renderable.ao;
}

simd_float4x4 Renderer3D::CreateViewMatrix(simd_float3 position, simd_float3 target, simd_float3 up) {
    simd_float3 zAxis = simd_normalize(position - target);  // Forward
    simd_float3 xAxis = simd_normalize(simd_cross(up, zAxis));  // Right
    simd_float3 yAxis = simd_cross(zAxis, xAxis);  // Up

    simd_float4x4 viewMatrix = {
        .columns[0] = {xAxis.x, yAxis.x, zAxis.x, 0.0f},
        .columns[1] = {xAxis.y, yAxis.y, zAxis.y, 0.0f},
        .columns[2] = {xAxis.z, yAxis.z, zAxis.z, 0.0f},
        .columns[3] = {-simd_dot(xAxis, position), -simd_dot(yAxis, position), -simd_dot(zAxis, position), 1.0f}
    };

    return viewMatrix;
}

simd_float4x4 Renderer3D::CreateProjectionMatrix(float fov, float aspectRatio, float nearPlane, float farPlane) {
    float fovRadians = fov * M_PI / 180.0f;
    float tanHalfFov = tanf(fovRadians / 2.0f);

    simd_float4x4 projMatrix = {
        .columns[0] = {1.0f / (aspectRatio * tanHalfFov), 0.0f, 0.0f, 0.0f},
        .columns[1] = {0.0f, 1.0f / tanHalfFov, 0.0f, 0.0f},
        .columns[2] = {0.0f, 0.0f, -(farPlane + nearPlane) / (farPlane - nearPlane), -1.0f},
        .columns[3] = {0.0f, 0.0f, -(2.0f * farPlane * nearPlane) / (farPlane - nearPlane), 0.0f}
    };

    return projMatrix;
}

simd_float4x4 Renderer3D::CreateModelMatrix(const Transform& transform) {
    // Create translation matrix
    simd_float4x4 translation = {
        .columns[0] = {1.0f, 0.0f, 0.0f, 0.0f},
        .columns[1] = {0.0f, 1.0f, 0.0f, 0.0f},
        .columns[2] = {0.0f, 0.0f, 1.0f, 0.0f},
        .columns[3] = {transform.position.x, transform.position.y, transform.position.z, 1.0f}
    };

    // For now, just return translation matrix
    // TODO: Add rotation and scale
    return translation;
}

std::shared_ptr<Mesh3D> Renderer3D::CreateCubeMesh() {
    std::vector<Vertex3D> vertices = {
        // Front face
        {{-0.5f, -0.5f,  0.5f}, {0.0f, 0.0f, 1.0f}, {0.0f, 0.0f}},
        {{ 0.5f, -0.5f,  0.5f}, {0.0f, 0.0f, 1.0f}, {1.0f, 0.0f}},
        {{ 0.5f,  0.5f,  0.5f}, {0.0f, 0.0f, 1.0f}, {1.0f, 1.0f}},
        {{-0.5f,  0.5f,  0.5f}, {0.0f, 0.0f, 1.0f}, {0.0f, 1.0f}},

        // Back face
        {{ 0.5f, -0.5f, -0.5f}, {0.0f, 0.0f, -1.0f}, {0.0f, 0.0f}},
        {{-0.5f, -0.5f, -0.5f}, {0.0f, 0.0f, -1.0f}, {1.0f, 0.0f}},
        {{-0.5f,  0.5f, -0.5f}, {0.0f, 0.0f, -1.0f}, {1.0f, 1.0f}},
        {{ 0.5f,  0.5f, -0.5f}, {0.0f, 0.0f, -1.0f}, {0.0f, 1.0f}},

        // Left face
        {{-0.5f, -0.5f, -0.5f}, {-1.0f, 0.0f, 0.0f}, {0.0f, 0.0f}},
        {{-0.5f, -0.5f,  0.5f}, {-1.0f, 0.0f, 0.0f}, {1.0f, 0.0f}},
        {{-0.5f,  0.5f,  0.5f}, {-1.0f, 0.0f, 0.0f}, {1.0f, 1.0f}},
        {{-0.5f,  0.5f, -0.5f}, {-1.0f, 0.0f, 0.0f}, {0.0f, 1.0f}},

        // Right face
        {{ 0.5f, -0.5f,  0.5f}, {1.0f, 0.0f, 0.0f}, {0.0f, 0.0f}},
        {{ 0.5f, -0.5f, -0.5f}, {1.0f, 0.0f, 0.0f}, {1.0f, 0.0f}},
        {{ 0.5f,  0.5f, -0.5f}, {1.0f, 0.0f, 0.0f}, {1.0f, 1.0f}},
        {{ 0.5f,  0.5f,  0.5f}, {1.0f, 0.0f, 0.0f}, {0.0f, 1.0f}},

        // Top face
        {{-0.5f,  0.5f,  0.5f}, {0.0f, 1.0f, 0.0f}, {0.0f, 0.0f}},
        {{ 0.5f,  0.5f,  0.5f}, {0.0f, 1.0f, 0.0f}, {1.0f, 0.0f}},
        {{ 0.5f,  0.5f, -0.5f}, {0.0f, 1.0f, 0.0f}, {1.0f, 1.0f}},
        {{-0.5f,  0.5f, -0.5f}, {0.0f, 1.0f, 0.0f}, {0.0f, 1.0f}},

        // Bottom face
        {{-0.5f, -0.5f, -0.5f}, {0.0f, -1.0f, 0.0f}, {0.0f, 0.0f}},
        {{ 0.5f, -0.5f, -0.5f}, {0.0f, -1.0f, 0.0f}, {1.0f, 0.0f}},
        {{ 0.5f, -0.5f,  0.5f}, {0.0f, -1.0f, 0.0f}, {1.0f, 1.0f}},
        {{-0.5f, -0.5f,  0.5f}, {0.0f, -1.0f, 0.0f}, {0.0f, 1.0f}}
    };

    std::vector<uint16_t> indices = {
        // Front face
        0, 1, 2, 2, 3, 0,
        // Back face
        4, 5, 6, 6, 7, 4,
        // Left face
        8, 9, 10, 10, 11, 8,
        // Right face
        12, 13, 14, 14, 15, 12,
        // Top face
        16, 17, 18, 18, 19, 16,
        // Bottom face
        20, 21, 22, 22, 23, 20
    };

    auto mesh = std::make_shared<Mesh3D>();
    if (mesh->Initialize(m_metalDevice->GetDevice(), vertices, indices)) {
        return mesh;
    }

    return nullptr;
}

std::shared_ptr<Mesh3D> Renderer3D::CreateSphereMesh(int segments) {
    std::vector<Vertex3D> vertices;
    std::vector<uint16_t> indices;

    // Generate sphere vertices
    for (int lat = 0; lat <= segments; ++lat) {
        float theta = lat * M_PI / segments;
        float sinTheta = sinf(theta);
        float cosTheta = cosf(theta);

        for (int lon = 0; lon <= segments; ++lon) {
            float phi = lon * 2 * M_PI / segments;
            float sinPhi = sinf(phi);
            float cosPhi = cosf(phi);

            simd_float3 position = {
                cosPhi * sinTheta,
                cosTheta,
                sinPhi * sinTheta
            };

            simd_float3 normal = position; // For unit sphere, position = normal
            simd_float2 texCoord = {
                (float)lon / segments,
                (float)lat / segments
            };

            vertices.push_back({position, normal, texCoord});
        }
    }

    // Generate sphere indices
    for (int lat = 0; lat < segments; ++lat) {
        for (int lon = 0; lon < segments; ++lon) {
            int first = lat * (segments + 1) + lon;
            int second = first + segments + 1;

            indices.push_back(first);
            indices.push_back(second);
            indices.push_back(first + 1);

            indices.push_back(second);
            indices.push_back(second + 1);
            indices.push_back(first + 1);
        }
    }

    auto mesh = std::make_shared<Mesh3D>();
    if (mesh->Initialize(m_metalDevice->GetDevice(), vertices, indices)) {
        return mesh;
    }

    return nullptr;
}

std::shared_ptr<Mesh3D> Renderer3D::CreatePlaneMesh() {
    std::vector<Vertex3D> vertices = {
        {{-1.0f, 0.0f, -1.0f}, {0.0f, 1.0f, 0.0f}, {0.0f, 0.0f}},
        {{ 1.0f, 0.0f, -1.0f}, {0.0f, 1.0f, 0.0f}, {1.0f, 0.0f}},
        {{ 1.0f, 0.0f,  1.0f}, {0.0f, 1.0f, 0.0f}, {1.0f, 1.0f}},
        {{-1.0f, 0.0f,  1.0f}, {0.0f, 1.0f, 0.0f}, {0.0f, 1.0f}}
    };

    std::vector<uint16_t> indices = {
        0, 1, 2, 2, 3, 0
    };

    auto mesh = std::make_shared<Mesh3D>();
    if (mesh->Initialize(m_metalDevice->GetDevice(), vertices, indices)) {
        return mesh;
    }

    return nullptr;
}

// ============================================================================
// Utility Functions
// ============================================================================

void Register3DGraphicsComponents(ECS& ecs) {
    ecs.RegisterComponent<Renderable3D>();
    ecs.RegisterComponent<Camera3D>();
}

Entity Create3DCameraEntity(ECS& ecs, simd_float3 position, simd_float3 target) {
    Entity camera = ecs.CreateEntity();
    ecs.AddComponent(camera, Name("3D Camera"));
    ecs.AddComponent(camera, Transform({position.x, position.y, position.z}));
    ecs.AddComponent(camera, Camera3D(position, target));
    return camera;
}

Entity Create3DRenderableEntity(ECS& ecs, const Transform& transform, std::shared_ptr<Mesh3D> mesh, simd_float4 color) {
    Entity entity = ecs.CreateEntity();
    ecs.AddComponent(entity, transform);
    ecs.AddComponent(entity, Renderable3D(mesh, color));
    return entity;
}

} // namespace Graphics
} // namespace Engine
