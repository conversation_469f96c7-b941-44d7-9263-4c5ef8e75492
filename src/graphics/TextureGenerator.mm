//
// TextureGenerator.mm
// macOS 3D Looter-Shooter Game Engine
//
// Texture generation implementation
//

#include "TextureGenerator.h"
#include <iostream>
#include <vector>

#import <Metal/Metal.h>

namespace Engine {
namespace Graphics {

id<MTLTexture> TextureGenerator::CreateSolidColorTexture(id<MTLDevice> device, 
                                                        float r, float g, float b, float a,
                                                        int width, int height) {
    // Create texture descriptor
    MTLTextureDescriptor* descriptor = [MTLTextureDescriptor texture2DDescriptorWithPixelFormat:MTLPixelFormatRGBA8Unorm
                                                                                          width:width
                                                                                         height:height
                                                                                      mipmapped:NO];
    descriptor.usage = MTLTextureUsageShaderRead;
    descriptor.storageMode = MTLStorageModeShared;
    
    // Create texture
    id<MTLTexture> texture = [device newTextureWithDescriptor:descriptor];
    if (!texture) {
        std::cerr << "❌ Failed to create solid color texture" << std::endl;
        return nil;
    }
    
    // Create pixel data
    std::vector<uint8_t> pixelData(width * height * 4);
    FillTextureData(pixelData.data(), width, height, r, g, b, a);
    
    // Upload pixel data
    MTLRegion region = MTLRegionMake2D(0, 0, width, height);
    [texture replaceRegion:region mipmapLevel:0 withBytes:pixelData.data() bytesPerRow:width * 4];
    
    return texture;
}

id<MTLTexture> TextureGenerator::CreateCheckerboardTexture(id<MTLDevice> device,
                                                          float r1, float g1, float b1,
                                                          float r2, float g2, float b2,
                                                          int width, int height,
                                                          int checkerSize) {
    // Create texture descriptor
    MTLTextureDescriptor* descriptor = [MTLTextureDescriptor texture2DDescriptorWithPixelFormat:MTLPixelFormatRGBA8Unorm
                                                                                          width:width
                                                                                         height:height
                                                                                      mipmapped:NO];
    descriptor.usage = MTLTextureUsageShaderRead;
    descriptor.storageMode = MTLStorageModeShared;
    
    // Create texture
    id<MTLTexture> texture = [device newTextureWithDescriptor:descriptor];
    if (!texture) {
        std::cerr << "❌ Failed to create checkerboard texture" << std::endl;
        return nil;
    }
    
    // Create pixel data
    std::vector<uint8_t> pixelData(width * height * 4);
    FillCheckerboardData(pixelData.data(), width, height, r1, g1, b1, r2, g2, b2, checkerSize);
    
    // Upload pixel data
    MTLRegion region = MTLRegionMake2D(0, 0, width, height);
    [texture replaceRegion:region mipmapLevel:0 withBytes:pixelData.data() bytesPerRow:width * 4];
    
    return texture;
}

id<MTLTexture> TextureGenerator::CreateWhiteTexture(id<MTLDevice> device) {
    return CreateSolidColorTexture(device, 1.0f, 1.0f, 1.0f, 1.0f, 2, 2);
}

id<MTLTexture> TextureGenerator::CreateBlackTexture(id<MTLDevice> device) {
    return CreateSolidColorTexture(device, 0.0f, 0.0f, 0.0f, 1.0f, 2, 2);
}

id<MTLTexture> TextureGenerator::CreateNormalMapTexture(id<MTLDevice> device) {
    // Default normal map (pointing up: 0.5, 0.5, 1.0 in RGB = 0, 0, 1 in normal space)
    return CreateSolidColorTexture(device, 0.5f, 0.5f, 1.0f, 1.0f, 2, 2);
}

void TextureGenerator::FillTextureData(uint8_t* data, int width, int height, 
                                      float r, float g, float b, float a) {
    uint8_t red = static_cast<uint8_t>(r * 255.0f);
    uint8_t green = static_cast<uint8_t>(g * 255.0f);
    uint8_t blue = static_cast<uint8_t>(b * 255.0f);
    uint8_t alpha = static_cast<uint8_t>(a * 255.0f);
    
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            int index = (y * width + x) * 4;
            data[index + 0] = red;   // R
            data[index + 1] = green; // G
            data[index + 2] = blue;  // B
            data[index + 3] = alpha; // A
        }
    }
}

void TextureGenerator::FillCheckerboardData(uint8_t* data, int width, int height,
                                           float r1, float g1, float b1,
                                           float r2, float g2, float b2,
                                           int checkerSize) {
    uint8_t color1[4] = {
        static_cast<uint8_t>(r1 * 255.0f),
        static_cast<uint8_t>(g1 * 255.0f),
        static_cast<uint8_t>(b1 * 255.0f),
        255
    };
    
    uint8_t color2[4] = {
        static_cast<uint8_t>(r2 * 255.0f),
        static_cast<uint8_t>(g2 * 255.0f),
        static_cast<uint8_t>(b2 * 255.0f),
        255
    };
    
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            int index = (y * width + x) * 4;
            
            // Determine which checker we're in
            int checkerX = x / checkerSize;
            int checkerY = y / checkerSize;
            bool useColor1 = (checkerX + checkerY) % 2 == 0;
            
            uint8_t* color = useColor1 ? color1 : color2;
            data[index + 0] = color[0]; // R
            data[index + 1] = color[1]; // G
            data[index + 2] = color[2]; // B
            data[index + 3] = color[3]; // A
        }
    }
}

bool TextureGenerator::SaveTextureToFile(id<MTLTexture> texture, const std::string& filename) {
    // This is a placeholder - in a real implementation, you'd use Core Graphics or similar
    // to save the texture data to a PNG file
    std::cout << "📁 Texture save to " << filename << " not implemented yet" << std::endl;
    return false;
}

} // namespace Graphics
} // namespace Engine
