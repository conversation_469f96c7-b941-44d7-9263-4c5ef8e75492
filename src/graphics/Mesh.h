//
// Mesh.h
// macOS 3D Looter-Shooter Game Engine
//
// Mesh and Material classes for 3D rendering
//

#pragma once

#include "engine/Types.h"
#include "engine/Components.h"
#include "Shaders.h"
#include <Metal/Metal.h>
#include <vector>
#include <string>
#include <memory>

namespace Engine {
namespace Graphics {

// ============================================================================
// Material Class
// ============================================================================

class Material {
public:
    Material();
    ~Material();
    
    // Initialization
    bool Initialize(id<MTLDevice> device, const std::string& name = "DefaultMaterial");
    void Shutdown();
    
    // Properties
    void SetAlbedo(const Vector3& albedo) { m_albedo = albedo; m_dirty = true; }
    void SetMetallic(float metallic) { m_metallic = metallic; m_dirty = true; }
    void SetRoughness(float roughness) { m_roughness = roughness; m_dirty = true; }
    void SetEmissive(const Vector3& emissive) { m_emissive = emissive; m_dirty = true; }
    void SetEmissiveStrength(float strength) { m_emissiveStrength = strength; m_dirty = true; }
    void SetAO(float ao) { m_ao = ao; m_dirty = true; }
    void SetNormalScale(float scale) { m_normalScale = scale; m_dirty = true; }
    
    const Vector3& GetAlbedo() const { return m_albedo; }
    float GetMetallic() const { return m_metallic; }
    float GetRoughness() const { return m_roughness; }
    const Vector3& GetEmissive() const { return m_emissive; }
    float GetEmissiveStrength() const { return m_emissiveStrength; }
    float GetAO() const { return m_ao; }
    float GetNormalScale() const { return m_normalScale; }
    
    // Textures
    void SetAlbedoTexture(id<MTLTexture> texture) { m_albedoTexture = texture; }
    void SetNormalTexture(id<MTLTexture> texture) { m_normalTexture = texture; }
    void SetMetallicTexture(id<MTLTexture> texture) { m_metallicTexture = texture; }
    void SetRoughnessTexture(id<MTLTexture> texture) { m_roughnessTexture = texture; }
    void SetAOTexture(id<MTLTexture> texture) { m_aoTexture = texture; }
    void SetEmissiveTexture(id<MTLTexture> texture) { m_emissiveTexture = texture; }
    
    id<MTLTexture> GetAlbedoTexture() const { return m_albedoTexture; }
    id<MTLTexture> GetNormalTexture() const { return m_normalTexture; }
    id<MTLTexture> GetMetallicTexture() const { return m_metallicTexture; }
    id<MTLTexture> GetRoughnessTexture() const { return m_roughnessTexture; }
    id<MTLTexture> GetAOTexture() const { return m_aoTexture; }
    id<MTLTexture> GetEmissiveTexture() const { return m_emissiveTexture; }
    
    // Uniform buffer management
    id<MTLBuffer> GetUniformBuffer() const { return m_uniformBuffer; }
    void UpdateUniforms();
    
    // Binding
    void Bind(id<MTLRenderCommandEncoder> encoder, int bufferIndex = FragmentBufferIndexMaterialUniforms);
    
    const std::string& GetName() const { return m_name; }
    
private:
    id<MTLDevice> m_device;
    std::string m_name;
    
    // Material properties
    Vector3 m_albedo;
    float m_metallic;
    float m_roughness;
    Vector3 m_emissive;
    float m_emissiveStrength;
    float m_ao;
    float m_normalScale;
    
    // Textures
    id<MTLTexture> m_albedoTexture;
    id<MTLTexture> m_normalTexture;
    id<MTLTexture> m_metallicTexture;
    id<MTLTexture> m_roughnessTexture;
    id<MTLTexture> m_aoTexture;
    id<MTLTexture> m_emissiveTexture;
    
    // Uniform buffer
    id<MTLBuffer> m_uniformBuffer;
    bool m_dirty;
    bool m_initialized;
    
    // Default textures
    void CreateDefaultTextures();
};

// ============================================================================
// Mesh Class
// ============================================================================

class Mesh {
public:
    Mesh();
    ~Mesh();
    
    // Initialization
    bool Initialize(id<MTLDevice> device, const std::string& name = "DefaultMesh");
    void Shutdown();
    
    // Vertex data management
    void SetVertices(const std::vector<Vertex>& vertices);
    void SetVertices(const std::vector<PBRVertex>& vertices);
    void SetIndices(const std::vector<uint32_t>& indices);
    
    // Geometry creation helpers
    static std::shared_ptr<Mesh> CreateCube(id<MTLDevice> device, float size = 1.0f);
    static std::shared_ptr<Mesh> CreateSphere(id<MTLDevice> device, float radius = 1.0f, int segments = 32);
    static std::shared_ptr<Mesh> CreatePlane(id<MTLDevice> device, float width = 1.0f, float height = 1.0f);
    static std::shared_ptr<Mesh> CreateQuad(id<MTLDevice> device);
    
    // Rendering
    void Draw(id<MTLRenderCommandEncoder> encoder);
    void DrawInstanced(id<MTLRenderCommandEncoder> encoder, int instanceCount);
    
    // Properties
    size_t GetVertexCount() const { return m_vertexCount; }
    size_t GetIndexCount() const { return m_indexCount; }
    size_t GetTriangleCount() const { return m_indexCount / 3; }
    bool HasIndices() const { return m_indexBuffer != nil; }
    bool IsPBR() const { return m_isPBR; }
    
    const std::string& GetName() const { return m_name; }
    
    // Bounding box
    Vector3 GetBoundsMin() const { return m_boundsMin; }
    Vector3 GetBoundsMax() const { return m_boundsMax; }
    Vector3 GetBoundsCenter() const { return (m_boundsMin + m_boundsMax) * 0.5f; }
    Vector3 GetBoundsSize() const { return m_boundsMax - m_boundsMin; }
    
private:
    id<MTLDevice> m_device;
    std::string m_name;
    
    // Vertex data
    id<MTLBuffer> m_vertexBuffer;
    id<MTLBuffer> m_indexBuffer;
    size_t m_vertexCount;
    size_t m_indexCount;
    bool m_isPBR;
    
    // Bounding box
    Vector3 m_boundsMin;
    Vector3 m_boundsMax;
    
    bool m_initialized;
    
    // Helper methods
    void CalculateBounds(const std::vector<Vertex>& vertices);
    void CalculateBounds(const std::vector<PBRVertex>& vertices);
    void CalculateTangents(std::vector<PBRVertex>& vertices, const std::vector<uint32_t>& indices);
};

// ============================================================================
// Mesh Component for ECS
// ============================================================================

struct MeshComponent {
    std::shared_ptr<Mesh> mesh;
    std::shared_ptr<Material> material;
    bool visible = true;
    float lodDistance = 100.0f;
    int lodLevel = 0;
    
    MeshComponent() = default;
    MeshComponent(std::shared_ptr<Mesh> m, std::shared_ptr<Material> mat = nullptr)
        : mesh(m), material(mat) {}
};

// ============================================================================
// Mesh Manager
// ============================================================================

class MeshManager {
public:
    MeshManager();
    ~MeshManager();
    
    // Initialization
    bool Initialize(id<MTLDevice> device);
    void Shutdown();
    
    // Mesh management
    std::shared_ptr<Mesh> CreateMesh(const std::string& name);
    std::shared_ptr<Mesh> GetMesh(const std::string& name);
    void RemoveMesh(const std::string& name);
    
    // Material management
    std::shared_ptr<Material> CreateMaterial(const std::string& name);
    std::shared_ptr<Material> GetMaterial(const std::string& name);
    void RemoveMaterial(const std::string& name);
    
    // Primitive creation
    std::shared_ptr<Mesh> GetCubeMesh();
    std::shared_ptr<Mesh> GetSphereMesh();
    std::shared_ptr<Mesh> GetPlaneMesh();
    std::shared_ptr<Mesh> GetQuadMesh();
    
    // Default materials
    std::shared_ptr<Material> GetDefaultMaterial();
    std::shared_ptr<Material> GetErrorMaterial();
    
    // Statistics
    size_t GetMeshCount() const { return m_meshes.size(); }
    size_t GetMaterialCount() const { return m_materials.size(); }
    
private:
    id<MTLDevice> m_device;
    
    // Resource storage
    std::unordered_map<std::string, std::shared_ptr<Mesh>> m_meshes;
    std::unordered_map<std::string, std::shared_ptr<Material>> m_materials;
    
    // Primitive meshes
    std::shared_ptr<Mesh> m_cubeMesh;
    std::shared_ptr<Mesh> m_sphereMesh;
    std::shared_ptr<Mesh> m_planeMesh;
    std::shared_ptr<Mesh> m_quadMesh;
    
    // Default materials
    std::shared_ptr<Material> m_defaultMaterial;
    std::shared_ptr<Material> m_errorMaterial;
    
    bool m_initialized;
    
    // Helper methods
    void CreatePrimitiveMeshes();
    void CreateDefaultMaterials();
};

} // namespace Graphics
} // namespace Engine
