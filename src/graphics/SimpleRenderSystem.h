//
// SimpleRenderSystem.h
// macOS 3D Looter-Shooter Game Engine
//
// Simplified rendering system for initial testing
//

#pragma once

#include "engine/System.h"
#include "engine/Components.h"
#include <iostream>

// Forward declarations
namespace Engine {
    class ECS;
}

namespace Engine {
namespace Graphics {

// ============================================================================
// Simple Camera Component
// ============================================================================

struct SimpleCamera {
    Vector3 position = {0.0f, 0.0f, 5.0f};
    Vector3 target = {0.0f, 0.0f, 0.0f};
    Vector3 up = {0.0f, 1.0f, 0.0f};
    
    float fov = 60.0f;          // Field of view in degrees
    float nearPlane = 0.1f;
    float farPlane = 1000.0f;
    
    SimpleCamera() = default;
    SimpleCamera(const Vector3& pos, const Vector3& tgt) : position(pos), target(tgt) {}
};

// ============================================================================
// Simple Mesh Component
// ============================================================================

struct SimpleMesh {
    std::string meshName = "cube";
    std::string materialName = "default";
    bool visible = true;
    Vector4 color = {1.0f, 1.0f, 1.0f, 1.0f};
    
    SimpleMesh() = default;
    SimpleMesh(const std::string& mesh) : meshName(mesh) {}
    SimpleMesh(const std::string& mesh, const Vector4& col) : meshName(mesh), color(col) {}
};

// ============================================================================
// Simple Render System
// ============================================================================

class SimpleRenderSystem : public System {
public:
    SimpleRenderSystem();
    ~SimpleRenderSystem();
    
    // System interface
    void Initialize() override;
    void Update(DeltaTime deltaTime) override;
    void Shutdown() override;
    const char* GetName() const override { return "SimpleRenderSystem"; }
    
    // Camera management
    void SetActiveCamera(Entity cameraEntity);
    Entity GetActiveCamera() const { return m_activeCamera; }
    
    // Render settings
    void SetClearColor(const Vector4& color) { m_clearColor = color; }
    
    // Statistics
    struct RenderStats {
        uint32_t frameCount = 0;
        uint32_t drawCalls = 0;
        uint32_t entities = 0;
        float frameTime = 0.0f;
    };
    
    const RenderStats& GetStats() const { return m_stats; }
    void ResetStats() { m_stats = {}; }
    
private:
    Entity m_activeCamera;
    Vector4 m_clearColor;
    RenderStats m_stats;
    float m_totalTime;
    
    bool m_initialized;
    
    // Helper methods
    void RenderEntities();
    void PrintEntityInfo(Entity entity, const Transform& transform, const SimpleMesh& mesh);
    void UpdateStats();
};

// ============================================================================
// Helper Functions
// ============================================================================

// Helper function to register simple graphics components with ECS
void RegisterSimpleGraphicsComponents(ECS& ecs);

// Helper function to create a basic camera entity
Entity CreateSimpleCameraEntity(ECS& ecs, const Vector3& position = {0.0f, 0.0f, 5.0f}, const Vector3& target = {0.0f, 0.0f, 0.0f});

// Helper function to create a basic mesh entity
Entity CreateSimpleMeshEntity(ECS& ecs, const std::string& meshName = "cube", const Vector4& color = {1.0f, 1.0f, 1.0f, 1.0f}, const Transform& transform = Transform());

} // namespace Graphics
} // namespace Engine
