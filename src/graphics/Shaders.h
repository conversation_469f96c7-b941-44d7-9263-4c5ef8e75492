//
// Shaders.h
// macOS 3D Looter-Shooter Game Engine
//
// Shader definitions and vertex structures for Metal rendering
//

#pragma once

#include "engine/Types.h"
#include "engine/Components.h"

#ifdef __OBJC__
#import <Metal/Metal.h>
#else
#include <Metal/Metal.h>
#endif

#include <simd/simd.h>

namespace Engine {
namespace Graphics {

// ============================================================================
// Vertex Structures
// ============================================================================

// Basic vertex with position, normal, and UV coordinates
struct Vertex {
    simd_float3 position;
    simd_float3 normal;
    simd_float2 texCoord;
    
    Vertex() = default;
    Vertex(const Vector3& pos, const Vector3& norm, float u = 0.0f, float v = 0.0f)
        : position{pos.x, pos.y, pos.z}
        , normal{norm.x, norm.y, norm.z}
        , texCoord{u, v} {}
};

// Extended vertex for PBR rendering
struct PBRVertex {
    simd_float3 position;
    simd_float3 normal;
    simd_float3 tangent;
    simd_float2 texCoord;
    simd_float4 color;
    
    PBRVertex() = default;
    PBRVertex(const Vector3& pos, const Vector3& norm, const Vector3& tan, float u = 0.0f, float v = 0.0f)
        : position{pos.x, pos.y, pos.z}
        , normal{norm.x, norm.y, norm.z}
        , tangent{tan.x, tan.y, tan.z}
        , texCoord{u, v}
        , color{1.0f, 1.0f, 1.0f, 1.0f} {}
};

// ============================================================================
// Uniform Structures
// ============================================================================

// Per-frame uniforms (updated once per frame)
struct FrameUniforms {
    simd_float4x4 viewMatrix;
    simd_float4x4 projectionMatrix;
    simd_float4x4 viewProjectionMatrix;
    simd_float3 cameraPosition;
    float time;
    simd_float3 lightDirection;
    float deltaTime;
    simd_float3 lightColor;
    float ambientIntensity;
};

// Per-object uniforms (updated per draw call)
struct ObjectUniforms {
    simd_float4x4 modelMatrix;
    simd_float4x4 normalMatrix;
    simd_float4x4 mvpMatrix;
    simd_float4 color;
    float metallic;
    float roughness;
    float ao; // Ambient occlusion
    float _padding;
};

// Material properties for PBR
struct MaterialUniforms {
    simd_float3 albedo;
    float metallic;
    simd_float3 emissive;
    float roughness;
    float ao;
    float normalScale;
    float emissiveStrength;
    float _padding;
};

// ============================================================================
// Shader Resource Indices
// ============================================================================

// Vertex shader buffer indices
enum VertexBufferIndex {
    VertexBufferIndexVertices = 0,
    VertexBufferIndexFrameUniforms = 1,
    VertexBufferIndexObjectUniforms = 2
};

// Fragment shader buffer indices
enum FragmentBufferIndex {
    FragmentBufferIndexFrameUniforms = 0,
    FragmentBufferIndexMaterialUniforms = 1
};

// Texture indices
enum TextureIndex {
    TextureIndexAlbedo = 0,
    TextureIndexNormal = 1,
    TextureIndexMetallic = 2,
    TextureIndexRoughness = 3,
    TextureIndexAO = 4,
    TextureIndexEmissive = 5,
    TextureIndexShadowMap = 6,
    TextureIndexEnvironment = 7
};

// ============================================================================
// Shader Manager
// ============================================================================

class ShaderManager {
public:
    ShaderManager();
    ~ShaderManager();
    
    // Initialization
    bool Initialize(id<MTLDevice> device, id<MTLLibrary> library);
    void Shutdown();
    
    // Shader loading
    id<MTLFunction> LoadVertexShader(const char* name);
    id<MTLFunction> LoadFragmentShader(const char* name);
    id<MTLFunction> LoadComputeShader(const char* name);
    
    // Pipeline state creation
    id<MTLRenderPipelineState> CreateBasicPipeline(MTLPixelFormat colorFormat, MTLPixelFormat depthFormat);
    id<MTLRenderPipelineState> CreatePBRPipeline(MTLPixelFormat colorFormat, MTLPixelFormat depthFormat);
    id<MTLRenderPipelineState> CreateShadowPipeline(MTLPixelFormat depthFormat);
    
    // Depth state creation
    id<MTLDepthStencilState> CreateDepthState(bool depthWrite = true, MTLCompareFunction depthCompare = MTLCompareFunctionLess);
    
    // Uniform buffer management
    id<MTLBuffer> CreateFrameUniformBuffer();
    id<MTLBuffer> CreateObjectUniformBuffer();
    id<MTLBuffer> CreateMaterialUniformBuffer();
    
    void UpdateFrameUniforms(id<MTLBuffer> buffer, const FrameUniforms& uniforms);
    void UpdateObjectUniforms(id<MTLBuffer> buffer, const ObjectUniforms& uniforms);
    void UpdateMaterialUniforms(id<MTLBuffer> buffer, const MaterialUniforms& uniforms);
    
    // Utility functions
    simd_float4x4 CreateModelMatrix(const Transform& transform);
    simd_float4x4 CreateViewMatrix(const Vector3& position, const Vector3& target, const Vector3& up);
    simd_float4x4 CreateProjectionMatrix(float fov, float aspect, float nearPlane, float farPlane);
    
private:
    id<MTLDevice> m_device;
    id<MTLLibrary> m_library;
    
    // Cached shaders
    id<MTLFunction> m_basicVertexShader;
    id<MTLFunction> m_basicFragmentShader;
    id<MTLFunction> m_pbrVertexShader;
    id<MTLFunction> m_pbrFragmentShader;
    id<MTLFunction> m_shadowVertexShader;
    id<MTLFunction> m_shadowFragmentShader;
    
    // Cached pipeline states
    id<MTLRenderPipelineState> m_basicPipeline;
    id<MTLRenderPipelineState> m_pbrPipeline;
    id<MTLRenderPipelineState> m_shadowPipeline;
    
    // Cached depth states
    id<MTLDepthStencilState> m_depthStateWrite;
    id<MTLDepthStencilState> m_depthStateReadOnly;
    
    bool m_initialized;
    
    // Helper methods
    bool LoadBasicShaders();
    bool LoadPBRShaders();
    bool LoadShadowShaders();
    bool CreatePipelineStates();
    bool CreateDepthStates();
};

// ============================================================================
// Math Utility Functions for Shaders
// ============================================================================

namespace ShaderMath {
    // Matrix creation
    simd_float4x4 matrix_identity();
    simd_float4x4 matrix_translation(const Vector3& translation);
    simd_float4x4 matrix_rotation_x(float radians);
    simd_float4x4 matrix_rotation_y(float radians);
    simd_float4x4 matrix_rotation_z(float radians);
    simd_float4x4 matrix_rotation(const Vector3& rotation);
    simd_float4x4 matrix_scale(const Vector3& scale);
    simd_float4x4 matrix_look_at(const Vector3& eye, const Vector3& target, const Vector3& up);
    simd_float4x4 matrix_perspective(float fov, float aspect, float nearPlane, float farPlane);
    simd_float4x4 matrix_orthographic(float left, float right, float bottom, float top, float nearPlane, float farPlane);
    
    // Matrix operations
    simd_float4x4 matrix_multiply(const simd_float4x4& a, const simd_float4x4& b);
    simd_float4x4 matrix_transpose(const simd_float4x4& matrix);
    simd_float4x4 matrix_inverse(const simd_float4x4& matrix);
    
    // Vector conversions
    simd_float3 vector3_from_engine(const Vector3& v);
    Vector3 vector3_to_engine(const simd_float3& v);
    simd_float4 vector4_from_engine(const Vector4& v);
    Vector4 vector4_to_engine(const simd_float4& v);
}

} // namespace Graphics
} // namespace Engine
