//
// MetalDevice.h
// macOS 3D Looter-Shooter Game Engine
//
// Metal device and context management for macOS
//

#pragma once

#ifdef __OBJC__
#import <Metal/Metal.h>
#import <MetalKit/MetalKit.h>
#import <Foundation/Foundation.h>
#else
#include <Metal/Metal.h>
#include <MetalKit/MetalKit.h>
#endif

#include "engine/Types.h"
#include <memory>
#include <string>

namespace Engine {
namespace Graphics {

// ============================================================================
// Metal Device Manager
// ============================================================================

class MetalDevice {
public:
    MetalDevice();
    ~MetalDevice();
    
    // Initialization and cleanup
    bool Initialize();
    void Shutdown();
    
    // Device access
    id<MTLDevice> GetDevice() const { return m_device; }
    id<MTLCommandQueue> GetCommandQueue() const { return m_commandQueue; }
    id<MTLLibrary> GetDefaultLibrary() const { return m_defaultLibrary; }
    
    // Device capabilities
    bool SupportsFeatureSet(MTLFeatureSet featureSet) const;
    bool SupportsFamily(MTLGPUFamily family) const;
    size_t GetMaxBufferLength() const;
    size_t GetRecommendedMaxWorkingSetSize() const;
    
    // Debug information
    void PrintDeviceInfo() const;
    const std::string& GetDeviceName() const { return m_deviceName; }
    bool IsLowPower() const { return m_isLowPower; }
    
    // Singleton access
    static MetalDevice* GetInstance() { return s_instance; }
    
private:
    id<MTLDevice> m_device;
    id<MTLCommandQueue> m_commandQueue;
    id<MTLLibrary> m_defaultLibrary;
    
    // Device properties
    std::string m_deviceName;
    bool m_isLowPower;
    bool m_initialized;
    
    // Singleton instance
    static MetalDevice* s_instance;
    
    // Helper methods
    bool CreateDevice();
    bool CreateCommandQueue();
    bool CreateDefaultLibrary();
    void QueryDeviceCapabilities();
};

// ============================================================================
// Metal Context
// ============================================================================

class MetalContext {
public:
    MetalContext();
    ~MetalContext();
    
    // Initialization
    bool Initialize(int width, int height, bool vsync = true);
    void Shutdown();
    
    // Frame management
    void BeginFrame();
    void EndFrame();
    void Present();
    
    // Command buffer management
    id<MTLCommandBuffer> CreateCommandBuffer();
    id<MTLRenderCommandEncoder> CreateRenderEncoder(MTLRenderPassDescriptor* renderPass);
    
    // Render targets
    MTLRenderPassDescriptor* GetCurrentRenderPassDescriptor();
    id<MTLTexture> GetCurrentDrawable();
    
    // Properties
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    float GetAspectRatio() const { return static_cast<float>(m_width) / static_cast<float>(m_height); }
    bool IsVSyncEnabled() const { return m_vsyncEnabled; }
    
    // Statistics
    uint64_t GetFrameCount() const { return m_frameCount; }
    
private:
    MetalDevice* m_device;
    
    // Render targets
    id<MTLTexture> m_colorTexture;
    id<MTLTexture> m_depthTexture;
    MTLRenderPassDescriptor* m_renderPassDescriptor;
    
    // Current frame state
    id<MTLCommandBuffer> m_currentCommandBuffer;
    id<MTLRenderCommandEncoder> m_currentRenderEncoder;
    
    // Properties
    int m_width;
    int m_height;
    bool m_vsyncEnabled;
    bool m_initialized;
    uint64_t m_frameCount;
    
    // Helper methods
    bool CreateRenderTargets();
    bool CreateRenderPassDescriptor();
    void ResizeRenderTargets(int width, int height);
};

// ============================================================================
// Metal Utility Functions
// ============================================================================

namespace MetalUtils {
    // Error handling
    bool CheckError(NSError* error, const char* operation);
    
    // Format utilities
    MTLPixelFormat GetOptimalColorFormat();
    MTLPixelFormat GetOptimalDepthFormat();
    
    // Buffer creation helpers
    id<MTLBuffer> CreateBuffer(id<MTLDevice> device, const void* data, size_t size, MTLResourceOptions options = MTLResourceStorageModeShared);
    
    template<typename T>
    id<MTLBuffer> CreateBuffer(id<MTLDevice> device, const T& data, MTLResourceOptions options = MTLResourceStorageModeShared) {
        return CreateBuffer(device, &data, sizeof(T), options);
    }
    
    template<typename T>
    id<MTLBuffer> CreateBuffer(id<MTLDevice> device, const std::vector<T>& data, MTLResourceOptions options = MTLResourceStorageModeShared) {
        return CreateBuffer(device, data.data(), data.size() * sizeof(T), options);
    }
    
    // Texture creation helpers
    id<MTLTexture> CreateTexture2D(id<MTLDevice> device, int width, int height, MTLPixelFormat format, MTLTextureUsage usage = MTLTextureUsageShaderRead);
    
    // Shader compilation
    id<MTLFunction> LoadFunction(id<MTLLibrary> library, const char* functionName);
    id<MTLRenderPipelineState> CreateRenderPipelineState(id<MTLDevice> device, id<MTLFunction> vertexFunction, id<MTLFunction> fragmentFunction, MTLPixelFormat colorFormat, MTLPixelFormat depthFormat);
}

} // namespace Graphics
} // namespace Engine
