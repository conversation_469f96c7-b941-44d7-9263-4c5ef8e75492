//
// RenderSystem.h
// macOS 3D Looter-Shooter Game Engine
//
// Rendering system that integrates with the ECS
//

#pragma once

#include "engine/System.h"
#include "engine/Components.h"
#include "MetalDevice.h"
#include "Shaders.h"
#include "Mesh.h"

#ifdef __OBJC__
#import <Metal/Metal.h>
#else
#include <Metal/Metal.h>
#endif

#include <memory>
#include <vector>

// Forward declarations
namespace Engine {
    class ECS;
}

namespace Engine {
namespace Graphics {

// ============================================================================
// Camera Component
// ============================================================================

struct Camera {
    Vector3 position = {0.0f, 0.0f, 5.0f};
    Vector3 target = {0.0f, 0.0f, 0.0f};
    Vector3 up = {0.0f, 1.0f, 0.0f};
    
    float fov = 60.0f;          // Field of view in degrees
    float nearPlane = 0.1f;
    float farPlane = 1000.0f;
    
    bool isPerspective = true;
    float orthoSize = 10.0f;    // For orthographic projection
    
    Camera() = default;
    Camera(const Vector3& pos, const Vector3& tgt) : position(pos), target(tgt) {}
    
    Vector3 GetForward() const {
        Vector3 forward = target - position;
        forward.Normalize();
        return forward;
    }
    
    Vector3 GetRight() const {
        Vector3 forward = GetForward();
        Vector3 right = forward.Cross(up);
        right.Normalize();
        return right;
    }
    
    Vector3 GetUp() const {
        Vector3 forward = GetForward();
        Vector3 right = GetRight();
        Vector3 actualUp = right.Cross(forward);
        actualUp.Normalize();
        return actualUp;
    }
};

// ============================================================================
// Light Component
// ============================================================================

struct DirectionalLight {
    Vector3 direction = {-0.5f, -1.0f, -0.5f};
    Vector3 color = {1.0f, 1.0f, 1.0f};
    float intensity = 1.0f;
    bool castShadows = true;
    
    DirectionalLight() {
        direction.Normalize();
    }
};

struct PointLight {
    Vector3 position = {0.0f, 0.0f, 0.0f};
    Vector3 color = {1.0f, 1.0f, 1.0f};
    float intensity = 1.0f;
    float range = 10.0f;
    bool castShadows = false;
};

struct SpotLight {
    Vector3 position = {0.0f, 0.0f, 0.0f};
    Vector3 direction = {0.0f, -1.0f, 0.0f};
    Vector3 color = {1.0f, 1.0f, 1.0f};
    float intensity = 1.0f;
    float range = 10.0f;
    float innerCone = 30.0f;    // Inner cone angle in degrees
    float outerCone = 45.0f;    // Outer cone angle in degrees
    bool castShadows = false;
};

// ============================================================================
// Render Statistics
// ============================================================================

struct RenderStats {
    uint32_t frameCount = 0;
    uint32_t drawCalls = 0;
    uint32_t triangles = 0;
    uint32_t vertices = 0;
    float frameTime = 0.0f;
    float renderTime = 0.0f;
    
    void Reset() {
        drawCalls = 0;
        triangles = 0;
        vertices = 0;
        renderTime = 0.0f;
    }
};

// ============================================================================
// Render System
// ============================================================================

class RenderSystem : public System {
public:
    RenderSystem();
    ~RenderSystem();
    
    // System interface
    void Initialize() override;
    void Update(DeltaTime deltaTime) override;
    void Shutdown() override;
    const char* GetName() const override { return "RenderSystem"; }
    
    // Initialization
    bool InitializeGraphics(int width, int height, bool vsync = true);
    void ShutdownGraphics();
    
    // Frame management
    void BeginFrame();
    void EndFrame();
    void Present();
    
    // Camera management
    void SetActiveCamera(Entity cameraEntity);
    Entity GetActiveCamera() const { return m_activeCamera; }
    
    // Lighting
    void SetDirectionalLight(const DirectionalLight& light) { m_directionalLight = light; }
    void SetAmbientLight(const Vector3& color, float intensity) { 
        m_ambientColor = color; 
        m_ambientIntensity = intensity; 
    }
    
    // Render settings
    void SetClearColor(const Vector4& color) { m_clearColor = color; }
    void SetWireframeMode(bool enabled) { m_wireframeMode = enabled; }
    void SetCullingEnabled(bool enabled) { m_cullingEnabled = enabled; }
    
    // Statistics
    const RenderStats& GetStats() const { return m_stats; }
    void ResetStats() { m_stats.Reset(); }
    
    // Debug rendering
    void DrawDebugLine(const Vector3& start, const Vector3& end, const Vector4& color = {1.0f, 1.0f, 1.0f, 1.0f});
    void DrawDebugSphere(const Vector3& center, float radius, const Vector4& color = {1.0f, 1.0f, 1.0f, 1.0f});
    void DrawDebugBox(const Vector3& center, const Vector3& size, const Vector4& color = {1.0f, 1.0f, 1.0f, 1.0f});
    
private:
    // Graphics subsystems
    std::unique_ptr<MetalDevice> m_device;
    std::unique_ptr<MetalContext> m_context;
    std::unique_ptr<ShaderManager> m_shaderManager;
    std::unique_ptr<MeshManager> m_meshManager;
    
    // Render state
    Entity m_activeCamera;
    DirectionalLight m_directionalLight;
    Vector3 m_ambientColor;
    float m_ambientIntensity;
    Vector4 m_clearColor;
    bool m_wireframeMode;
    bool m_cullingEnabled;
    
    // Uniform buffers
    id<MTLBuffer> m_frameUniformBuffer;
    id<MTLBuffer> m_objectUniformBuffer;
    
    // Render statistics
    RenderStats m_stats;
    std::chrono::high_resolution_clock::time_point m_frameStartTime;
    
    // Debug rendering
    std::vector<std::pair<Vector3, Vector3>> m_debugLines;
    std::vector<Vector4> m_debugLineColors;
    
    bool m_initialized;
    
    // Helper methods
    void UpdateFrameUniforms(const Camera& camera, float aspectRatio);
    void UpdateObjectUniforms(const Transform& transform);
    void RenderMeshes();
    void RenderDebugGeometry();
    
    // Culling and sorting
    std::vector<Entity> CullAndSortEntities(const Camera& camera);
    bool IsEntityVisible(Entity entity, const Camera& camera);
    float CalculateDistanceToCamera(Entity entity, const Camera& camera);
    
    // Matrix calculations
    simd_float4x4 CalculateViewMatrix(const Camera& camera);
    simd_float4x4 CalculateProjectionMatrix(const Camera& camera, float aspectRatio);
    simd_float4x4 CalculateModelMatrix(const Transform& transform);
};

// ============================================================================
// Graphics Components for ECS Registration
// ============================================================================

// Helper function to register all graphics components with ECS
void RegisterGraphicsComponents(ECS& ecs);

// Helper function to create a basic camera entity
Entity CreateCameraEntity(ECS& ecs, const Vector3& position = {0.0f, 0.0f, 5.0f}, const Vector3& target = {0.0f, 0.0f, 0.0f});

// Helper function to create a basic mesh entity
Entity CreateMeshEntity(ECS& ecs, std::shared_ptr<Mesh> mesh, std::shared_ptr<Material> material = nullptr, const Transform& transform = Transform());

} // namespace Graphics
} // namespace Engine
