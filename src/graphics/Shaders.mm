//
// Shaders.mm
// macOS 3D Looter-Shooter Game Engine
//
// Shader manager implementation
//

#include "Shaders.h"
#include "MetalDevice.h"
#include <iostream>
#include <cmath>

namespace Engine {
namespace Graphics {

// ============================================================================
// ShaderManager Implementation
// ============================================================================

ShaderManager::ShaderManager()
    : m_device(nil)
    , m_library(nil)
    , m_basicVertexShader(nil)
    , m_basicFragmentShader(nil)
    , m_pbrVertexShader(nil)
    , m_pbrFragmentShader(nil)
    , m_shadowVertexShader(nil)
    , m_shadowFragmentShader(nil)
    , m_basicPipeline(nil)
    , m_pbrPipeline(nil)
    , m_shadowPipeline(nil)
    , m_depthStateWrite(nil)
    , m_depthStateReadOnly(nil)
    , m_initialized(false) {
}

ShaderManager::~ShaderManager() {
    if (m_initialized) {
        Shutdown();
    }
}

bool ShaderManager::Initialize(id<MTLDevice> device, id<MTLLibrary> library) {
    ENGINE_ASSERT(!m_initialized, "ShaderManager already initialized");
    
    m_device = device;
    m_library = library;
    
    std::cout << "🔧 Initializing ShaderManager..." << std::endl;
    
    if (!LoadBasicShaders()) {
        std::cerr << "❌ Failed to load basic shaders" << std::endl;
        return false;
    }
    
    if (!CreateDepthStates()) {
        std::cerr << "❌ Failed to create depth states" << std::endl;
        return false;
    }
    
    m_initialized = true;
    std::cout << "✅ ShaderManager initialized successfully" << std::endl;
    
    return true;
}

void ShaderManager::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "🔧 Shutting down ShaderManager..." << std::endl;
    
    // Release depth states
    if (m_depthStateReadOnly) {
        [m_depthStateReadOnly release];
        m_depthStateReadOnly = nil;
    }
    
    if (m_depthStateWrite) {
        [m_depthStateWrite release];
        m_depthStateWrite = nil;
    }
    
    // Release pipeline states
    if (m_shadowPipeline) {
        [m_shadowPipeline release];
        m_shadowPipeline = nil;
    }
    
    if (m_pbrPipeline) {
        [m_pbrPipeline release];
        m_pbrPipeline = nil;
    }
    
    if (m_basicPipeline) {
        [m_basicPipeline release];
        m_basicPipeline = nil;
    }
    
    // Release shaders
    if (m_shadowFragmentShader) {
        [m_shadowFragmentShader release];
        m_shadowFragmentShader = nil;
    }
    
    if (m_shadowVertexShader) {
        [m_shadowVertexShader release];
        m_shadowVertexShader = nil;
    }
    
    if (m_pbrFragmentShader) {
        [m_pbrFragmentShader release];
        m_pbrFragmentShader = nil;
    }
    
    if (m_pbrVertexShader) {
        [m_pbrVertexShader release];
        m_pbrVertexShader = nil;
    }
    
    if (m_basicFragmentShader) {
        [m_basicFragmentShader release];
        m_basicFragmentShader = nil;
    }
    
    if (m_basicVertexShader) {
        [m_basicVertexShader release];
        m_basicVertexShader = nil;
    }
    
    m_initialized = false;
    std::cout << "✅ ShaderManager shutdown complete" << std::endl;
}

bool ShaderManager::LoadBasicShaders() {
    // Load basic vertex shader
    m_basicVertexShader = MetalUtils::LoadFunction(m_library, "basic_vertex_shader");
    if (!m_basicVertexShader) {
        std::cerr << "❌ Failed to load basic vertex shader" << std::endl;
        return false;
    }
    
    // Load basic fragment shader
    m_basicFragmentShader = MetalUtils::LoadFunction(m_library, "basic_fragment_shader");
    if (!m_basicFragmentShader) {
        std::cerr << "❌ Failed to load basic fragment shader" << std::endl;
        return false;
    }
    
    std::cout << "✅ Basic shaders loaded successfully" << std::endl;
    return true;
}

bool ShaderManager::CreateDepthStates() {
    // Create depth state with writing enabled
    MTLDepthStencilDescriptor* depthDescriptor = [[MTLDepthStencilDescriptor alloc] init];
    depthDescriptor.depthCompareFunction = MTLCompareFunctionLess;
    depthDescriptor.depthWriteEnabled = YES;
    
    m_depthStateWrite = [m_device newDepthStencilStateWithDescriptor:depthDescriptor];
    if (!m_depthStateWrite) {
        std::cerr << "❌ Failed to create depth state with writing" << std::endl;
        [depthDescriptor release];
        return false;
    }
    
    // Create depth state with writing disabled
    depthDescriptor.depthWriteEnabled = NO;
    m_depthStateReadOnly = [m_device newDepthStencilStateWithDescriptor:depthDescriptor];
    if (!m_depthStateReadOnly) {
        std::cerr << "❌ Failed to create read-only depth state" << std::endl;
        [depthDescriptor release];
        return false;
    }
    
    [depthDescriptor release];
    return true;
}

id<MTLFunction> ShaderManager::LoadVertexShader(const char* name) {
    return MetalUtils::LoadFunction(m_library, name);
}

id<MTLFunction> ShaderManager::LoadFragmentShader(const char* name) {
    return MetalUtils::LoadFunction(m_library, name);
}

id<MTLFunction> ShaderManager::LoadComputeShader(const char* name) {
    return MetalUtils::LoadFunction(m_library, name);
}

id<MTLRenderPipelineState> ShaderManager::CreateBasicPipeline(MTLPixelFormat colorFormat, MTLPixelFormat depthFormat) {
    if (m_basicPipeline) {
        return m_basicPipeline;
    }
    
    m_basicPipeline = MetalUtils::CreateRenderPipelineState(m_device, m_basicVertexShader, m_basicFragmentShader, colorFormat, depthFormat);
    return m_basicPipeline;
}

id<MTLDepthStencilState> ShaderManager::CreateDepthState(bool depthWrite, MTLCompareFunction depthCompare) {
    if (depthWrite && depthCompare == MTLCompareFunctionLess) {
        return m_depthStateWrite;
    } else if (!depthWrite && depthCompare == MTLCompareFunctionLess) {
        return m_depthStateReadOnly;
    }
    
    // Create custom depth state
    MTLDepthStencilDescriptor* descriptor = [[MTLDepthStencilDescriptor alloc] init];
    descriptor.depthCompareFunction = depthCompare;
    descriptor.depthWriteEnabled = depthWrite;
    
    id<MTLDepthStencilState> depthState = [m_device newDepthStencilStateWithDescriptor:descriptor];
    [descriptor release];
    
    return depthState;
}

id<MTLBuffer> ShaderManager::CreateFrameUniformBuffer() {
    return MetalUtils::CreateBuffer(m_device, nullptr, sizeof(FrameUniforms), MTLResourceStorageModeShared);
}

id<MTLBuffer> ShaderManager::CreateObjectUniformBuffer() {
    return MetalUtils::CreateBuffer(m_device, nullptr, sizeof(ObjectUniforms), MTLResourceStorageModeShared);
}

id<MTLBuffer> ShaderManager::CreateMaterialUniformBuffer() {
    return MetalUtils::CreateBuffer(m_device, nullptr, sizeof(MaterialUniforms), MTLResourceStorageModeShared);
}

void ShaderManager::UpdateFrameUniforms(id<MTLBuffer> buffer, const FrameUniforms& uniforms) {
    memcpy([buffer contents], &uniforms, sizeof(FrameUniforms));
}

void ShaderManager::UpdateObjectUniforms(id<MTLBuffer> buffer, const ObjectUniforms& uniforms) {
    memcpy([buffer contents], &uniforms, sizeof(ObjectUniforms));
}

void ShaderManager::UpdateMaterialUniforms(id<MTLBuffer> buffer, const MaterialUniforms& uniforms) {
    memcpy([buffer contents], &uniforms, sizeof(MaterialUniforms));
}

simd_float4x4 ShaderManager::CreateModelMatrix(const Transform& transform) {
    simd_float4x4 translation = ShaderMath::matrix_translation(transform.position);
    simd_float4x4 rotation = ShaderMath::matrix_rotation(transform.rotation);
    simd_float4x4 scale = ShaderMath::matrix_scale(transform.scale);
    
    return simd_mul(simd_mul(translation, rotation), scale);
}

simd_float4x4 ShaderManager::CreateViewMatrix(const Vector3& position, const Vector3& target, const Vector3& up) {
    return ShaderMath::matrix_look_at(position, target, up);
}

simd_float4x4 ShaderManager::CreateProjectionMatrix(float fov, float aspect, float nearPlane, float farPlane) {
    return ShaderMath::matrix_perspective(fov, aspect, nearPlane, farPlane);
}

// ============================================================================
// ShaderMath Implementation
// ============================================================================

namespace ShaderMath {

simd_float4x4 matrix_identity() {
    return matrix_identity_float4x4;
}

simd_float4x4 matrix_translation(const Vector3& translation) {
    simd_float4x4 matrix = matrix_identity_float4x4;
    matrix.columns[3] = simd_make_float4(translation.x, translation.y, translation.z, 1.0f);
    return matrix;
}

simd_float4x4 matrix_rotation_x(float radians) {
    float c = cosf(radians);
    float s = sinf(radians);
    
    simd_float4x4 matrix = matrix_identity_float4x4;
    matrix.columns[1] = simd_make_float4(0.0f, c, s, 0.0f);
    matrix.columns[2] = simd_make_float4(0.0f, -s, c, 0.0f);
    return matrix;
}

simd_float4x4 matrix_rotation_y(float radians) {
    float c = cosf(radians);
    float s = sinf(radians);
    
    simd_float4x4 matrix = matrix_identity_float4x4;
    matrix.columns[0] = simd_make_float4(c, 0.0f, -s, 0.0f);
    matrix.columns[2] = simd_make_float4(s, 0.0f, c, 0.0f);
    return matrix;
}

simd_float4x4 matrix_rotation_z(float radians) {
    float c = cosf(radians);
    float s = sinf(radians);
    
    simd_float4x4 matrix = matrix_identity_float4x4;
    matrix.columns[0] = simd_make_float4(c, s, 0.0f, 0.0f);
    matrix.columns[1] = simd_make_float4(-s, c, 0.0f, 0.0f);
    return matrix;
}

simd_float4x4 matrix_rotation(const Vector3& rotation) {
    simd_float4x4 rotX = matrix_rotation_x(rotation.x);
    simd_float4x4 rotY = matrix_rotation_y(rotation.y);
    simd_float4x4 rotZ = matrix_rotation_z(rotation.z);
    
    return simd_mul(simd_mul(rotZ, rotY), rotX);
}

simd_float4x4 matrix_scale(const Vector3& scale) {
    simd_float4x4 matrix = matrix_identity_float4x4;
    matrix.columns[0] = simd_make_float4(scale.x, 0.0f, 0.0f, 0.0f);
    matrix.columns[1] = simd_make_float4(0.0f, scale.y, 0.0f, 0.0f);
    matrix.columns[2] = simd_make_float4(0.0f, 0.0f, scale.z, 0.0f);
    return matrix;
}

simd_float4x4 matrix_look_at(const Vector3& eye, const Vector3& target, const Vector3& up) {
    Vector3 zAxis = eye - target;
    zAxis.Normalize();
    
    Vector3 xAxis = up.Cross(zAxis);
    xAxis.Normalize();
    
    Vector3 yAxis = zAxis.Cross(xAxis);
    
    simd_float4x4 matrix;
    matrix.columns[0] = simd_make_float4(xAxis.x, yAxis.x, zAxis.x, 0.0f);
    matrix.columns[1] = simd_make_float4(xAxis.y, yAxis.y, zAxis.y, 0.0f);
    matrix.columns[2] = simd_make_float4(xAxis.z, yAxis.z, zAxis.z, 0.0f);
    matrix.columns[3] = simd_make_float4(-xAxis.Dot(eye), -yAxis.Dot(eye), -zAxis.Dot(eye), 1.0f);
    
    return matrix;
}

simd_float4x4 matrix_perspective(float fov, float aspect, float nearPlane, float farPlane) {
    float yScale = 1.0f / tanf(fov * 0.5f);
    float xScale = yScale / aspect;
    float zRange = farPlane - nearPlane;
    float zScale = -(farPlane + nearPlane) / zRange;
    float wzScale = -2.0f * farPlane * nearPlane / zRange;
    
    simd_float4x4 matrix = {0};
    matrix.columns[0] = simd_make_float4(xScale, 0.0f, 0.0f, 0.0f);
    matrix.columns[1] = simd_make_float4(0.0f, yScale, 0.0f, 0.0f);
    matrix.columns[2] = simd_make_float4(0.0f, 0.0f, zScale, -1.0f);
    matrix.columns[3] = simd_make_float4(0.0f, 0.0f, wzScale, 0.0f);
    
    return matrix;
}

simd_float4x4 matrix_orthographic(float left, float right, float bottom, float top, float nearPlane, float farPlane) {
    float width = right - left;
    float height = top - bottom;
    float depth = farPlane - nearPlane;
    
    simd_float4x4 matrix = {0};
    matrix.columns[0] = simd_make_float4(2.0f / width, 0.0f, 0.0f, 0.0f);
    matrix.columns[1] = simd_make_float4(0.0f, 2.0f / height, 0.0f, 0.0f);
    matrix.columns[2] = simd_make_float4(0.0f, 0.0f, -2.0f / depth, 0.0f);
    matrix.columns[3] = simd_make_float4(-(right + left) / width, -(top + bottom) / height, -(farPlane + nearPlane) / depth, 1.0f);
    
    return matrix;
}

simd_float3 vector3_from_engine(const Vector3& v) {
    return simd_make_float3(v.x, v.y, v.z);
}

Vector3 vector3_to_engine(const simd_float3& v) {
    return Vector3(v.x, v.y, v.z);
}

simd_float4 vector4_from_engine(const Vector4& v) {
    return simd_make_float4(v.x, v.y, v.z, v.w);
}

Vector4 vector4_to_engine(const simd_float4& v) {
    return Vector4(v.x, v.y, v.z, v.w);
}

} // namespace ShaderMath

} // namespace Graphics
} // namespace Engine
