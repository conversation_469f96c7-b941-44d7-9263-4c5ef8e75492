//
// Renderer3D.h
// macOS 3D Looter-Shooter Game Engine
//
// 3D renderer using Metal API with proper shaders and 3D graphics
//

#pragma once

#include "engine/System.h"
#include "engine/Components.h"
#include "engine/ECS.h"
#include "MetalDevice.h"
#include "WindowManager.h"

#ifdef __OBJC__
#import <Metal/Metal.h>
#import <MetalKit/MetalKit.h>
#import <simd/simd.h>
#else
#include <Metal/Metal.h>
#include <MetalKit/MetalKit.h>
#include <simd/simd.h>
#endif

#include <memory>
#include <vector>
#include <unordered_map>

namespace Engine {
namespace Graphics {

// ============================================================================
// 3D Vertex Structure
// ============================================================================

struct Vertex3D {
    simd_float3 position;
    simd_float3 normal;
    simd_float2 texCoord;
};

// ============================================================================
// Uniform Structures (matching Metal shaders)
// ============================================================================

struct FrameUniforms {
    simd_float4x4 viewMatrix;
    simd_float4x4 projectionMatrix;
    simd_float4x4 viewProjectionMatrix;
    simd_float3 cameraPosition;
    float time;
    simd_float3 lightDirection;
    float deltaTime;
    simd_float3 lightColor;
    float ambientIntensity;
};

struct ObjectUniforms {
    simd_float4x4 modelMatrix;
    simd_float4x4 normalMatrix;
    simd_float4x4 mvpMatrix;
    simd_float4 color;
    float metallic;
    float roughness;
    float ao;
    float _padding;
};

// ============================================================================
// 3D Mesh Class
// ============================================================================

class Mesh3D {
public:
    Mesh3D();
    ~Mesh3D();
    
    bool Initialize(id<MTLDevice> device, const std::vector<Vertex3D>& vertices, const std::vector<uint16_t>& indices);
    void Shutdown();
    
    void Draw(id<MTLRenderCommandEncoder> encoder);
    
    size_t GetVertexCount() const { return m_vertexCount; }
    size_t GetIndexCount() const { return m_indexCount; }
    size_t GetTriangleCount() const { return m_indexCount / 3; }
    
private:
    id<MTLBuffer> m_vertexBuffer;
    id<MTLBuffer> m_indexBuffer;
    size_t m_vertexCount;
    size_t m_indexCount;
    bool m_initialized;
};

// ============================================================================
// 3D Renderable Component
// ============================================================================

struct Renderable3D {
    std::shared_ptr<Mesh3D> mesh;
    simd_float4 color = {1.0f, 1.0f, 1.0f, 1.0f};
    float metallic = 0.0f;
    float roughness = 0.5f;
    float ao = 1.0f;
    bool visible = true;
    
    Renderable3D() = default;
    Renderable3D(std::shared_ptr<Mesh3D> m, simd_float4 c = {1.0f, 1.0f, 1.0f, 1.0f}) 
        : mesh(m), color(c) {}
};

// ============================================================================
// 3D Camera Component
// ============================================================================

struct Camera3D {
    simd_float3 position = {0.0f, 0.0f, 5.0f};
    simd_float3 target = {0.0f, 0.0f, 0.0f};
    simd_float3 up = {0.0f, 1.0f, 0.0f};
    float fov = 60.0f; // Field of view in degrees
    float nearPlane = 0.1f;
    float farPlane = 100.0f;
    
    Camera3D() = default;
    Camera3D(simd_float3 pos, simd_float3 tgt) : position(pos), target(tgt) {}
};

// ============================================================================
// 3D Renderer System
// ============================================================================

class Renderer3D : public System {
public:
    Renderer3D();
    ~Renderer3D();
    
    // System interface
    void Initialize() override;
    void Update(DeltaTime deltaTime) override;
    void Shutdown() override;
    const char* GetName() const override { return "Renderer3D"; }
    
    // Camera management
    void SetActiveCamera(Entity camera) { m_activeCamera = camera; }
    Entity GetActiveCamera() const { return m_activeCamera; }
    
    // Mesh creation utilities
    std::shared_ptr<Mesh3D> CreateCubeMesh();
    std::shared_ptr<Mesh3D> CreateSphereMesh(int segments = 16);
    std::shared_ptr<Mesh3D> CreatePlaneMesh();
    
    // Rendering statistics
    struct RenderStats {
        uint32_t frameCount = 0;
        uint32_t drawCalls = 0;
        uint32_t triangles = 0;
        uint32_t vertices = 0;
        uint32_t entities = 0;
        float frameTime = 0.0f;

        void Reset() {
            drawCalls = 0;
            triangles = 0;
            vertices = 0;
            entities = 0;
        }
    };
    
    const RenderStats& GetStats() const { return m_stats; }
    
private:
    // Initialization
    bool InitializeGraphics();
    bool CreateShaderPipeline();
    bool CreateUniformBuffers();
    
    // Rendering
    void BeginFrame();
    void EndFrame();
    void Present();
    void RenderMeshes();
    void UpdateFrameUniforms(const Camera3D& camera, float aspectRatio);
    void UpdateObjectUniforms(const Transform& transform, const Renderable3D& renderable);
    
    // Matrix calculations
    simd_float4x4 CreateViewMatrix(simd_float3 position, simd_float3 target, simd_float3 up);
    simd_float4x4 CreateProjectionMatrix(float fov, float aspectRatio, float nearPlane, float farPlane);
    simd_float4x4 CreateModelMatrix(const Transform& transform);
    
    // Graphics resources
    MetalDevice* m_metalDevice;
    WindowManager* m_windowManager;
    id<MTLRenderPipelineState> m_pipelineState;
    id<MTLDepthStencilState> m_depthStencilState;
    id<MTLBuffer> m_frameUniformBuffer;
    id<MTLBuffer> m_objectUniformBuffer;
    id<MTLSamplerState> m_samplerState;
    
    // Rendering state
    Entity m_activeCamera;
    RenderStats m_stats;
    float m_totalTime;
    
    // Lighting
    simd_float3 m_lightDirection = {-0.5f, -1.0f, -0.5f};
    simd_float3 m_lightColor = {1.0f, 1.0f, 1.0f};
    float m_ambientIntensity = 0.3f;
    
    // Initialization state
    bool m_initialized;
};

// ============================================================================
// Utility Functions
// ============================================================================

// Register 3D graphics components with ECS
void Register3DGraphicsComponents(ECS& ecs);

// Create a 3D camera entity
Entity Create3DCameraEntity(ECS& ecs, simd_float3 position, simd_float3 target);

// Create a 3D renderable entity
Entity Create3DRenderableEntity(ECS& ecs, const Transform& transform, std::shared_ptr<Mesh3D> mesh, simd_float4 color);

} // namespace Graphics
} // namespace Engine
