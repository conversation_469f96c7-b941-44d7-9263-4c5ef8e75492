//
// SimpleShaders.mm
// macOS 3D Looter-Shooter Game Engine
//
// Simple shader system implementation
//

#include "SimpleShaders.h"
#include <iostream>

#import <Metal/Metal.h>

namespace Engine {
namespace Graphics {

id<MTLLibrary> SimpleShaders::CreateRuntimeLibrary(id<MTLDevice> device) {
    std::string shaderSource = GetShaderHeader() + GetVertexShaderSource() + GetFragmentShaderSource();
    
    NSString* sourceString = [NSString stringWithUTF8String:shaderSource.c_str()];
    
    NSError* error = nil;
    id<MTLLibrary> library = [device newLibraryWithSource:sourceString options:nil error:&error];
    
    if (error) {
        NSLog(@"❌ Shader compilation error: %@", error.localizedDescription);
        return nil;
    }
    
    if (!library) {
        std::cerr << "❌ Failed to create runtime shader library" << std::endl;
        return nil;
    }
    
    std::cout << "✅ Runtime shader library created successfully" << std::endl;
    return library;
}

std::string SimpleShaders::GetShaderHeader() {
    return R"(
#include <metal_stdlib>
using namespace metal;

struct VertexIn {
    float3 position [[attribute(0)]];
    float3 normal [[attribute(1)]];
    float2 texCoord [[attribute(2)]];
};

struct VertexOut {
    float4 position [[position]];
    float3 worldPosition;
    float3 normal;
    float2 texCoord;
    float4 color;
};

struct FrameUniforms {
    float4x4 viewMatrix;
    float4x4 projectionMatrix;
    float4x4 viewProjectionMatrix;
    float3 cameraPosition;
    float time;
    float3 lightDirection;
    float deltaTime;
    float3 lightColor;
    float ambientIntensity;
};

struct ObjectUniforms {
    float4x4 modelMatrix;
    float4x4 normalMatrix;
    float4x4 mvpMatrix;
    float4 color;
    float metallic;
    float roughness;
    float ao;
    float padding;
};

)";
}

std::string SimpleShaders::GetVertexShaderSource() {
    return R"(
vertex VertexOut basic_vertex_shader(VertexIn in [[stage_in]],
                                    constant FrameUniforms& frameUniforms [[buffer(1)]],
                                    constant ObjectUniforms& objectUniforms [[buffer(2)]]) {
    VertexOut out;
    
    // Transform position
    float4 worldPosition = objectUniforms.modelMatrix * float4(in.position, 1.0);
    out.worldPosition = worldPosition.xyz;
    out.position = frameUniforms.viewProjectionMatrix * worldPosition;
    
    // Transform normal
    out.normal = normalize((objectUniforms.normalMatrix * float4(in.normal, 0.0)).xyz);
    
    // Pass through texture coordinates
    out.texCoord = in.texCoord;
    
    // Pass through color
    out.color = objectUniforms.color;
    
    return out;
}

)";
}

std::string SimpleShaders::GetFragmentShaderSource() {
    return R"(
fragment float4 basic_fragment_shader(VertexOut in [[stage_in]],
                                     constant FrameUniforms& frameUniforms [[buffer(0)]]) {
    // Simple lighting calculation
    float3 normal = normalize(in.normal);
    float3 lightDir = normalize(-frameUniforms.lightDirection);
    
    // Diffuse lighting
    float diffuse = max(dot(normal, lightDir), 0.0);
    
    // Ambient lighting
    float ambient = frameUniforms.ambientIntensity;
    
    // Combine lighting
    float lighting = ambient + diffuse * (1.0 - ambient);
    
    // Apply lighting to color
    float3 finalColor = in.color.rgb * lighting * frameUniforms.lightColor;
    
    // Add some animation based on time
    float pulse = sin(frameUniforms.time * 2.0) * 0.1 + 0.9;
    finalColor *= pulse;
    
    return float4(finalColor, in.color.a);
}

)";
}

} // namespace Graphics
} // namespace Engine
