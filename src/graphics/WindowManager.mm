//
// WindowManager.mm
// macOS 3D Looter-Shooter Game Engine
//
// Window management system implementation for macOS
//

#include "WindowManager.h"
#include "MetalDevice.h"
#include "input/InputSystem.h"
#include "input/CocoaInputHandler.h"
#include <iostream>

#import <Cocoa/Cocoa.h>
#import <MetalKit/MetalKit.h>

// ============================================================================
// Forward declaration for input handler
// ============================================================================
namespace Engine {
namespace Input {
    class CocoaInputHandler;
}
}

// ============================================================================
// Custom Metal View for Input Handling
// ============================================================================

@interface GameMetalView : MTKView
@property (nonatomic, assign) Engine::Input::CocoaInputHandler* inputHandler;
@end

@implementation GameMetalView

- (BOOL)acceptsFirstResponder {
    return YES;
}

- (void)keyDown:(NSEvent *)event {
    if (self.inputHandler) {
        self.inputHandler->HandleKeyDown([event keyCode], [event isARepeat]);
    }
}

- (void)keyUp:(NSEvent *)event {
    if (self.inputHandler) {
        self.inputHandler->HandleKeyUp([event keyCode]);
    }
}

- (void)mouseDown:(NSEvent *)event {
    if (self.inputHandler) {
        NSPoint location = [event locationInWindow];
        self.inputHandler->HandleMouseDown(0, location.x, location.y);
    }
}

- (void)mouseUp:(NSEvent *)event {
    if (self.inputHandler) {
        NSPoint location = [event locationInWindow];
        self.inputHandler->HandleMouseUp(0, location.x, location.y);
    }
}

- (void)rightMouseDown:(NSEvent *)event {
    if (self.inputHandler) {
        NSPoint location = [event locationInWindow];
        self.inputHandler->HandleMouseDown(1, location.x, location.y);
    }
}

- (void)rightMouseUp:(NSEvent *)event {
    if (self.inputHandler) {
        NSPoint location = [event locationInWindow];
        self.inputHandler->HandleMouseUp(1, location.x, location.y);
    }
}

- (void)mouseMoved:(NSEvent *)event {
    if (self.inputHandler) {
        NSPoint location = [event locationInWindow];
        self.inputHandler->HandleMouseMoved(location.x, location.y);
    }
}

- (void)mouseDragged:(NSEvent *)event {
    [self mouseMoved:event];
}

- (void)rightMouseDragged:(NSEvent *)event {
    [self mouseMoved:event];
}

- (void)scrollWheel:(NSEvent *)event {
    if (self.inputHandler) {
        NSPoint location = [event locationInWindow];
        self.inputHandler->HandleScrollWheel([event deltaX], [event deltaY], location.x, location.y);
    }
}

@end

// ============================================================================
// Window Delegate Interface (must be outside namespace)
// ============================================================================

@interface GameWindowDelegate : NSObject <NSWindowDelegate>
@property (nonatomic, assign) Engine::Graphics::WindowManager* windowManager;
@end

@implementation GameWindowDelegate

- (void)windowWillClose:(NSNotification *)notification {
    if (self.windowManager) {
        self.windowManager->RequestClose();
    }
}

- (void)windowDidResize:(NSNotification *)notification {
    // Handle window resize if needed
}

@end

namespace Engine {
namespace Graphics {

// Static member definition
WindowManager* WindowManager::s_instance = nullptr;

// ============================================================================
// WindowManager Implementation
// ============================================================================

WindowManager::WindowManager()
    : m_width(1920)
    , m_height(1080)
    , m_fullscreen(false)
    , m_resizable(true)
    , m_vsync(true)
    , m_visible(false)
    , m_shouldClose(false)
    , m_title("macOS 3D Looter-Shooter")
    , m_window(nullptr)
    , m_metalView(nullptr)
    , m_delegate(nullptr)
    , m_metalDevice(nullptr)
    , m_inputSystem(nullptr)
    , m_inputHandler(nullptr)
    , m_initialized(false) {
    
    if (s_instance == nullptr) {
        s_instance = this;
    }
}

WindowManager::~WindowManager() {
    if (m_initialized) {
        Shutdown();
    }
    
    if (s_instance == this) {
        s_instance = nullptr;
    }
}

bool WindowManager::Initialize(const WindowConfig& config) {
    if (m_initialized) {
        std::cout << "⚠️  WindowManager already initialized" << std::endl;
        return true;
    }
    
    std::cout << "🪟 Initializing WindowManager..." << std::endl;
    
    // Store configuration
    m_width = config.width;
    m_height = config.height;
    m_fullscreen = config.fullscreen;
    m_resizable = config.resizable;
    m_vsync = config.vsync;
    m_title = config.title;
    
    // Get Metal device
    m_metalDevice = MetalDevice::GetInstance();
    if (!m_metalDevice || !m_metalDevice->GetDevice()) {
        std::cerr << "❌ MetalDevice must be initialized before WindowManager" << std::endl;
        return false;
    }
    
    // Create window
    if (!CreateWindow()) {
        std::cerr << "❌ Failed to create window" << std::endl;
        return false;
    }
    
    // Create Metal view
    if (!CreateMetalView()) {
        std::cerr << "❌ Failed to create Metal view" << std::endl;
        return false;
    }
    
    // Set up window delegate
    SetupWindowDelegate();
    
    m_initialized = true;
    std::cout << "✅ WindowManager initialized successfully" << std::endl;
    
    return true;
}

void WindowManager::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "🪟 Shutting down WindowManager..." << std::endl;
    
    // Hide window
    Hide();

    // Shutdown input handler
    if (m_inputHandler) {
        m_inputHandler->Shutdown();
        delete m_inputHandler;
        m_inputHandler = nullptr;
    }
    m_inputSystem = nullptr;

    // Release Objective-C objects
    if (m_delegate) {
        GameWindowDelegate* delegate = (__bridge_transfer GameWindowDelegate*)m_delegate;
        delegate = nil;
        m_delegate = nullptr;
    }
    
    if (m_metalView) {
        MTKView* metalView = (__bridge_transfer MTKView*)m_metalView;
        metalView = nil;
        m_metalView = nullptr;
    }
    
    if (m_window) {
        NSWindow* window = (__bridge_transfer NSWindow*)m_window;
        [window close];
        window = nil;
        m_window = nullptr;
    }
    
    m_initialized = false;
    std::cout << "✅ WindowManager shutdown complete" << std::endl;
}

void WindowManager::Show() {
    if (!m_initialized || !m_window) {
        return;
    }

    NSWindow* window = (__bridge NSWindow*)m_window;
    [window center];
    [window makeKeyAndOrderFront:nil];
    [window orderFrontRegardless];
    [NSApp activateIgnoringOtherApps:YES];

    m_visible = true;
    std::cout << "🪟 Window shown and activated" << std::endl;
}

void WindowManager::Hide() {
    if (!m_initialized || !m_window) {
        return;
    }
    
    NSWindow* window = (__bridge NSWindow*)m_window;
    [window orderOut:nil];
    
    m_visible = false;
}

void WindowManager::SetTitle(const std::string& title) {
    m_title = title;
    
    if (m_initialized && m_window) {
        NSWindow* window = (__bridge NSWindow*)m_window;
        [window setTitle:[NSString stringWithUTF8String:title.c_str()]];
    }
}

void WindowManager::PollEvents() {
    if (!m_initialized) {
        return;
    }

    // Process Cocoa events
    NSEvent* event;
    while ((event = [NSApp nextEventMatchingMask:NSEventMaskAny
                                       untilDate:[NSDate distantPast]
                                          inMode:NSDefaultRunLoopMode
                                         dequeue:YES])) {
        [NSApp sendEvent:event];
        [NSApp updateWindows];
    }
}

bool WindowManager::ShouldClose() const {
    return m_shouldClose;
}

void WindowManager::RequestClose() {
    m_shouldClose = true;
}

void* WindowManager::GetMetalView() {
    return m_metalView;
}

void* WindowManager::GetWindow() {
    return m_window;
}

void WindowManager::SetInputSystem(Input::InputSystem* inputSystem) {
    m_inputSystem = inputSystem;

    if (m_inputSystem && !m_inputHandler) {
        // Create input handler
        m_inputHandler = new Input::CocoaInputHandler();
        if (m_inputHandler->Initialize(m_inputSystem)) {
            // Set window and view for input handler
            if (m_window) {
                m_inputHandler->SetWindow((__bridge id)m_window);
            }
            if (m_metalView) {
                m_inputHandler->SetView((__bridge id)m_metalView);

                // Connect input handler to the custom metal view
                GameMetalView* metalView = (__bridge GameMetalView*)m_metalView;
                metalView.inputHandler = m_inputHandler;
            }
            std::cout << "🎮 Input system connected to WindowManager" << std::endl;
        } else {
            delete m_inputHandler;
            m_inputHandler = nullptr;
            std::cerr << "❌ Failed to initialize CocoaInputHandler" << std::endl;
        }
    }
}

// ============================================================================
// Private Methods
// ============================================================================

bool WindowManager::CreateWindow() {
    // Create window rect
    NSRect windowRect = NSMakeRect(0, 0, m_width, m_height);

    // Window style
    NSWindowStyleMask styleMask = NSWindowStyleMaskTitled | NSWindowStyleMaskClosable | NSWindowStyleMaskMiniaturizable;
    if (m_resizable) {
        styleMask |= NSWindowStyleMaskResizable;
    }

    // Create window
    NSWindow* window = [[NSWindow alloc] initWithContentRect:windowRect
                                                   styleMask:styleMask
                                                     backing:NSBackingStoreBuffered
                                                       defer:NO];

    if (!window) {
        std::cerr << "❌ Failed to create NSWindow" << std::endl;
        return false;
    }

    // Configure window
    [window setTitle:[NSString stringWithUTF8String:m_title.c_str()]];
    [window setAcceptsMouseMovedEvents:YES];
    [window setLevel:NSNormalWindowLevel];
    [window setReleasedWhenClosed:NO]; // Important: prevent auto-release

    // Store window reference
    m_window = (__bridge_retained void*)window;

    return true;
}

bool WindowManager::CreateMetalView() {
    if (!m_window || !m_metalDevice) {
        return false;
    }

    NSWindow* window = (__bridge NSWindow*)m_window;

    // Create custom Metal view with input handling
    GameMetalView* metalView = [[GameMetalView alloc] initWithFrame:[window.contentView bounds]
                                                             device:m_metalDevice->GetDevice()];

    if (!metalView) {
        std::cerr << "❌ Failed to create GameMetalView" << std::endl;
        return false;
    }

    // Configure Metal view
    metalView.colorPixelFormat = MTLPixelFormatBGRA8Unorm;
    metalView.depthStencilPixelFormat = MTLPixelFormatDepth32Float;
    metalView.clearColor = MTLClearColorMake(0.2, 0.3, 0.4, 1.0); // Dark blue background
    metalView.enableSetNeedsDisplay = YES; // Enable manual drawing
    metalView.paused = YES; // We'll control drawing manually
    metalView.preferredFramesPerSecond = m_vsync ? 60 : 0;

    // Set as window content view
    [window setContentView:metalView];

    // Make the view the first responder to receive input events
    [window makeFirstResponder:metalView];

    // Store Metal view reference
    m_metalView = (__bridge_retained void*)metalView;

    return true;
}

void WindowManager::SetupWindowDelegate() {
    if (!m_window) {
        return;
    }
    
    NSWindow* window = (__bridge NSWindow*)m_window;
    
    // Create delegate
    GameWindowDelegate* delegate = [[GameWindowDelegate alloc] init];
    delegate.windowManager = this;
    
    // Set delegate
    [window setDelegate:delegate];
    
    // Store delegate reference
    m_delegate = (__bridge_retained void*)delegate;
}

} // namespace Graphics
} // namespace Engine
