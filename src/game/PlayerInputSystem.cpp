//
// PlayerInputSystem.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Implementation of player input system
//

#include "PlayerInputSystem.h"
#include "engine/Engine.h"
#include <iostream>
#include <cmath>

namespace Engine {
namespace Game {

// ============================================================================
// PlayerInputSystem Implementation
// ============================================================================

PlayerInputSystem::PlayerInputSystem()
    : m_inputSystem(nullptr)
    , m_initialized(false)
    , m_defaultMoveSpeed(5.0f)
    , m_defaultSprintMultiplier(2.0f)
    , m_defaultMouseSensitivity(0.002f) {
}

PlayerInputSystem::~PlayerInputSystem() {
    if (m_initialized) {
        Shutdown();
    }
}

void PlayerInputSystem::Initialize() {
    if (m_initialized) {
        return;
    }
    
    std::cout << "🎮 Initializing PlayerInputSystem..." << std::endl;
    
    m_initialized = true;
    std::cout << "✅ PlayerInputSystem initialized" << std::endl;
}

void PlayerInputSystem::Update(DeltaTime deltaTime) {
    if (!m_initialized || !m_inputSystem) {
        return;
    }
    
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    
    // Process all entities with PlayerInput, Transform, and Velocity components
    for (auto& entity : m_entities) {
        if (!ecs.HasComponent<PlayerInput>(entity) || 
            !ecs.HasComponent<Transform>(entity) || 
            !ecs.HasComponent<Velocity>(entity)) {
            continue;
        }
        
        auto& playerInput = ecs.GetComponent<PlayerInput>(entity);
        auto& transform = ecs.GetComponent<Transform>(entity);
        auto& velocity = ecs.GetComponent<Velocity>(entity);
        
        // Process movement input
        ProcessMovementInput(entity, playerInput, transform, velocity, deltaTime);
        
        // Process mouse input for rotation
        ProcessMouseInput(entity, playerInput, transform, deltaTime);
    }
}

void PlayerInputSystem::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "🎮 Shutting down PlayerInputSystem..." << std::endl;
    
    m_inputSystem = nullptr;
    m_initialized = false;
    
    std::cout << "✅ PlayerInputSystem shutdown complete" << std::endl;
}

void PlayerInputSystem::SetInputSystem(Input::InputSystem* inputSystem) {
    m_inputSystem = inputSystem;
    if (m_inputSystem) {
        std::cout << "🎮 Input system connected to PlayerInputSystem" << std::endl;
    }
}

// ============================================================================
// Input Processing Methods
// ============================================================================

void PlayerInputSystem::ProcessMovementInput(Entity entity, PlayerInput& playerInput, Transform& transform, Velocity& velocity, DeltaTime deltaTime) {
    if (!playerInput.enableMovement) {
        return;
    }
    
    // Calculate movement vector based on input
    Vector3 movement = CalculateMovementVector(playerInput, deltaTime);
    
    // Apply movement to velocity
    velocity.linear = movement;
}

void PlayerInputSystem::ProcessMouseInput(Entity entity, PlayerInput& playerInput, Transform& transform, DeltaTime deltaTime) {
    // Get mouse delta from input system
    auto mouseDelta = m_inputSystem->GetMouseDelta();

    // Apply mouse movement to rotation (simple Y-axis rotation for now)
    if (mouseDelta.deltaX != 0.0f || mouseDelta.deltaY != 0.0f) {
        transform.rotation.y += mouseDelta.deltaX * playerInput.mouseSensitivity;

        // Keep rotation in valid range
        while (transform.rotation.y > 2.0f * M_PI) {
            transform.rotation.y -= 2.0f * M_PI;
        }
        while (transform.rotation.y < 0.0f) {
            transform.rotation.y += 2.0f * M_PI;
        }
    }
}

Vector3 PlayerInputSystem::CalculateMovementVector(const PlayerInput& playerInput, DeltaTime deltaTime) {
    Vector3 movement = {0.0f, 0.0f, 0.0f};
    
    // Check movement keys
    bool moveForward = m_inputSystem->IsKeyHeld(Input::KeyCode::W);
    bool moveBackward = m_inputSystem->IsKeyHeld(Input::KeyCode::S);
    bool moveLeft = m_inputSystem->IsKeyHeld(Input::KeyCode::A);
    bool moveRight = m_inputSystem->IsKeyHeld(Input::KeyCode::D);
    bool moveUp = m_inputSystem->IsKeyHeld(Input::KeyCode::Space);
    bool moveDown = m_inputSystem->IsKeyHeld(Input::KeyCode::Shift);
    
    // Calculate movement direction
    if (moveForward) movement.z -= 1.0f;
    if (moveBackward) movement.z += 1.0f;
    if (moveLeft) movement.x -= 1.0f;
    if (moveRight) movement.x += 1.0f;
    if (moveUp) movement.y += 1.0f;
    if (moveDown) movement.y -= 1.0f;
    
    // Normalize movement vector to prevent faster diagonal movement
    float length = movement.Length();
    if (length > 0.0f) {
        movement.x /= length;
        movement.y /= length;
        movement.z /= length;
        
        // Apply speed
        float speed = playerInput.moveSpeed;
        
        // Check for sprint
        if (playerInput.enableSprint && IsSprintKeyPressed()) {
            speed *= playerInput.sprintMultiplier;
        }
        
        movement.x *= speed;
        movement.y *= speed;
        movement.z *= speed;
    }
    
    return movement;
}

bool PlayerInputSystem::IsMovementKeyPressed() const {
    if (!m_inputSystem) return false;
    
    return m_inputSystem->IsKeyHeld(Input::KeyCode::W) ||
           m_inputSystem->IsKeyHeld(Input::KeyCode::S) ||
           m_inputSystem->IsKeyHeld(Input::KeyCode::A) ||
           m_inputSystem->IsKeyHeld(Input::KeyCode::D) ||
           m_inputSystem->IsKeyHeld(Input::KeyCode::Space) ||
           m_inputSystem->IsKeyHeld(Input::KeyCode::Shift);
}

bool PlayerInputSystem::IsSprintKeyPressed() const {
    if (!m_inputSystem) return false;
    
    return m_inputSystem->IsKeyHeld(Input::KeyCode::Shift);
}

} // namespace Game
} // namespace Engine
