//
// PlayerInputSystem.h
// macOS 3D Looter-Shooter Game Engine
//
// System for handling player input and converting it to entity movement
//

#pragma once

#include "engine/System.h"
#include "engine/Components.h"
#include "input/InputSystem.h"

namespace Engine {
namespace Game {

// ============================================================================
// Player Input Component
// ============================================================================

struct PlayerInput {
    float moveSpeed = 5.0f;
    float sprintMultiplier = 2.0f;
    float mouseSensitivity = 0.002f;
    bool enableMovement = true;
    bool enableSprint = true;
    
    PlayerInput() = default;
    PlayerInput(float speed) : moveSpeed(speed) {}
    PlayerInput(float speed, float sprintMult) : moveSpeed(speed), sprintMultiplier(sprintMult) {}
};

// ============================================================================
// Player Input System
// ============================================================================

class PlayerInputSystem : public System {
public:
    PlayerInputSystem();
    ~PlayerInputSystem();
    
    // System interface
    void Initialize() override;
    void Update(DeltaTime deltaTime) override;
    void Shutdown() override;
    const char* GetName() const override { return "PlayerInputSystem"; }
    
    // Input system connection
    void SetInputSystem(Input::InputSystem* inputSystem);
    Input::InputSystem* GetInputSystem() const { return m_inputSystem; }
    
    // Configuration
    void SetDefaultMoveSpeed(float speed) { m_defaultMoveSpeed = speed; }
    void SetDefaultSprintMultiplier(float multiplier) { m_defaultSprintMultiplier = multiplier; }
    void SetDefaultMouseSensitivity(float sensitivity) { m_defaultMouseSensitivity = sensitivity; }
    
private:
    Input::InputSystem* m_inputSystem;
    bool m_initialized;
    
    // Default configuration
    float m_defaultMoveSpeed;
    float m_defaultSprintMultiplier;
    float m_defaultMouseSensitivity;
    
    // Input processing methods
    void ProcessMovementInput(Entity entity, PlayerInput& playerInput, Transform& transform, Velocity& velocity, DeltaTime deltaTime);
    void ProcessMouseInput(Entity entity, PlayerInput& playerInput, Transform& transform, DeltaTime deltaTime);
    
    // Helper methods
    Vector3 CalculateMovementVector(const PlayerInput& playerInput, DeltaTime deltaTime);
    bool IsMovementKeyPressed() const;
    bool IsSprintKeyPressed() const;
};

} // namespace Game
} // namespace Engine
