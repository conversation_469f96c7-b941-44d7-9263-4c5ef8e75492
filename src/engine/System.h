//
// System.h
// macOS 3D Looter-Shooter Game Engine
//
// System base class and manager for the Entity-Component System
//

#pragma once

#include "Types.h"
#include "Entity.h"
#include <set>
#include <unordered_map>
#include <memory>
#include <typeinfo>

namespace Engine {

// ============================================================================
// System Base Class
// ============================================================================

class System {
public:
    virtual ~System() = default;
    
    // Core system lifecycle
    virtual void Initialize() {}
    virtual void Update(DeltaTime deltaTime) = 0;
    virtual void Shutdown() {}
    
    // Entity management
    std::set<Entity> m_entities;
    
    // System properties
    virtual const char* GetName() const = 0;
    virtual bool IsEnabled() const { return m_enabled; }
    virtual void SetEnabled(bool enabled) { m_enabled = enabled; }
    
protected:
    bool m_enabled = true;
};

// ============================================================================
// System Manager
// ============================================================================

class SystemManager {
public:
    template<typename T>
    std::shared_ptr<T> RegisterSystem();
    
    template<typename T>
    void SetSignature(ComponentSignature signature);
    
    void EntityDestroyed(Entity entity);
    void EntitySignatureChanged(Entity entity, ComponentSignature entitySignature);
    
    // System lifecycle
    void InitializeAllSystems();
    void UpdateAllSystems(DeltaTime deltaTime);
    void ShutdownAllSystems();
    
    // System management
    template<typename T>
    std::shared_ptr<T> GetSystem();
    
    template<typename T>
    void EnableSystem(bool enabled = true);
    
    // Debug information
    void PrintDebugInfo() const;
    size_t GetSystemCount() const { return m_systems.size(); }
    
private:
    // Map from system type name to system signature
    std::unordered_map<const char*, ComponentSignature> m_signatures;
    
    // Map from system type name to system pointer
    std::unordered_map<const char*, std::shared_ptr<System>> m_systems;
    
    // Helper to get system type name
    template<typename T>
    const char* GetTypeName() const;
};

// ============================================================================
// System Manager Implementation
// ============================================================================

template<typename T>
std::shared_ptr<T> SystemManager::RegisterSystem() {
    const char* typeName = GetTypeName<T>();
    
    ENGINE_ASSERT(m_systems.find(typeName) == m_systems.end(),
                  "Registering system more than once");
    
    auto system = std::make_shared<T>();
    m_systems.insert({typeName, system});
    return system;
}

template<typename T>
void SystemManager::SetSignature(ComponentSignature signature) {
    const char* typeName = GetTypeName<T>();
    
    ENGINE_ASSERT(m_systems.find(typeName) != m_systems.end(),
                  "System used before registered");
    
    m_signatures.insert({typeName, signature});
}

template<typename T>
std::shared_ptr<T> SystemManager::GetSystem() {
    const char* typeName = GetTypeName<T>();
    
    ENGINE_ASSERT(m_systems.find(typeName) != m_systems.end(),
                  "System not registered");
    
    return std::static_pointer_cast<T>(m_systems[typeName]);
}

template<typename T>
void SystemManager::EnableSystem(bool enabled) {
    auto system = GetSystem<T>();
    system->SetEnabled(enabled);
}

template<typename T>
const char* SystemManager::GetTypeName() const {
    return typeid(T).name();
}

inline void SystemManager::EntityDestroyed(Entity entity) {
    // Erase the destroyed entity from all system lists
    for (auto const& pair : m_systems) {
        auto const& system = pair.second;
        system->m_entities.erase(entity);
    }
}

inline void SystemManager::EntitySignatureChanged(Entity entity, ComponentSignature entitySignature) {
    // Notify each system that an entity's signature changed
    for (auto const& pair : m_systems) {
        auto const& type = pair.first;
        auto const& system = pair.second;
        auto const& systemSignature = m_signatures[type];
        
        // Entity signature matches system signature - insert into set
        if ((entitySignature & systemSignature) == systemSignature) {
            system->m_entities.insert(entity);
        }
        // Entity signature does not match system signature - erase from set
        else {
            system->m_entities.erase(entity);
        }
    }
}

inline void SystemManager::InitializeAllSystems() {
    for (auto const& pair : m_systems) {
        auto const& system = pair.second;
        if (system->IsEnabled()) {
            system->Initialize();
        }
    }
}

inline void SystemManager::UpdateAllSystems(DeltaTime deltaTime) {
    for (auto const& pair : m_systems) {
        auto const& system = pair.second;
        if (system->IsEnabled()) {
            system->Update(deltaTime);
        }
    }
}

inline void SystemManager::ShutdownAllSystems() {
    for (auto const& pair : m_systems) {
        auto const& system = pair.second;
        system->Shutdown();
    }
}

inline void SystemManager::PrintDebugInfo() const {
    std::cout << "SystemManager Debug Info:" << std::endl;
    std::cout << "  Registered systems: " << m_systems.size() << std::endl;
    
    for (auto const& pair : m_systems) {
        auto const& system = pair.second;
        std::cout << "    " << system->GetName() 
                  << " (entities: " << system->m_entities.size() 
                  << ", enabled: " << (system->IsEnabled() ? "yes" : "no") << ")" << std::endl;
    }
}

} // namespace Engine
