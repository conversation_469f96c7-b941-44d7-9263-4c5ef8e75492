//
// Entity.h
// macOS 3D Looter-Shooter Game Engine
//
// Entity management for the Entity-Component System
//

#pragma once

#include "Types.h"
#include <vector>
#include <queue>
#include <array>
#include <iostream>

namespace Engine {

// ============================================================================
// Entity Class
// ============================================================================

class Entity {
public:
    Entity() : m_id(INVALID_ENTITY) {}
    explicit Entity(EntityID id) : m_id(id) {}
    
    EntityID GetID() const { return m_id; }
    bool IsValid() const { return m_id != INVALID_ENTITY; }
    
    // Comparison operators
    bool operator==(const Entity& other) const { return m_id == other.m_id; }
    bool operator!=(const Entity& other) const { return m_id != other.m_id; }
    bool operator<(const Entity& other) const { return m_id < other.m_id; }

    // Comparison with EntityID
    bool operator==(EntityID id) const { return m_id == id; }
    bool operator!=(EntityID id) const { return m_id != id; }
    
private:
    EntityID m_id;
};

// ============================================================================
// Entity Manager
// ============================================================================

class EntityManager {
public:
    EntityManager();
    ~EntityManager();
    
    // Entity lifecycle
    Entity CreateEntity();
    void DestroyEntity(Entity entity);
    bool IsEntityValid(Entity entity) const;
    
    // Component signature management
    void SetSignature(Entity entity, ComponentSignature signature);
    ComponentSignature GetSignature(Entity entity) const;
    
    // Statistics
    size_t GetEntityCount() const { return m_livingEntityCount; }
    size_t GetMaxEntities() const { return MAX_ENTITIES; }
    
    // Debug information
    void PrintDebugInfo() const;
    
private:
    // Entity storage
    std::queue<EntityID> m_availableEntities;
    std::array<ComponentSignature, MAX_ENTITIES> m_signatures;
    size_t m_livingEntityCount;
    EntityID m_nextEntityID;
    
    // Helper methods
    void InitializeAvailableEntities();
};

// ============================================================================
// Entity Manager Implementation
// ============================================================================

inline EntityManager::EntityManager() 
    : m_livingEntityCount(0)
    , m_nextEntityID(0) {
    InitializeAvailableEntities();
}

inline EntityManager::~EntityManager() {
    // Cleanup is automatic with RAII
}

inline Entity EntityManager::CreateEntity() {
    ENGINE_ASSERT(m_livingEntityCount < MAX_ENTITIES, 
                  "Too many entities in existence. Maximum is " + std::to_string(MAX_ENTITIES));
    
    EntityID id = m_availableEntities.front();
    m_availableEntities.pop();
    ++m_livingEntityCount;
    
    return Entity(id);
}

inline void EntityManager::DestroyEntity(Entity entity) {
    ENGINE_ASSERT(entity.GetID() < MAX_ENTITIES, "Entity out of range");
    
    // Reset the entity's signature
    m_signatures[entity.GetID()].reset();
    
    // Put the entity ID back at the end of the queue
    m_availableEntities.push(entity.GetID());
    --m_livingEntityCount;
}

inline bool EntityManager::IsEntityValid(Entity entity) const {
    if (entity.GetID() >= MAX_ENTITIES) {
        return false;
    }
    
    // An entity is valid if it has any components
    return m_signatures[entity.GetID()].any();
}

inline void EntityManager::SetSignature(Entity entity, ComponentSignature signature) {
    ENGINE_ASSERT(entity.GetID() < MAX_ENTITIES, "Entity out of range");
    m_signatures[entity.GetID()] = signature;
}

inline ComponentSignature EntityManager::GetSignature(Entity entity) const {
    ENGINE_ASSERT(entity.GetID() < MAX_ENTITIES, "Entity out of range");
    return m_signatures[entity.GetID()];
}

inline void EntityManager::PrintDebugInfo() const {
    std::cout << "EntityManager Debug Info:" << std::endl;
    std::cout << "  Living entities: " << m_livingEntityCount << "/" << MAX_ENTITIES << std::endl;
    std::cout << "  Available entities: " << m_availableEntities.size() << std::endl;
}

inline void EntityManager::InitializeAvailableEntities() {
    for (EntityID entity = 0; entity < MAX_ENTITIES; ++entity) {
        m_availableEntities.push(entity);
    }
}

} // namespace Engine
