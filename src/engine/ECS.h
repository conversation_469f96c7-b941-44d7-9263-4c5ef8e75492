//
// ECS.h
// macOS 3D Looter-Shooter Game Engine
//
// Entity-Component-System coordinator that manages all ECS operations
//

#pragma once

#include "Types.h"
#include "Entity.h"
#include "Component.h"
#include "System.h"
#include <memory>

namespace Engine {

// ============================================================================
// ECS Coordinator
// ============================================================================

class ECS {
public:
    ECS();
    ~ECS();
    
    // Initialization and cleanup
    void Initialize();
    void Shutdown();
    void Update(DeltaTime deltaTime);
    
    // Entity management
    Entity CreateEntity();
    void DestroyEntity(Entity entity);
    bool IsEntityValid(Entity entity) const;
    
    // Component management
    template<typename T>
    void RegisterComponent();

    template<typename T>
    ComponentTypeID GetComponentType();

    template<typename T>
    void AddComponent(Entity entity, T component);

    template<typename T>
    void RemoveComponent(Entity entity);

    template<typename T>
    T& GetComponent(Entity entity);

    template<typename T>
    const T& GetComponent(Entity entity) const;

    template<typename T>
    bool HasComponent(Entity entity) const;
    
    // System management
    template<typename T>
    std::shared_ptr<T> RegisterSystem();
    
    template<typename T>
    void SetSystemSignature(ComponentSignature signature);
    
    template<typename T>
    std::shared_ptr<T> GetSystem();
    
    template<typename T>
    void EnableSystem(bool enabled = true);
    
    // Debug and statistics
    void PrintDebugInfo() const;
    size_t GetEntityCount() const;
    size_t GetSystemCount() const;
    
private:
    std::unique_ptr<EntityManager> m_entityManager;
    std::unique_ptr<ComponentManager> m_componentManager;
    std::unique_ptr<SystemManager> m_systemManager;
    
    bool m_initialized = false;
};

// ============================================================================
// ECS Implementation
// ============================================================================

inline ECS::ECS() {
    m_entityManager = std::make_unique<EntityManager>();
    m_componentManager = std::make_unique<ComponentManager>();
    m_systemManager = std::make_unique<SystemManager>();
}

inline ECS::~ECS() {
    if (m_initialized) {
        Shutdown();
    }
}

inline void ECS::Initialize() {
    ENGINE_ASSERT(!m_initialized, "ECS already initialized");
    
    m_systemManager->InitializeAllSystems();
    m_initialized = true;
    
    std::cout << "🔧 ECS initialized successfully" << std::endl;
}

inline void ECS::Shutdown() {
    ENGINE_ASSERT(m_initialized, "ECS not initialized");
    
    m_systemManager->ShutdownAllSystems();
    m_initialized = false;
    
    std::cout << "🔧 ECS shutdown complete" << std::endl;
}

inline void ECS::Update(DeltaTime deltaTime) {
    ENGINE_ASSERT(m_initialized, "ECS not initialized");
    m_systemManager->UpdateAllSystems(deltaTime);
}

inline Entity ECS::CreateEntity() {
    return m_entityManager->CreateEntity();
}

inline void ECS::DestroyEntity(Entity entity) {
    m_entityManager->DestroyEntity(entity);
    m_componentManager->EntityDestroyed(entity);
    m_systemManager->EntityDestroyed(entity);
}

inline bool ECS::IsEntityValid(Entity entity) const {
    return m_entityManager->IsEntityValid(entity);
}

template<typename T>
void ECS::RegisterComponent() {
    m_componentManager->RegisterComponent<T>();
}

template<typename T>
ComponentTypeID ECS::GetComponentType() {
    return m_componentManager->GetComponentType<T>();
}

template<typename T>
void ECS::AddComponent(Entity entity, T component) {
    m_componentManager->AddComponent<T>(entity, component);
    
    auto signature = m_entityManager->GetSignature(entity);
    signature.set(m_componentManager->GetComponentType<T>(), true);
    m_entityManager->SetSignature(entity, signature);
    
    m_systemManager->EntitySignatureChanged(entity, signature);
}

template<typename T>
void ECS::RemoveComponent(Entity entity) {
    m_componentManager->RemoveComponent<T>(entity);
    
    auto signature = m_entityManager->GetSignature(entity);
    signature.set(m_componentManager->GetComponentType<T>(), false);
    m_entityManager->SetSignature(entity, signature);
    
    m_systemManager->EntitySignatureChanged(entity, signature);
}

template<typename T>
T& ECS::GetComponent(Entity entity) {
    return m_componentManager->GetComponent<T>(entity);
}

template<typename T>
const T& ECS::GetComponent(Entity entity) const {
    return m_componentManager->GetComponent<T>(entity);
}

template<typename T>
bool ECS::HasComponent(Entity entity) const {
    return m_componentManager->HasComponent<T>(entity);
}

template<typename T>
std::shared_ptr<T> ECS::RegisterSystem() {
    return m_systemManager->RegisterSystem<T>();
}

template<typename T>
void ECS::SetSystemSignature(ComponentSignature signature) {
    m_systemManager->SetSignature<T>(signature);
}

template<typename T>
std::shared_ptr<T> ECS::GetSystem() {
    return m_systemManager->GetSystem<T>();
}

template<typename T>
void ECS::EnableSystem(bool enabled) {
    m_systemManager->EnableSystem<T>(enabled);
}

inline void ECS::PrintDebugInfo() const {
    std::cout << "=== ECS Debug Information ===" << std::endl;
    m_entityManager->PrintDebugInfo();
    m_componentManager->PrintDebugInfo();
    m_systemManager->PrintDebugInfo();
    std::cout << "=============================" << std::endl;
}

inline size_t ECS::GetEntityCount() const {
    return m_entityManager->GetEntityCount();
}

inline size_t ECS::GetSystemCount() const {
    return m_systemManager->GetSystemCount();
}

} // namespace Engine
