//
// Component.h
// macOS 3D Looter-Shooter Game Engine
//
// Component system for the Entity-Component System
//

#pragma once

#include "Types.h"
#include "Entity.h"
#include <array>
#include <unordered_map>
#include <memory>
#include <typeinfo>
#include <iostream>

namespace Engine {

// ============================================================================
// Component Array Interface
// ============================================================================

class IComponentArray {
public:
    virtual ~IComponentArray() = default;
    virtual void EntityDestroyed(Entity entity) = 0;
    virtual size_t Size() const = 0;
};

// ============================================================================
// Component Array Template
// ============================================================================

template<typename T>
class ComponentArray : public IComponentArray {
public:
    void InsertData(Entity entity, T component);
    void RemoveData(Entity entity);
    T& GetData(Entity entity);
    const T& GetData(Entity entity) const;
    bool HasData(Entity entity) const;
    
    void EntityDestroyed(Entity entity) override;
    size_t Size() const override { return m_size; }
    
    // Iterator support for systems
    typename std::array<T, MAX_ENTITIES>::iterator begin() { return m_componentArray.begin(); }
    typename std::array<T, MAX_ENTITIES>::iterator end() { return m_componentArray.begin() + m_size; }
    typename std::array<T, MAX_ENTITIES>::const_iterator begin() const { return m_componentArray.begin(); }
    typename std::array<T, MAX_ENTITIES>::const_iterator end() const { return m_componentArray.begin() + m_size; }
    
private:
    // Packed array of components (only valid components, no gaps)
    std::array<T, MAX_ENTITIES> m_componentArray;
    
    // Map from entity ID to array index
    std::unordered_map<EntityID, size_t> m_entityToIndexMap;
    
    // Map from array index to entity ID
    std::unordered_map<size_t, EntityID> m_indexToEntityMap;
    
    // Total size of valid entries in the array
    size_t m_size = 0;
};

// ============================================================================
// Component Manager
// ============================================================================

class ComponentManager {
public:
    template<typename T>
    void RegisterComponent();
    
    template<typename T>
    ComponentTypeID GetComponentType();
    
    template<typename T>
    void AddComponent(Entity entity, T component);
    
    template<typename T>
    void RemoveComponent(Entity entity);
    
    template<typename T>
    T& GetComponent(Entity entity);
    
    template<typename T>
    const T& GetComponent(Entity entity) const;
    
    template<typename T>
    bool HasComponent(Entity entity) const;
    
    void EntityDestroyed(Entity entity);
    
    // Debug information
    void PrintDebugInfo() const;
    
private:
    // Map from type name to component type ID
    std::unordered_map<const char*, ComponentTypeID> m_componentTypes;
    
    // Map from type name to component array
    std::unordered_map<const char*, std::shared_ptr<IComponentArray>> m_componentArrays;
    
    // Next component type ID to assign
    ComponentTypeID m_nextComponentType = 0;
    
    // Helper to get component type name
    template<typename T>
    const char* GetTypeName() const;

    // Helper to get component array
    template<typename T>
    std::shared_ptr<ComponentArray<T>> GetComponentArray();

    template<typename T>
    std::shared_ptr<ComponentArray<T>> GetComponentArray() const;
};

// ============================================================================
// Component Array Implementation
// ============================================================================

template<typename T>
void ComponentArray<T>::InsertData(Entity entity, T component) {
    ENGINE_ASSERT(m_entityToIndexMap.find(entity.GetID()) == m_entityToIndexMap.end(),
                  "Component added to same entity more than once");
    
    // Put new entry at end and update the maps
    size_t newIndex = m_size;
    m_entityToIndexMap[entity.GetID()] = newIndex;
    m_indexToEntityMap[newIndex] = entity.GetID();
    m_componentArray[newIndex] = component;
    ++m_size;
}

template<typename T>
void ComponentArray<T>::RemoveData(Entity entity) {
    ENGINE_ASSERT(m_entityToIndexMap.find(entity.GetID()) != m_entityToIndexMap.end(),
                  "Removing non-existent component");
    
    // Copy element at end into deleted element's place to maintain density
    size_t indexOfRemovedEntity = m_entityToIndexMap[entity.GetID()];
    size_t indexOfLastElement = m_size - 1;
    m_componentArray[indexOfRemovedEntity] = m_componentArray[indexOfLastElement];
    
    // Update map to point to moved spot
    EntityID entityOfLastElement = m_indexToEntityMap[indexOfLastElement];
    m_entityToIndexMap[entityOfLastElement] = indexOfRemovedEntity;
    m_indexToEntityMap[indexOfRemovedEntity] = entityOfLastElement;
    
    m_entityToIndexMap.erase(entity.GetID());
    m_indexToEntityMap.erase(indexOfLastElement);
    
    --m_size;
}

template<typename T>
T& ComponentArray<T>::GetData(Entity entity) {
    ENGINE_ASSERT(m_entityToIndexMap.find(entity.GetID()) != m_entityToIndexMap.end(),
                  "Retrieving non-existent component");
    
    return m_componentArray[m_entityToIndexMap[entity.GetID()]];
}

template<typename T>
const T& ComponentArray<T>::GetData(Entity entity) const {
    ENGINE_ASSERT(m_entityToIndexMap.find(entity.GetID()) != m_entityToIndexMap.end(),
                  "Retrieving non-existent component");
    
    return m_componentArray[m_entityToIndexMap.at(entity.GetID())];
}

template<typename T>
bool ComponentArray<T>::HasData(Entity entity) const {
    return m_entityToIndexMap.find(entity.GetID()) != m_entityToIndexMap.end();
}

template<typename T>
void ComponentArray<T>::EntityDestroyed(Entity entity) {
    if (m_entityToIndexMap.find(entity.GetID()) != m_entityToIndexMap.end()) {
        RemoveData(entity);
    }
}

// ============================================================================
// Component Manager Implementation
// ============================================================================

template<typename T>
void ComponentManager::RegisterComponent() {
    const char* typeName = GetTypeName<T>();
    
    ENGINE_ASSERT(m_componentTypes.find(typeName) == m_componentTypes.end(),
                  "Registering component type more than once");
    
    m_componentTypes.insert(std::make_pair(typeName, m_nextComponentType));
    m_componentArrays.insert(std::make_pair(typeName, std::make_shared<ComponentArray<T>>()));
    
    ++m_nextComponentType;
}

template<typename T>
ComponentTypeID ComponentManager::GetComponentType() {
    const char* typeName = GetTypeName<T>();
    
    ENGINE_ASSERT(m_componentTypes.find(typeName) != m_componentTypes.end(),
                  "Component not registered before use");
    
    return m_componentTypes[typeName];
}

template<typename T>
void ComponentManager::AddComponent(Entity entity, T component) {
    GetComponentArray<T>()->InsertData(entity, component);
}

template<typename T>
void ComponentManager::RemoveComponent(Entity entity) {
    GetComponentArray<T>()->RemoveData(entity);
}

template<typename T>
T& ComponentManager::GetComponent(Entity entity) {
    return GetComponentArray<T>()->GetData(entity);
}

template<typename T>
const T& ComponentManager::GetComponent(Entity entity) const {
    return GetComponentArray<T>()->GetData(entity);
}

template<typename T>
bool ComponentManager::HasComponent(Entity entity) const {
    return GetComponentArray<T>()->HasData(entity);
}

template<typename T>
const char* ComponentManager::GetTypeName() const {
    return typeid(T).name();
}

template<typename T>
std::shared_ptr<ComponentArray<T>> ComponentManager::GetComponentArray() {
    const char* typeName = GetTypeName<T>();

    ENGINE_ASSERT(m_componentTypes.find(typeName) != m_componentTypes.end(),
                  "Component not registered before use");

    return std::static_pointer_cast<ComponentArray<T>>(m_componentArrays[typeName]);
}

template<typename T>
std::shared_ptr<ComponentArray<T>> ComponentManager::GetComponentArray() const {
    const char* typeName = GetTypeName<T>();

    ENGINE_ASSERT(m_componentTypes.find(typeName) != m_componentTypes.end(),
                  "Component not registered before use");

    return std::static_pointer_cast<ComponentArray<T>>(m_componentArrays.at(typeName));
}

inline void ComponentManager::EntityDestroyed(Entity entity) {
    for (auto const& pair : m_componentArrays) {
        auto const& component = pair.second;
        component->EntityDestroyed(entity);
    }
}

inline void ComponentManager::PrintDebugInfo() const {
    std::cout << "ComponentManager Debug Info:" << std::endl;
    std::cout << "  Registered component types: " << m_componentTypes.size() << std::endl;

    for (auto const& pair : m_componentArrays) {
        auto const& typeName = pair.first;
        auto const& componentArray = pair.second;
        std::cout << "    " << typeName << " (count: " << componentArray->Size() << ")" << std::endl;
    }
}

} // namespace Engine
