//
// TestSystems.h
// macOS 3D Looter-Shooter Game Engine
//
// Test systems for validating the ECS implementation
//

#pragma once

#include "System.h"
#include "Components.h"
#include <iostream>

namespace Engine {

// ============================================================================
// Movement System
// ============================================================================

class MovementSystem : public System {
public:
    void Update(DeltaTime deltaTime) override {
        for (auto& entity : m_entities) {
            auto& transform = Engine::Engine::GetInstance()->GetECS().GetComponent<Transform>(entity);
            auto& velocity = Engine::Engine::GetInstance()->GetECS().GetComponent<Velocity>(entity);
            
            // Update position based on velocity
            transform.position += velocity.linear * deltaTime;
            
            // Update rotation based on angular velocity
            transform.rotation += velocity.angular * deltaTime;
        }
    }
    
    const char* GetName() const override {
        return "MovementSystem";
    }
};

// ============================================================================
// Health System
// ============================================================================

class HealthSystem : public System {
public:
    void Update(DeltaTime deltaTime) override {
        ENGINE_UNUSED(deltaTime);
        
        auto& ecs = Engine::Engine::GetInstance()->GetECS();
        
        // Check for dead entities and mark them for removal
        std::vector<Entity> deadEntities;
        
        for (auto& entity : m_entities) {
            auto& health = ecs.GetComponent<Health>(entity);
            
            if (health.isDead) {
                deadEntities.push_back(entity);
                
                // Print death message if entity has a name
                if (ecs.HasComponent<Name>(entity)) {
                    auto& name = ecs.GetComponent<Name>(entity);
                    std::cout << "💀 " << name.name << " has died!" << std::endl;
                }
            }
        }
        
        // Remove dead entities (in a real game, you might want to handle this differently)
        for (auto& entity : deadEntities) {
            std::cout << "🗑️  Removing dead entity " << entity.GetID() << std::endl;
            ecs.DestroyEntity(entity);
        }
    }
    
    const char* GetName() const override {
        return "HealthSystem";
    }
};

// ============================================================================
// Debug System
// ============================================================================

class DebugSystem : public System {
public:
    DebugSystem() : m_printInterval(2.0f), m_timeSinceLastPrint(0.0f) {}
    
    void Update(DeltaTime deltaTime) override {
        m_timeSinceLastPrint += deltaTime;
        
        if (m_timeSinceLastPrint >= m_printInterval) {
            PrintEntityInfo();
            m_timeSinceLastPrint = 0.0f;
        }
    }
    
    const char* GetName() const override {
        return "DebugSystem";
    }
    
    void SetPrintInterval(float interval) {
        m_printInterval = interval;
    }
    
private:
    float m_printInterval;
    float m_timeSinceLastPrint;
    
    void PrintEntityInfo() {
        if (m_entities.empty()) {
            return;
        }
        
        std::cout << "🔍 Debug System - Tracking " << m_entities.size() << " entities:" << std::endl;
        
        auto& ecs = Engine::Engine::GetInstance()->GetECS();
        
        for (auto& entity : m_entities) {
            std::cout << "  Entity " << entity.GetID() << ": ";
            
            if (ecs.HasComponent<Name>(entity)) {
                auto& name = ecs.GetComponent<Name>(entity);
                std::cout << "'" << name.name << "' ";
            }
            
            if (ecs.HasComponent<Transform>(entity)) {
                auto& transform = ecs.GetComponent<Transform>(entity);
                std::cout << "pos(" << transform.position.x << ", " 
                         << transform.position.y << ", " << transform.position.z << ") ";
            }
            
            if (ecs.HasComponent<Health>(entity)) {
                auto& health = ecs.GetComponent<Health>(entity);
                std::cout << "health(" << health.currentHealth << "/" << health.maxHealth << ") ";
            }
            
            if (ecs.HasComponent<Velocity>(entity)) {
                auto& velocity = ecs.GetComponent<Velocity>(entity);
                std::cout << "vel(" << velocity.linear.x << ", " 
                         << velocity.linear.y << ", " << velocity.linear.z << ") ";
            }
            
            std::cout << std::endl;
        }
    }
};

} // namespace Engine
