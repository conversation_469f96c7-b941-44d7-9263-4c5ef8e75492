//
// Types.h
// macOS 3D Looter-Shooter Game Engine
//
// Core type definitions and constants for the engine
//

#pragma once

#include <cstdint>
#include <cstddef>
#include <limits>

namespace Engine {

// ============================================================================
// Basic Types
// ============================================================================

using EntityID = uint32_t;
using ComponentTypeID = uint32_t;
using SystemID = uint32_t;

// Invalid/null identifiers
static constexpr EntityID INVALID_ENTITY = std::numeric_limits<EntityID>::max();
static constexpr ComponentTypeID INVALID_COMPONENT_TYPE = std::numeric_limits<ComponentTypeID>::max();
static constexpr SystemID INVALID_SYSTEM = std::numeric_limits<SystemID>::max();

// Maximum entities and components
static constexpr size_t MAX_ENTITIES = 10000;
static constexpr size_t MAX_COMPONENT_TYPES = 64;
static constexpr size_t MAX_SYSTEMS = 32;

// ============================================================================
// Time Types
// ============================================================================

using TimeStamp = double;      // Time in seconds since engine start
using DeltaTime = float;       // Time delta in seconds
using FrameCount = uint64_t;   // Frame counter

// Target frame rate and timing
static constexpr float TARGET_FPS = 60.0f;
static constexpr float TARGET_FRAME_TIME = 1.0f / TARGET_FPS;
static constexpr float MAX_FRAME_TIME = 1.0f / 20.0f; // Cap at 20 FPS minimum

// ============================================================================
// Memory Types
// ============================================================================

using MemorySize = size_t;
using MemoryAlignment = size_t;

// Memory pool sizes (in bytes)
static constexpr MemorySize MEMORY_POOL_SIZE_SMALL = 1024 * 1024;      // 1 MB
static constexpr MemorySize MEMORY_POOL_SIZE_MEDIUM = 16 * 1024 * 1024; // 16 MB
static constexpr MemorySize MEMORY_POOL_SIZE_LARGE = 64 * 1024 * 1024;  // 64 MB

// Default alignment
static constexpr MemoryAlignment DEFAULT_ALIGNMENT = 16;

// ============================================================================
// Engine State
// ============================================================================

enum class EngineState {
    Uninitialized,
    Initializing,
    Running,
    Paused,
    Shutting_Down,
    Shutdown
};

// ============================================================================
// Debug and Logging
// ============================================================================

enum class LogLevel {
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warning = 3,
    Error = 4,
    Fatal = 5
};

// ============================================================================
// Math Types (Forward declarations - will be expanded in Math.h)
// ============================================================================

struct Vector2;
struct Vector3;
struct Vector4;
struct Matrix4;
struct Quaternion;

// ============================================================================
// Component Signature
// ============================================================================

// Simple bitset implementation to avoid C++ standard library conflicts
class ComponentSignature {
public:
    ComponentSignature() : m_bits(0) {}

    void set(size_t pos, bool value = true) {
        if (pos < MAX_COMPONENT_TYPES) {
            if (value) {
                m_bits |= (1ULL << pos);
            } else {
                m_bits &= ~(1ULL << pos);
            }
        }
    }

    void reset() { m_bits = 0; }
    void reset(size_t pos) { set(pos, false); }

    bool test(size_t pos) const {
        return pos < MAX_COMPONENT_TYPES && (m_bits & (1ULL << pos)) != 0;
    }

    bool any() const { return m_bits != 0; }
    bool none() const { return m_bits == 0; }

    ComponentSignature operator&(const ComponentSignature& other) const {
        ComponentSignature result;
        result.m_bits = m_bits & other.m_bits;
        return result;
    }

    ComponentSignature operator|(const ComponentSignature& other) const {
        ComponentSignature result;
        result.m_bits = m_bits | other.m_bits;
        return result;
    }

    bool operator==(const ComponentSignature& other) const {
        return m_bits == other.m_bits;
    }

private:
    uint64_t m_bits;
};

// ============================================================================
// Utility Macros
// ============================================================================

#ifdef DEBUG
    #define ENGINE_ASSERT(condition, message) \
        do { \
            if (!(condition)) { \
                std::cerr << "Assertion failed: " << message << std::endl; \
                std::cerr << "File: " << __FILE__ << ", Line: " << __LINE__ << std::endl; \
                std::abort(); \
            } \
        } while(0)
#else
    #define ENGINE_ASSERT(condition, message) ((void)0)
#endif

#define ENGINE_UNUSED(x) ((void)(x))

// ============================================================================
// Platform Specific
// ============================================================================

#ifdef __APPLE__
    #define ENGINE_PLATFORM_MACOS 1
    #include <TargetConditionals.h>
    #if TARGET_OS_MAC
        #define ENGINE_METAL_AVAILABLE 1
    #endif
#endif

} // namespace Engine
