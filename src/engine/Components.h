//
// Components.h
// macOS 3D Looter-Shooter Game Engine
//
// Basic component definitions for the ECS system
//

#pragma once

#include "Types.h"
#include <string>
#include <cmath>

namespace Engine {

// ============================================================================
// Math Types (temporary implementation - will be moved to Math.h later)
// ============================================================================

struct Vector3 {
    float x, y, z;

    Vector3() : x(0.0f), y(0.0f), z(0.0f) {}
    Vector3(float x_, float y_, float z_) : x(x_), y(y_), z(z_) {}

    Vector3 operator+(const Vector3& other) const {
        return Vector3(x + other.x, y + other.y, z + other.z);
    }

    Vector3 operator-(const Vector3& other) const {
        return Vector3(x - other.x, y - other.y, z - other.z);
    }

    Vector3 operator*(float scalar) const {
        return Vector3(x * scalar, y * scalar, z * scalar);
    }

    Vector3& operator+=(const Vector3& other) {
        x += other.x;
        y += other.y;
        z += other.z;
        return *this;
    }

    Vector3& operator-=(const Vector3& other) {
        x -= other.x;
        y -= other.y;
        z -= other.z;
        return *this;
    }

    Vector3& operator*=(float scalar) {
        x *= scalar;
        y *= scalar;
        z *= scalar;
        return *this;
    }

    float Length() const {
        return std::sqrt(x * x + y * y + z * z);
    }

    float LengthSquared() const {
        return x * x + y * y + z * z;
    }

    Vector3 Normalized() const {
        float len = Length();
        if (len > 0.0f) {
            return Vector3(x / len, y / len, z / len);
        }
        return Vector3();
    }

    void Normalize() {
        float len = Length();
        if (len > 0.0f) {
            x /= len;
            y /= len;
            z /= len;
        }
    }

    float Dot(const Vector3& other) const {
        return x * other.x + y * other.y + z * other.z;
    }

    Vector3 Cross(const Vector3& other) const {
        return Vector3(
            y * other.z - z * other.y,
            z * other.x - x * other.z,
            x * other.y - y * other.x
        );
    }
};

// Global operator for scalar * Vector3
inline Vector3 operator*(float scalar, const Vector3& vec) {
    return vec * scalar;
}

// Placeholder for other math types
struct Vector2 {
    float x, y;
    Vector2() : x(0.0f), y(0.0f) {}
    Vector2(float x_, float y_) : x(x_), y(y_) {}
};

struct Vector4 {
    float x, y, z, w;
    Vector4() : x(0.0f), y(0.0f), z(0.0f), w(0.0f) {}
    Vector4(float x_, float y_, float z_, float w_) : x(x_), y(y_), z(z_), w(w_) {}
};

struct Matrix4 {
    float m[16];
    Matrix4() {
        for (int i = 0; i < 16; ++i) m[i] = 0.0f;
        m[0] = m[5] = m[10] = m[15] = 1.0f; // Identity matrix
    }
};

struct Quaternion {
    float x, y, z, w;
    Quaternion() : x(0.0f), y(0.0f), z(0.0f), w(1.0f) {}
    Quaternion(float x_, float y_, float z_, float w_) : x(x_), y(y_), z(z_), w(w_) {}
};

// ============================================================================
// Transform Component
// ============================================================================

struct Transform {
    Vector3 position = {0.0f, 0.0f, 0.0f};
    Vector3 rotation = {0.0f, 0.0f, 0.0f}; // Euler angles in radians
    Vector3 scale = {1.0f, 1.0f, 1.0f};
    
    Transform() = default;
    Transform(const Vector3& pos) : position(pos) {}
    Transform(const Vector3& pos, const Vector3& rot) : position(pos), rotation(rot) {}
    Transform(const Vector3& pos, const Vector3& rot, const Vector3& scl) 
        : position(pos), rotation(rot), scale(scl) {}
};

// ============================================================================
// Name Component
// ============================================================================

struct Name {
    std::string name;
    
    Name() = default;
    Name(const std::string& n) : name(n) {}
    Name(const char* n) : name(n) {}
};

// ============================================================================
// Health Component
// ============================================================================

struct Health {
    float currentHealth = 100.0f;
    float maxHealth = 100.0f;
    bool isDead = false;
    
    Health() = default;
    Health(float max) : currentHealth(max), maxHealth(max) {}
    
    void TakeDamage(float damage) {
        currentHealth -= damage;
        if (currentHealth <= 0.0f) {
            currentHealth = 0.0f;
            isDead = true;
        }
    }
    
    void Heal(float amount) {
        if (!isDead) {
            currentHealth += amount;
            if (currentHealth > maxHealth) {
                currentHealth = maxHealth;
            }
        }
    }
    
    float GetHealthPercentage() const {
        return maxHealth > 0.0f ? currentHealth / maxHealth : 0.0f;
    }
};

// ============================================================================
// Velocity Component
// ============================================================================

struct Velocity {
    Vector3 linear = {0.0f, 0.0f, 0.0f};   // Linear velocity in units/second
    Vector3 angular = {0.0f, 0.0f, 0.0f};  // Angular velocity in radians/second
    
    Velocity() = default;
    Velocity(const Vector3& lin) : linear(lin) {}
    Velocity(const Vector3& lin, const Vector3& ang) : linear(lin), angular(ang) {}
};

// ============================================================================
// Renderable Component
// ============================================================================

struct Renderable {
    std::string meshName;
    std::string materialName;
    bool visible = true;
    float lodDistance = 100.0f; // Distance for level-of-detail switching
    
    Renderable() = default;
    Renderable(const std::string& mesh) : meshName(mesh) {}
    Renderable(const std::string& mesh, const std::string& material) 
        : meshName(mesh), materialName(material) {}
};

} // namespace Engine
