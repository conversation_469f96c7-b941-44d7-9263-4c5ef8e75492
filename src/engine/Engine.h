//
// Engine.h
// macOS 3D Looter-Shooter Game Engine
//
// Main engine class that coordinates all subsystems
//

#pragma once

#include "Types.h"
#include "ECS.h"
#include <memory>
#include <chrono>
#include <thread>
#include <iostream>

namespace Engine {

// Forward declarations for future implementation
// class MemoryManager;
// class EventManager;

// ============================================================================
// Engine Configuration
// ============================================================================

struct EngineConfig {
    // Window settings
    int windowWidth = 1920;
    int windowHeight = 1080;
    bool fullscreen = false;
    bool vsync = true;
    
    // Performance settings
    float targetFPS = 60.0f;
    bool enableDebugMode = false;
    
    // Memory settings
    MemorySize memoryPoolSize = MEMORY_POOL_SIZE_LARGE;
    
    // Asset settings
    std::string assetPath = "assets/";
    
    // Game settings
    std::string gameName = "macOS 3D Looter-Shooter";
    std::string gameVersion = "1.0.0";
};

// ============================================================================
// Engine Class
// ============================================================================

class Engine {
public:
    Engine();
    ~Engine();
    
    // Engine lifecycle
    bool Initialize(const EngineConfig& config);
    void Run();
    void RunFrame(); // Process a single frame
    void Shutdown();
    
    // Engine state
    EngineState GetState() const { return m_state; }
    bool IsRunning() const { return m_state == EngineState::Running; }
    void RequestShutdown() { m_shouldShutdown = true; }
    
    // Subsystem access
    ECS& GetECS() { return *m_ecs; }
    const ECS& GetECS() const { return *m_ecs; }
    
    // Time and frame information
    DeltaTime GetDeltaTime() const { return m_deltaTime; }
    TimeStamp GetTotalTime() const { return m_totalTime; }
    FrameCount GetFrameCount() const { return m_frameCount; }
    float GetFPS() const { return m_fps; }
    
    // Configuration
    const EngineConfig& GetConfig() const { return m_config; }
    
    // Debug information
    void PrintDebugInfo() const;
    
    // Singleton access (for convenience, but prefer dependency injection)
    static Engine* GetInstance() { return s_instance; }
    
private:
    // Core subsystems
    std::unique_ptr<ECS> m_ecs;
    // TODO: Add these when implemented
    // std::unique_ptr<MemoryManager> m_memoryManager;
    // std::unique_ptr<EventManager> m_eventManager;
    
    // Engine state
    EngineState m_state = EngineState::Uninitialized;
    EngineConfig m_config;
    bool m_shouldShutdown = false;
    
    // Timing
    std::chrono::high_resolution_clock::time_point m_startTime;
    std::chrono::high_resolution_clock::time_point m_lastFrameTime;
    DeltaTime m_deltaTime = 0.0f;
    TimeStamp m_totalTime = 0.0;
    FrameCount m_frameCount = 0;
    float m_fps = 0.0f;
    
    // FPS calculation
    static constexpr int FPS_SAMPLE_COUNT = 60;
    float m_fpsHistory[FPS_SAMPLE_COUNT] = {0.0f};
    int m_fpsHistoryIndex = 0;
    
    // Singleton instance
    static Engine* s_instance;
    
    // Private methods
    void UpdateTiming();
    void CalculateFPS();
    bool InitializeSubsystems();
    void ShutdownSubsystems();
    void GameLoop();
};

// ============================================================================
// Engine Implementation
// ============================================================================

inline Engine::Engine() {
    ENGINE_ASSERT(s_instance == nullptr, "Engine instance already exists");
    s_instance = this;
    
    m_ecs = std::make_unique<ECS>();
    // Memory and Event managers will be created when implemented
}

inline Engine::~Engine() {
    if (m_state != EngineState::Shutdown) {
        Shutdown();
    }
    s_instance = nullptr;
}

inline bool Engine::Initialize(const EngineConfig& config) {
    ENGINE_ASSERT(m_state == EngineState::Uninitialized, "Engine already initialized");
    
    m_state = EngineState::Initializing;
    m_config = config;
    
    std::cout << "🚀 Initializing " << m_config.gameName << " v" << m_config.gameVersion << std::endl;
    
    // Initialize timing
    m_startTime = std::chrono::high_resolution_clock::now();
    m_lastFrameTime = m_startTime;
    
    // Initialize subsystems
    if (!InitializeSubsystems()) {
        std::cerr << "❌ Failed to initialize engine subsystems" << std::endl;
        m_state = EngineState::Shutdown;
        return false;
    }
    
    m_state = EngineState::Running;
    std::cout << "✅ Engine initialization complete" << std::endl;
    
    if (m_config.enableDebugMode) {
        PrintDebugInfo();
    }
    
    return true;
}

inline void Engine::Run() {
    ENGINE_ASSERT(m_state == EngineState::Running, "Engine not running");

    std::cout << "🎮 Starting game loop..." << std::endl;
    GameLoop();
}

inline void Engine::RunFrame() {
    ENGINE_ASSERT(m_state == EngineState::Running, "Engine not running");

    UpdateTiming();

    // Update all systems
    m_ecs->Update(m_deltaTime);

    // TODO: Add other update phases when implemented
    // - Input processing
    // - Physics simulation
    // - Rendering
    // - Audio
}

inline void Engine::Shutdown() {
    if (m_state == EngineState::Shutdown) {
        return;
    }
    
    std::cout << "🛑 Shutting down engine..." << std::endl;
    m_state = EngineState::Shutting_Down;
    
    ShutdownSubsystems();
    
    m_state = EngineState::Shutdown;
    std::cout << "✅ Engine shutdown complete" << std::endl;
}

inline void Engine::UpdateTiming() {
    auto currentTime = std::chrono::high_resolution_clock::now();
    
    // Calculate delta time
    auto deltaTimeNs = std::chrono::duration_cast<std::chrono::nanoseconds>(currentTime - m_lastFrameTime);
    m_deltaTime = static_cast<float>(deltaTimeNs.count()) / 1e9f;
    
    // Cap delta time to prevent spiral of death
    if (m_deltaTime > MAX_FRAME_TIME) {
        m_deltaTime = MAX_FRAME_TIME;
    }
    
    // Calculate total time
    auto totalTimeNs = std::chrono::duration_cast<std::chrono::nanoseconds>(currentTime - m_startTime);
    m_totalTime = static_cast<double>(totalTimeNs.count()) / 1e9;
    
    m_lastFrameTime = currentTime;
    ++m_frameCount;
    
    CalculateFPS();
}

inline void Engine::CalculateFPS() {
    // Store current frame time in history
    m_fpsHistory[m_fpsHistoryIndex] = m_deltaTime;
    m_fpsHistoryIndex = (m_fpsHistoryIndex + 1) % FPS_SAMPLE_COUNT;
    
    // Calculate average frame time
    float averageFrameTime = 0.0f;
    for (int i = 0; i < FPS_SAMPLE_COUNT; ++i) {
        averageFrameTime += m_fpsHistory[i];
    }
    averageFrameTime /= FPS_SAMPLE_COUNT;
    
    // Calculate FPS
    if (averageFrameTime > 0.0f) {
        m_fps = 1.0f / averageFrameTime;
    }
}

inline bool Engine::InitializeSubsystems() {
    try {
        // Initialize ECS
        m_ecs->Initialize();
        
        // TODO: Initialize other subsystems when implemented
        // m_memoryManager->Initialize();
        // m_eventManager->Initialize();
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "❌ Subsystem initialization failed: " << e.what() << std::endl;
        return false;
    }
}

inline void Engine::ShutdownSubsystems() {
    // Shutdown in reverse order
    if (m_ecs) {
        m_ecs->Shutdown();
    }
    
    // TODO: Shutdown other subsystems when implemented
    // if (m_eventManager) m_eventManager->Shutdown();
    // if (m_memoryManager) m_memoryManager->Shutdown();
}

inline void Engine::GameLoop() {
    while (IsRunning() && !m_shouldShutdown) {
        UpdateTiming();
        
        // Update all systems
        m_ecs->Update(m_deltaTime);
        
        // TODO: Add other update phases when implemented
        // - Input processing
        // - Physics simulation
        // - Rendering
        // - Audio
        
        // Simple frame rate limiting (will be improved with proper vsync)
        if (m_config.targetFPS > 0.0f) {
            float targetFrameTime = 1.0f / m_config.targetFPS;
            if (m_deltaTime < targetFrameTime) {
                auto sleepTime = std::chrono::duration<float>(targetFrameTime - m_deltaTime);
                std::this_thread::sleep_for(sleepTime);
            }
        }
    }
}

inline void Engine::PrintDebugInfo() const {
    std::cout << "=== Engine Debug Information ===" << std::endl;
    std::cout << "State: ";
    switch (m_state) {
        case EngineState::Uninitialized: std::cout << "Uninitialized"; break;
        case EngineState::Initializing: std::cout << "Initializing"; break;
        case EngineState::Running: std::cout << "Running"; break;
        case EngineState::Paused: std::cout << "Paused"; break;
        case EngineState::Shutting_Down: std::cout << "Shutting Down"; break;
        case EngineState::Shutdown: std::cout << "Shutdown"; break;
    }
    std::cout << std::endl;
    
    std::cout << "Frame: " << m_frameCount << std::endl;
    std::cout << "Total Time: " << m_totalTime << "s" << std::endl;
    std::cout << "Delta Time: " << m_deltaTime << "s" << std::endl;
    std::cout << "FPS: " << m_fps << std::endl;
    std::cout << "Target FPS: " << m_config.targetFPS << std::endl;
    
    if (m_ecs) {
        m_ecs->PrintDebugInfo();
    }
    
    std::cout << "===============================" << std::endl;
}

// Static member declaration (definition will be in Engine.cpp)

} // namespace Engine
