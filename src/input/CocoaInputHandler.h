//
// CocoaInputHandler.h
// macOS 3D Looter-Shooter Game Engine
//
// Cocoa-based input handler for macOS
//

#pragma once

#include "InputSystem.h"

#ifdef __OBJC__
#import <Cocoa/Cocoa.h>
#else
#include <objc/objc.h>
typedef long NSInteger;
#endif

namespace Engine {
namespace Input {

// ============================================================================
// Key Code Mapping
// ============================================================================

class CocoaKeyMapper {
public:
    static KeyCode MapCocoaKeyCode(unsigned short cocoaKeyCode);
    static MouseButton MapCocoaMouseButton(NSInteger buttonNumber);
    
private:
    static void InitializeKeyMapping();
    static bool s_initialized;
    static KeyCode s_keyMapping[256];
};

// ============================================================================
// Cocoa Input Handler
// ============================================================================

class CocoaInputHandler {
public:
    CocoaInputHandler();
    ~CocoaInputHandler();
    
    // Initialization
    bool Initialize(InputSystem* inputSystem);
    void Shutdown();
    
    // Event handling methods (called from Objective-C)
    void HandleKeyDown(unsigned short keyCode, bool isRepeat);
    void HandleKeyUp(unsigned short keyCode);
    void HandleMouseDown(NSInteger buttonNumber, float x, float y);
    void HandleMouseUp(NSInteger buttonNumber, float x, float y);
    void HandleMouseMoved(float x, float y);
    void HandleScrollWheel(float deltaX, float deltaY, float x, float y);
    
    // Window and view management
    void SetWindow(id window);
    void SetView(id view);
    
    // Mouse capture
    void CaptureMouse(bool capture);
    bool IsMouseCaptured() const { return m_mouseCaptured; }
    
    // Cursor management
    void ShowCursor(bool show);
    void SetCursorPosition(float x, float y);

private:
    InputSystem* m_inputSystem;
    id m_window;
    id m_view;
    
    bool m_initialized;
    bool m_mouseCaptured;
    bool m_cursorVisible;
    
    // Mouse tracking
    float m_lastMouseX;
    float m_lastMouseY;
    bool m_firstMouseMove;
    
    // Internal methods
    void UpdateMousePosition(float x, float y);
    void ConvertScreenToViewCoordinates(float& x, float& y);
};

} // namespace Input
} // namespace Engine

// ============================================================================
// Objective-C Interface (must be outside namespace)
// ============================================================================

#ifdef __OBJC__

// Custom NSView subclass for handling input events
@interface GameInputView : NSView {
    Engine::Input::CocoaInputHandler* inputHandler;
}

@property (nonatomic, assign) Engine::Input::CocoaInputHandler* inputHandler;

- (void)setInputHandler:(Engine::Input::CocoaInputHandler*)handler;

@end

// Custom NSWindow subclass for handling window events
@interface GameInputWindow : NSWindow {
    Engine::Input::CocoaInputHandler* inputHandler;
}

@property (nonatomic, assign) Engine::Input::CocoaInputHandler* inputHandler;

- (void)setInputHandler:(Engine::Input::CocoaInputHandler*)handler;

@end

#endif // __OBJC__

// ============================================================================
// C Interface for Integration
// ============================================================================

extern "C" {
    // Create and manage the input handler
    void* CreateCocoaInputHandler();
    void DestroyCocoaInputHandler(void* handler);
    bool InitializeCocoaInputHandler(void* handler, void* inputSystem);
    void ShutdownCocoaInputHandler(void* handler);

    // Window and view setup
    void SetCocoaInputWindow(void* handler, void* window);
    void SetCocoaInputView(void* handler, void* view);

    // Mouse control
    void CaptureCocoaMouse(void* handler, bool capture);
    void ShowCocoaCursor(void* handler, bool show);
    void SetCocoaCursorPosition(void* handler, float x, float y);

    // Manual event injection (for testing)
    void InjectCocoaKeyEvent(void* handler, unsigned short keyCode, bool isDown, bool isRepeat);
    void InjectCocoaMouseEvent(void* handler, int buttonNumber, bool isDown, float x, float y);
    void InjectCocoaMouseMove(void* handler, float x, float y);
    void InjectCocoaScrollEvent(void* handler, float deltaX, float deltaY, float x, float y);
}
