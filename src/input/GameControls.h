//
// GameControls.h
// macOS 3D Looter-Shooter Game Engine
//
// Game-specific input controls and handling
//

#pragma once

#include "engine/ECS.h"
#include "engine/Components.h"

#ifdef __OBJC__
#import <Cocoa/Cocoa.h>
#else
#include <Cocoa/Cocoa.h>
#endif

namespace Engine {
namespace Input {

// ============================================================================
// Game Controls Class
// ============================================================================

class GameControls {
public:
    GameControls();
    ~GameControls();
    
    void Initialize(ECS* ecs);
    void Update(float deltaTime);
    void Shutdown();
    
    // Input handling
    void HandleKeyDown(int keyCode);
    void HandleKeyUp(int keyCode);
    void HandleMouseMove(float deltaX, float deltaY);
    void HandleMouseClick(int button, bool pressed);
    
    // Player control
    void SetPlayerEntity(Entity player) { m_playerEntity = player; }
    Entity GetPlayerEntity() const { return m_playerEntity; }
    
    // Camera control
    void SetCameraEntity(Entity camera) { m_cameraEntity = camera; }
    Entity GetCameraEntity() const { return m_cameraEntity; }
    
    // Control state
    struct ControlState {
        bool moveForward = false;
        bool moveBackward = false;
        bool moveLeft = false;
        bool moveRight = false;
        bool moveUp = false;
        bool moveDown = false;
        bool shoot = false;
        bool sprint = false;
        
        float mouseSensitivity = 0.002f;
        float moveSpeed = 5.0f;
        float sprintMultiplier = 2.0f;
    };
    
    const ControlState& GetControlState() const { return m_controlState; }
    
private:
    ECS* m_ecs;
    Entity m_playerEntity;
    Entity m_cameraEntity;
    ControlState m_controlState;
    bool m_initialized;
    
    // Input processing
    void UpdatePlayerMovement(float deltaTime);
    void UpdateCameraMovement(float deltaTime);
    void ProcessShooting(float deltaTime);
    
    // Key mapping
    bool IsKeyPressed(int keyCode);
    void UpdateKeyState(int keyCode, bool pressed);
    
    // Key states
    bool m_keyStates[256];
};

// ============================================================================
// Key Codes (macOS)
// ============================================================================

enum GameKeyCode {
    KEY_W = 13,      // Forward
    KEY_A = 0,       // Left
    KEY_S = 1,       // Backward
    KEY_D = 2,       // Right
    KEY_Q = 12,      // Up
    KEY_E = 14,      // Down
    KEY_SPACE = 49,  // Shoot
    KEY_SHIFT = 56,  // Sprint
    KEY_ESC = 53,    // Escape
    KEY_TAB = 48,    // Tab
    KEY_ENTER = 36,  // Enter
    
    // Arrow keys
    KEY_UP = 126,
    KEY_DOWN = 125,
    KEY_LEFT = 123,
    KEY_RIGHT = 124
};

} // namespace Input
} // namespace Engine
