//
// InputSystem.h
// macOS 3D Looter-Shooter Game Engine
//
// Input system for handling keyboard, mouse, and gamepad input
//

#pragma once

#include "engine/Types.h"
#include "engine/System.h"
#include <unordered_map>
#include <vector>
#include <functional>

namespace Engine {
namespace Input {

// ============================================================================
// Input Types and Enums
// ============================================================================

enum class KeyCode {
    // Letters
    A = 0, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z,
    
    // Numbers
    Num0, Num1, Num2, Num3, Num4, Num5, Num6, Num7, Num8, Num9,
    
    // Function keys
    F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12,
    
    // Arrow keys
    Up, Down, Left, Right,
    
    // Special keys
    Space, Enter, Escape, Tab, Backspace, Delete,
    Shift, Control, Alt, Command,
    
    // Count for array sizing
    COUNT
};

enum class MouseButton {
    Left = 0,
    Right,
    Middle,
    Button4,
    Button5,
    COUNT
};

enum class InputState {
    Released = 0,
    Pressed,
    Held
};

struct MousePosition {
    float x, y;
    
    MousePosition() : x(0.0f), y(0.0f) {}
    MousePosition(float x_, float y_) : x(x_), y(y_) {}
};

struct MouseDelta {
    float deltaX, deltaY;
    float scrollX, scrollY;
    
    MouseDelta() : deltaX(0.0f), deltaY(0.0f), scrollX(0.0f), scrollY(0.0f) {}
};

// ============================================================================
// Input Configuration
// ============================================================================

struct InputConfig {
    // Mouse sensitivity
    float mouseSensitivity = 1.0f;
    float scrollSensitivity = 1.0f;
    
    // Mouse settings
    bool invertMouseY = false;
    bool rawMouseInput = true;
    
    // Keyboard settings
    float keyRepeatDelay = 0.5f;
    float keyRepeatRate = 0.1f;
    
    // Dead zones for analog inputs
    float analogDeadZone = 0.1f;
    
    // Input smoothing
    bool enableMouseSmoothing = false;
    float mouseSmoothingFactor = 0.1f;
};

// ============================================================================
// Input Events
// ============================================================================

struct KeyEvent {
    KeyCode key;
    InputState state;
    bool isRepeat;
    uint32_t modifiers; // Shift, Ctrl, Alt, etc.
};

struct MouseButtonEvent {
    MouseButton button;
    InputState state;
    MousePosition position;
};

struct MouseMoveEvent {
    MousePosition position;
    MouseDelta delta;
};

struct ScrollEvent {
    float deltaX, deltaY;
    MousePosition position;
};

// Event callback types
using KeyEventCallback = std::function<void(const KeyEvent&)>;
using MouseButtonEventCallback = std::function<void(const MouseButtonEvent&)>;
using MouseMoveEventCallback = std::function<void(const MouseMoveEvent&)>;
using ScrollEventCallback = std::function<void(const ScrollEvent&)>;

// ============================================================================
// Input Action System
// ============================================================================

enum class ActionType {
    Digital,  // On/Off (keyboard keys, mouse buttons)
    Analog    // Continuous values (mouse movement, analog sticks)
};

struct InputAction {
    std::string name;
    ActionType type;
    std::vector<KeyCode> keys;
    std::vector<MouseButton> mouseButtons;
    bool isActive = false;
    float value = 0.0f;
    float deadZone = 0.1f;
};

// ============================================================================
// Input System
// ============================================================================

class InputSystem : public System {
public:
    InputSystem();
    ~InputSystem();
    
    // System interface
    void Initialize() override;
    void Update(DeltaTime deltaTime) override;
    void Shutdown() override;
    const char* GetName() const override { return "InputSystem"; }
    
    // Configuration
    void SetConfig(const InputConfig& config);
    const InputConfig& GetConfig() const { return m_config; }
    
    // Raw input state queries
    bool IsKeyPressed(KeyCode key) const;
    bool IsKeyHeld(KeyCode key) const;
    bool IsKeyReleased(KeyCode key) const;
    
    bool IsMouseButtonPressed(MouseButton button) const;
    bool IsMouseButtonHeld(MouseButton button) const;
    bool IsMouseButtonReleased(MouseButton button) const;
    
    MousePosition GetMousePosition() const { return m_mousePosition; }
    MouseDelta GetMouseDelta() const { return m_mouseDelta; }
    
    // Action system
    void RegisterAction(const InputAction& action);
    void UnregisterAction(const std::string& name);
    bool IsActionActive(const std::string& name) const;
    float GetActionValue(const std::string& name) const;
    
    // Event callbacks
    void SetKeyEventCallback(KeyEventCallback callback);
    void SetMouseButtonEventCallback(MouseButtonEventCallback callback);
    void SetMouseMoveEventCallback(MouseMoveEventCallback callback);
    void SetScrollEventCallback(ScrollEventCallback callback);
    
    // Platform-specific input handling
    void HandleKeyEvent(KeyCode key, InputState state, bool isRepeat = false);
    void HandleMouseButtonEvent(MouseButton button, InputState state, float x, float y);
    void HandleMouseMoveEvent(float x, float y);
    void HandleScrollEvent(float deltaX, float deltaY, float x, float y);
    
    // Utility functions
    static const char* GetKeyName(KeyCode key);
    static const char* GetMouseButtonName(MouseButton button);

private:
    // Input state
    InputState m_keyStates[static_cast<size_t>(KeyCode::COUNT)];
    InputState m_mouseButtonStates[static_cast<size_t>(MouseButton::COUNT)];
    
    MousePosition m_mousePosition;
    MousePosition m_lastMousePosition;
    MouseDelta m_mouseDelta;
    
    // Configuration
    InputConfig m_config;
    
    // Action system
    std::unordered_map<std::string, InputAction> m_actions;
    
    // Event callbacks
    KeyEventCallback m_keyEventCallback;
    MouseButtonEventCallback m_mouseButtonEventCallback;
    MouseMoveEventCallback m_mouseMoveEventCallback;
    ScrollEventCallback m_scrollEventCallback;
    
    // Internal methods
    void UpdateActions();
    void UpdateMouseDelta();
    void ResetFrameState();
    
    // Input smoothing
    std::vector<MouseDelta> m_mouseDeltaHistory;
    static constexpr size_t MOUSE_HISTORY_SIZE = 5;
    
    bool m_initialized = false;
};

// ============================================================================
// Input Manager (Singleton)
// ============================================================================

class InputManager {
public:
    static InputManager& GetInstance();
    
    void Initialize();
    void Shutdown();
    
    InputSystem* GetInputSystem() { return m_inputSystem; }
    
    // Convenience methods that delegate to the input system
    bool IsKeyPressed(KeyCode key) const;
    bool IsKeyHeld(KeyCode key) const;
    MousePosition GetMousePosition() const;
    MouseDelta GetMouseDelta() const;
    
    bool IsActionActive(const std::string& name) const;
    float GetActionValue(const std::string& name) const;

private:
    InputManager() = default;
    ~InputManager() = default;
    InputManager(const InputManager&) = delete;
    InputManager& operator=(const InputManager&) = delete;
    
    InputSystem* m_inputSystem = nullptr;
};

} // namespace Input
} // namespace Engine
