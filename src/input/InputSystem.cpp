//
// InputSystem.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Input system implementation
//

#include "InputSystem.h"
#include <iostream>
#include <algorithm>
#include <cstring>

namespace Engine {
namespace Input {

// ============================================================================
// InputSystem Implementation
// ============================================================================

InputSystem::InputSystem() {
    // Initialize all input states to released
    std::memset(m_keyStates, 0, sizeof(m_keyStates));
    std::memset(m_mouseButtonStates, 0, sizeof(m_mouseButtonStates));
    
    // Initialize mouse delta history
    m_mouseDeltaHistory.resize(MOUSE_HISTORY_SIZE);
}

InputSystem::~InputSystem() {
    if (m_initialized) {
        Shutdown();
    }
}

void InputSystem::Initialize() {
    if (m_initialized) {
        return;
    }
    
    std::cout << "🎮 Initializing InputSystem..." << std::endl;
    
    // Reset all states
    ResetFrameState();
    
    m_initialized = true;
    std::cout << "✅ InputSystem initialized" << std::endl;
}

void InputSystem::Update(DeltaTime deltaTime) {
    if (!m_initialized) {
        return;
    }
    
    // Update mouse delta
    UpdateMouseDelta();
    
    // Update action system
    UpdateActions();
    
    // Apply mouse smoothing if enabled
    if (m_config.enableMouseSmoothing) {
        // Add current delta to history
        m_mouseDeltaHistory.push_back(m_mouseDelta);
        if (m_mouseDeltaHistory.size() > MOUSE_HISTORY_SIZE) {
            m_mouseDeltaHistory.erase(m_mouseDeltaHistory.begin());
        }
        
        // Calculate smoothed delta
        MouseDelta smoothedDelta;
        for (const auto& delta : m_mouseDeltaHistory) {
            smoothedDelta.deltaX += delta.deltaX;
            smoothedDelta.deltaY += delta.deltaY;
        }
        
        float factor = 1.0f / static_cast<float>(m_mouseDeltaHistory.size());
        smoothedDelta.deltaX *= factor * m_config.mouseSmoothingFactor;
        smoothedDelta.deltaY *= factor * m_config.mouseSmoothingFactor;
        
        m_mouseDelta = smoothedDelta;
    }
    
    // Store last mouse position for next frame
    m_lastMousePosition = m_mousePosition;
}

void InputSystem::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "🎮 Shutting down InputSystem..." << std::endl;
    
    // Clear all callbacks
    m_keyEventCallback = nullptr;
    m_mouseButtonEventCallback = nullptr;
    m_mouseMoveEventCallback = nullptr;
    m_scrollEventCallback = nullptr;
    
    // Clear actions
    m_actions.clear();
    
    m_initialized = false;
    std::cout << "✅ InputSystem shutdown complete" << std::endl;
}

void InputSystem::SetConfig(const InputConfig& config) {
    m_config = config;
    std::cout << "🎮 Input configuration updated" << std::endl;
}

// ============================================================================
// Raw Input State Queries
// ============================================================================

bool InputSystem::IsKeyPressed(KeyCode key) const {
    size_t index = static_cast<size_t>(key);
    return index < static_cast<size_t>(KeyCode::COUNT) && 
           m_keyStates[index] == InputState::Pressed;
}

bool InputSystem::IsKeyHeld(KeyCode key) const {
    size_t index = static_cast<size_t>(key);
    return index < static_cast<size_t>(KeyCode::COUNT) && 
           (m_keyStates[index] == InputState::Pressed || m_keyStates[index] == InputState::Held);
}

bool InputSystem::IsKeyReleased(KeyCode key) const {
    size_t index = static_cast<size_t>(key);
    return index < static_cast<size_t>(KeyCode::COUNT) && 
           m_keyStates[index] == InputState::Released;
}

bool InputSystem::IsMouseButtonPressed(MouseButton button) const {
    size_t index = static_cast<size_t>(button);
    return index < static_cast<size_t>(MouseButton::COUNT) && 
           m_mouseButtonStates[index] == InputState::Pressed;
}

bool InputSystem::IsMouseButtonHeld(MouseButton button) const {
    size_t index = static_cast<size_t>(button);
    return index < static_cast<size_t>(MouseButton::COUNT) && 
           (m_mouseButtonStates[index] == InputState::Pressed || 
            m_mouseButtonStates[index] == InputState::Held);
}

bool InputSystem::IsMouseButtonReleased(MouseButton button) const {
    size_t index = static_cast<size_t>(button);
    return index < static_cast<size_t>(MouseButton::COUNT) && 
           m_mouseButtonStates[index] == InputState::Released;
}

// ============================================================================
// Action System
// ============================================================================

void InputSystem::RegisterAction(const InputAction& action) {
    m_actions[action.name] = action;
    std::cout << "🎮 Registered input action: " << action.name << std::endl;
}

void InputSystem::UnregisterAction(const std::string& name) {
    auto it = m_actions.find(name);
    if (it != m_actions.end()) {
        m_actions.erase(it);
        std::cout << "🎮 Unregistered input action: " << name << std::endl;
    }
}

bool InputSystem::IsActionActive(const std::string& name) const {
    auto it = m_actions.find(name);
    return it != m_actions.end() && it->second.isActive;
}

float InputSystem::GetActionValue(const std::string& name) const {
    auto it = m_actions.find(name);
    return it != m_actions.end() ? it->second.value : 0.0f;
}

// ============================================================================
// Event Callbacks
// ============================================================================

void InputSystem::SetKeyEventCallback(KeyEventCallback callback) {
    m_keyEventCallback = callback;
}

void InputSystem::SetMouseButtonEventCallback(MouseButtonEventCallback callback) {
    m_mouseButtonEventCallback = callback;
}

void InputSystem::SetMouseMoveEventCallback(MouseMoveEventCallback callback) {
    m_mouseMoveEventCallback = callback;
}

void InputSystem::SetScrollEventCallback(ScrollEventCallback callback) {
    m_scrollEventCallback = callback;
}

// ============================================================================
// Platform Input Handling
// ============================================================================

void InputSystem::HandleKeyEvent(KeyCode key, InputState state, bool isRepeat) {
    size_t index = static_cast<size_t>(key);
    if (index >= static_cast<size_t>(KeyCode::COUNT)) {
        return;
    }
    
    m_keyStates[index] = state;
    
    // Fire callback if set
    if (m_keyEventCallback) {
        KeyEvent event;
        event.key = key;
        event.state = state;
        event.isRepeat = isRepeat;
        event.modifiers = 0; // TODO: Implement modifier tracking
        
        m_keyEventCallback(event);
    }
}

void InputSystem::HandleMouseButtonEvent(MouseButton button, InputState state, float x, float y) {
    size_t index = static_cast<size_t>(button);
    if (index >= static_cast<size_t>(MouseButton::COUNT)) {
        return;
    }
    
    m_mouseButtonStates[index] = state;
    m_mousePosition = MousePosition(x, y);
    
    // Fire callback if set
    if (m_mouseButtonEventCallback) {
        MouseButtonEvent event;
        event.button = button;
        event.state = state;
        event.position = m_mousePosition;
        
        m_mouseButtonEventCallback(event);
    }
}

void InputSystem::HandleMouseMoveEvent(float x, float y) {
    m_mousePosition = MousePosition(x, y);
    
    // Fire callback if set
    if (m_mouseMoveEventCallback) {
        MouseMoveEvent event;
        event.position = m_mousePosition;
        event.delta = m_mouseDelta;
        
        m_mouseMoveEventCallback(event);
    }
}

void InputSystem::HandleScrollEvent(float deltaX, float deltaY, float x, float y) {
    m_mouseDelta.scrollX = deltaX * m_config.scrollSensitivity;
    m_mouseDelta.scrollY = deltaY * m_config.scrollSensitivity;
    
    // Fire callback if set
    if (m_scrollEventCallback) {
        ScrollEvent event;
        event.deltaX = m_mouseDelta.scrollX;
        event.deltaY = m_mouseDelta.scrollY;
        event.position = MousePosition(x, y);
        
        m_scrollEventCallback(event);
    }
}

// ============================================================================
// Private Methods
// ============================================================================

void InputSystem::UpdateActions() {
    for (auto& [name, action] : m_actions) {
        bool wasActive = action.isActive;
        action.isActive = false;
        action.value = 0.0f;
        
        if (action.type == ActionType::Digital) {
            // Check keys
            for (KeyCode key : action.keys) {
                if (IsKeyHeld(key)) {
                    action.isActive = true;
                    action.value = 1.0f;
                    break;
                }
            }
            
            // Check mouse buttons
            if (!action.isActive) {
                for (MouseButton button : action.mouseButtons) {
                    if (IsMouseButtonHeld(button)) {
                        action.isActive = true;
                        action.value = 1.0f;
                        break;
                    }
                }
            }
        }
        // TODO: Implement analog actions for mouse movement, gamepad sticks, etc.
    }
}

void InputSystem::UpdateMouseDelta() {
    m_mouseDelta.deltaX = (m_mousePosition.x - m_lastMousePosition.x) * m_config.mouseSensitivity;
    m_mouseDelta.deltaY = (m_mousePosition.y - m_lastMousePosition.y) * m_config.mouseSensitivity;
    
    if (m_config.invertMouseY) {
        m_mouseDelta.deltaY = -m_mouseDelta.deltaY;
    }
}

void InputSystem::ResetFrameState() {
    // Reset scroll deltas
    m_mouseDelta.scrollX = 0.0f;
    m_mouseDelta.scrollY = 0.0f;

    // Convert pressed states to held states
    for (size_t i = 0; i < static_cast<size_t>(KeyCode::COUNT); ++i) {
        if (m_keyStates[i] == InputState::Pressed) {
            m_keyStates[i] = InputState::Held;
        }
    }

    for (size_t i = 0; i < static_cast<size_t>(MouseButton::COUNT); ++i) {
        if (m_mouseButtonStates[i] == InputState::Pressed) {
            m_mouseButtonStates[i] = InputState::Held;
        }
    }
}

// ============================================================================
// Utility Functions
// ============================================================================

const char* InputSystem::GetKeyName(KeyCode key) {
    switch (key) {
        case KeyCode::A: return "A";
        case KeyCode::B: return "B";
        case KeyCode::C: return "C";
        case KeyCode::D: return "D";
        case KeyCode::E: return "E";
        case KeyCode::F: return "F";
        case KeyCode::G: return "G";
        case KeyCode::H: return "H";
        case KeyCode::I: return "I";
        case KeyCode::J: return "J";
        case KeyCode::K: return "K";
        case KeyCode::L: return "L";
        case KeyCode::M: return "M";
        case KeyCode::N: return "N";
        case KeyCode::O: return "O";
        case KeyCode::P: return "P";
        case KeyCode::Q: return "Q";
        case KeyCode::R: return "R";
        case KeyCode::S: return "S";
        case KeyCode::T: return "T";
        case KeyCode::U: return "U";
        case KeyCode::V: return "V";
        case KeyCode::W: return "W";
        case KeyCode::X: return "X";
        case KeyCode::Y: return "Y";
        case KeyCode::Z: return "Z";
        case KeyCode::Num0: return "0";
        case KeyCode::Num1: return "1";
        case KeyCode::Num2: return "2";
        case KeyCode::Num3: return "3";
        case KeyCode::Num4: return "4";
        case KeyCode::Num5: return "5";
        case KeyCode::Num6: return "6";
        case KeyCode::Num7: return "7";
        case KeyCode::Num8: return "8";
        case KeyCode::Num9: return "9";
        case KeyCode::Space: return "Space";
        case KeyCode::Enter: return "Enter";
        case KeyCode::Escape: return "Escape";
        case KeyCode::Tab: return "Tab";
        case KeyCode::Shift: return "Shift";
        case KeyCode::Control: return "Control";
        case KeyCode::Alt: return "Alt";
        case KeyCode::Command: return "Command";
        case KeyCode::Up: return "Up";
        case KeyCode::Down: return "Down";
        case KeyCode::Left: return "Left";
        case KeyCode::Right: return "Right";
        default: return "Unknown";
    }
}

const char* InputSystem::GetMouseButtonName(MouseButton button) {
    switch (button) {
        case MouseButton::Left: return "Left";
        case MouseButton::Right: return "Right";
        case MouseButton::Middle: return "Middle";
        case MouseButton::Button4: return "Button4";
        case MouseButton::Button5: return "Button5";
        default: return "Unknown";
    }
}

// ============================================================================
// InputManager Implementation
// ============================================================================

InputManager& InputManager::GetInstance() {
    static InputManager instance;
    return instance;
}

void InputManager::Initialize() {
    if (m_inputSystem) {
        return;
    }

    m_inputSystem = new InputSystem();
    m_inputSystem->Initialize();

    std::cout << "🎮 InputManager initialized" << std::endl;
}

void InputManager::Shutdown() {
    if (m_inputSystem) {
        m_inputSystem->Shutdown();
        delete m_inputSystem;
        m_inputSystem = nullptr;
    }

    std::cout << "🎮 InputManager shutdown" << std::endl;
}

bool InputManager::IsKeyPressed(KeyCode key) const {
    return m_inputSystem ? m_inputSystem->IsKeyPressed(key) : false;
}

bool InputManager::IsKeyHeld(KeyCode key) const {
    return m_inputSystem ? m_inputSystem->IsKeyHeld(key) : false;
}

MousePosition InputManager::GetMousePosition() const {
    return m_inputSystem ? m_inputSystem->GetMousePosition() : MousePosition();
}

MouseDelta InputManager::GetMouseDelta() const {
    return m_inputSystem ? m_inputSystem->GetMouseDelta() : MouseDelta();
}

bool InputManager::IsActionActive(const std::string& name) const {
    return m_inputSystem ? m_inputSystem->IsActionActive(name) : false;
}

float InputManager::GetActionValue(const std::string& name) const {
    return m_inputSystem ? m_inputSystem->GetActionValue(name) : 0.0f;
}

} // namespace Input
} // namespace Engine
