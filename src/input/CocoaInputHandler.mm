//
// CocoaInputHandler.mm
// macOS 3D Looter-Shooter Game Engine
//
// Cocoa-based input handler implementation
//

#import "CocoaInputHandler.h"
#import <Cocoa/Cocoa.h>
#include <iostream>

namespace Engine {
namespace Input {

// ============================================================================
// Key Mapping Implementation
// ============================================================================

bool CocoaKeyMapper::s_initialized = false;
KeyCode CocoaKeyMapper::s_keyMapping[256];

void CocoaKeyMapper::InitializeKeyMapping() {
    if (s_initialized) {
        return;
    }
    
    // Initialize all keys to unknown
    for (int i = 0; i < 256; ++i) {
        s_keyMapping[i] = KeyCode::COUNT; // Invalid key
    }
    
    // Map common keys (using macOS virtual key codes)
    s_keyMapping[0x00] = KeyCode::A;
    s_keyMapping[0x0B] = KeyCode::B;
    s_keyMapping[0x08] = KeyCode::C;
    s_keyMapping[0x02] = KeyCode::D;
    s_keyMapping[0x0E] = KeyCode::E;
    s_keyMapping[0x03] = KeyCode::F;
    s_keyMapping[0x05] = KeyCode::G;
    s_keyMapping[0x04] = KeyCode::H;
    s_keyMapping[0x22] = KeyCode::I;
    s_keyMapping[0x26] = KeyCode::J;
    s_keyMapping[0x28] = KeyCode::K;
    s_keyMapping[0x25] = KeyCode::L;
    s_keyMapping[0x2E] = KeyCode::M;
    s_keyMapping[0x2D] = KeyCode::N;
    s_keyMapping[0x1F] = KeyCode::O;
    s_keyMapping[0x23] = KeyCode::P;
    s_keyMapping[0x0C] = KeyCode::Q;
    s_keyMapping[0x0F] = KeyCode::R;
    s_keyMapping[0x01] = KeyCode::S;
    s_keyMapping[0x11] = KeyCode::T;
    s_keyMapping[0x20] = KeyCode::U;
    s_keyMapping[0x09] = KeyCode::V;
    s_keyMapping[0x0D] = KeyCode::W;
    s_keyMapping[0x07] = KeyCode::X;
    s_keyMapping[0x10] = KeyCode::Y;
    s_keyMapping[0x06] = KeyCode::Z;
    
    // Numbers
    s_keyMapping[0x1D] = KeyCode::Num0;
    s_keyMapping[0x12] = KeyCode::Num1;
    s_keyMapping[0x13] = KeyCode::Num2;
    s_keyMapping[0x14] = KeyCode::Num3;
    s_keyMapping[0x15] = KeyCode::Num4;
    s_keyMapping[0x17] = KeyCode::Num5;
    s_keyMapping[0x16] = KeyCode::Num6;
    s_keyMapping[0x1A] = KeyCode::Num7;
    s_keyMapping[0x1C] = KeyCode::Num8;
    s_keyMapping[0x19] = KeyCode::Num9;
    
    // Special keys
    s_keyMapping[0x31] = KeyCode::Space;
    s_keyMapping[0x24] = KeyCode::Enter;
    s_keyMapping[0x35] = KeyCode::Escape;
    s_keyMapping[0x30] = KeyCode::Tab;
    s_keyMapping[0x33] = KeyCode::Backspace;
    s_keyMapping[0x75] = KeyCode::Delete;
    
    // Modifiers
    s_keyMapping[0x38] = KeyCode::Shift;    // Left Shift
    s_keyMapping[0x3C] = KeyCode::Shift;    // Right Shift
    s_keyMapping[0x3B] = KeyCode::Control;  // Left Control
    s_keyMapping[0x3E] = KeyCode::Control;  // Right Control
    s_keyMapping[0x3A] = KeyCode::Alt;      // Left Alt
    s_keyMapping[0x3D] = KeyCode::Alt;      // Right Alt
    s_keyMapping[0x37] = KeyCode::Command;  // Left Command
    s_keyMapping[0x36] = KeyCode::Command;  // Right Command
    
    // Arrow keys
    s_keyMapping[0x7E] = KeyCode::Up;
    s_keyMapping[0x7D] = KeyCode::Down;
    s_keyMapping[0x7B] = KeyCode::Left;
    s_keyMapping[0x7C] = KeyCode::Right;
    
    // Function keys
    s_keyMapping[0x7A] = KeyCode::F1;
    s_keyMapping[0x78] = KeyCode::F2;
    s_keyMapping[0x63] = KeyCode::F3;
    s_keyMapping[0x76] = KeyCode::F4;
    s_keyMapping[0x60] = KeyCode::F5;
    s_keyMapping[0x61] = KeyCode::F6;
    s_keyMapping[0x62] = KeyCode::F7;
    s_keyMapping[0x64] = KeyCode::F8;
    s_keyMapping[0x65] = KeyCode::F9;
    s_keyMapping[0x6D] = KeyCode::F10;
    s_keyMapping[0x67] = KeyCode::F11;
    s_keyMapping[0x6F] = KeyCode::F12;
    
    s_initialized = true;
}

KeyCode CocoaKeyMapper::MapCocoaKeyCode(unsigned short cocoaKeyCode) {
    InitializeKeyMapping();
    
    if (cocoaKeyCode < 256) {
        KeyCode mapped = s_keyMapping[cocoaKeyCode];
        if (mapped != KeyCode::COUNT) {
            return mapped;
        }
    }
    
    // Return a default key for unmapped codes
    return KeyCode::COUNT; // Invalid
}

MouseButton CocoaKeyMapper::MapCocoaMouseButton(NSInteger buttonNumber) {
    switch (buttonNumber) {
        case 0: return MouseButton::Left;
        case 1: return MouseButton::Right;
        case 2: return MouseButton::Middle;
        case 3: return MouseButton::Button4;
        case 4: return MouseButton::Button5;
        default: return MouseButton::COUNT; // Invalid
    }
}

// ============================================================================
// CocoaInputHandler Implementation
// ============================================================================

CocoaInputHandler::CocoaInputHandler()
    : m_inputSystem(nullptr)
    , m_window(nil)
    , m_view(nil)
    , m_initialized(false)
    , m_mouseCaptured(false)
    , m_cursorVisible(true)
    , m_lastMouseX(0.0f)
    , m_lastMouseY(0.0f)
    , m_firstMouseMove(true) {
}

CocoaInputHandler::~CocoaInputHandler() {
    if (m_initialized) {
        Shutdown();
    }
}

bool CocoaInputHandler::Initialize(InputSystem* inputSystem) {
    if (m_initialized || !inputSystem) {
        return false;
    }
    
    m_inputSystem = inputSystem;
    m_initialized = true;
    
    std::cout << "🎮 CocoaInputHandler initialized" << std::endl;
    return true;
}

void CocoaInputHandler::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    // Restore cursor if it was hidden
    if (!m_cursorVisible) {
        ShowCursor(true);
    }
    
    // Release mouse capture
    if (m_mouseCaptured) {
        CaptureMouse(false);
    }
    
    m_inputSystem = nullptr;
    m_window = nil;
    m_view = nil;
    m_initialized = false;
    
    std::cout << "🎮 CocoaInputHandler shutdown" << std::endl;
}

void CocoaInputHandler::HandleKeyDown(unsigned short keyCode, bool isRepeat) {
    if (!m_inputSystem) {
        return;
    }
    
    KeyCode engineKey = CocoaKeyMapper::MapCocoaKeyCode(keyCode);
    if (engineKey != KeyCode::COUNT) {
        m_inputSystem->HandleKeyEvent(engineKey, InputState::Pressed, isRepeat);
    }
}

void CocoaInputHandler::HandleKeyUp(unsigned short keyCode) {
    if (!m_inputSystem) {
        return;
    }
    
    KeyCode engineKey = CocoaKeyMapper::MapCocoaKeyCode(keyCode);
    if (engineKey != KeyCode::COUNT) {
        m_inputSystem->HandleKeyEvent(engineKey, InputState::Released, false);
    }
}

void CocoaInputHandler::HandleMouseDown(NSInteger buttonNumber, float x, float y) {
    if (!m_inputSystem) {
        return;
    }
    
    ConvertScreenToViewCoordinates(x, y);
    
    MouseButton engineButton = CocoaKeyMapper::MapCocoaMouseButton(buttonNumber);
    if (engineButton != MouseButton::COUNT) {
        m_inputSystem->HandleMouseButtonEvent(engineButton, InputState::Pressed, x, y);
    }
}

void CocoaInputHandler::HandleMouseUp(NSInteger buttonNumber, float x, float y) {
    if (!m_inputSystem) {
        return;
    }
    
    ConvertScreenToViewCoordinates(x, y);
    
    MouseButton engineButton = CocoaKeyMapper::MapCocoaMouseButton(buttonNumber);
    if (engineButton != MouseButton::COUNT) {
        m_inputSystem->HandleMouseButtonEvent(engineButton, InputState::Released, x, y);
    }
}

void CocoaInputHandler::HandleMouseMoved(float x, float y) {
    if (!m_inputSystem) {
        return;
    }
    
    ConvertScreenToViewCoordinates(x, y);
    UpdateMousePosition(x, y);
    
    m_inputSystem->HandleMouseMoveEvent(x, y);
}

void CocoaInputHandler::HandleScrollWheel(float deltaX, float deltaY, float x, float y) {
    if (!m_inputSystem) {
        return;
    }
    
    ConvertScreenToViewCoordinates(x, y);
    m_inputSystem->HandleScrollEvent(deltaX, deltaY, x, y);
}

void CocoaInputHandler::SetWindow(id window) {
    m_window = window;
}

void CocoaInputHandler::SetView(id view) {
    m_view = view;
}

void CocoaInputHandler::CaptureMouse(bool capture) {
    m_mouseCaptured = capture;
    
    if (capture) {
        // Hide cursor and associate it with the window
        [NSCursor hide];
        CGAssociateMouseAndMouseCursorPosition(false);
    } else {
        // Show cursor and restore normal behavior
        [NSCursor unhide];
        CGAssociateMouseAndMouseCursorPosition(true);
    }
}

void CocoaInputHandler::ShowCursor(bool show) {
    m_cursorVisible = show;
    
    if (show) {
        [NSCursor unhide];
    } else {
        [NSCursor hide];
    }
}

void CocoaInputHandler::SetCursorPosition(float x, float y) {
    if (m_view) {
        NSView* view = (__bridge NSView*)m_view;
        NSPoint point = NSMakePoint(x, y);
        NSPoint screenPoint = [view convertPoint:point toView:nil];
        NSPoint globalPoint = [[view window] convertPointToScreen:screenPoint];
        
        CGWarpMouseCursorPosition(CGPointMake(globalPoint.x, globalPoint.y));
    }
}

void CocoaInputHandler::UpdateMousePosition(float x, float y) {
    if (m_firstMouseMove) {
        m_lastMouseX = x;
        m_lastMouseY = y;
        m_firstMouseMove = false;
    }
    
    m_lastMouseX = x;
    m_lastMouseY = y;
}

void CocoaInputHandler::ConvertScreenToViewCoordinates(float& x, float& y) {
    // For now, assume coordinates are already in view space
    // In a real implementation, you'd convert from screen to view coordinates
    // This would depend on your window/view setup
}

} // namespace Input
} // namespace Engine

// ============================================================================
// Objective-C View Implementation
// ============================================================================

@implementation GameInputView

@synthesize inputHandler;

- (void)setInputHandler:(Engine::Input::CocoaInputHandler*)handler {
    inputHandler = handler;
}

- (BOOL)acceptsFirstResponder {
    return YES;
}

- (void)keyDown:(NSEvent *)event {
    if (inputHandler) {
        inputHandler->HandleKeyDown([event keyCode], [event isARepeat]);
    }
}

- (void)keyUp:(NSEvent *)event {
    if (inputHandler) {
        inputHandler->HandleKeyUp([event keyCode]);
    }
}

- (void)mouseDown:(NSEvent *)event {
    if (inputHandler) {
        NSPoint location = [event locationInWindow];
        inputHandler->HandleMouseDown(0, location.x, location.y);
    }
}

- (void)mouseUp:(NSEvent *)event {
    if (inputHandler) {
        NSPoint location = [event locationInWindow];
        inputHandler->HandleMouseUp(0, location.x, location.y);
    }
}

- (void)rightMouseDown:(NSEvent *)event {
    if (inputHandler) {
        NSPoint location = [event locationInWindow];
        inputHandler->HandleMouseDown(1, location.x, location.y);
    }
}

- (void)rightMouseUp:(NSEvent *)event {
    if (inputHandler) {
        NSPoint location = [event locationInWindow];
        inputHandler->HandleMouseUp(1, location.x, location.y);
    }
}

- (void)otherMouseDown:(NSEvent *)event {
    if (inputHandler) {
        NSPoint location = [event locationInWindow];
        inputHandler->HandleMouseDown([event buttonNumber], location.x, location.y);
    }
}

- (void)otherMouseUp:(NSEvent *)event {
    if (inputHandler) {
        NSPoint location = [event locationInWindow];
        inputHandler->HandleMouseUp([event buttonNumber], location.x, location.y);
    }
}

- (void)mouseMoved:(NSEvent *)event {
    if (inputHandler) {
        NSPoint location = [event locationInWindow];
        inputHandler->HandleMouseMoved(location.x, location.y);
    }
}

- (void)mouseDragged:(NSEvent *)event {
    [self mouseMoved:event];
}

- (void)rightMouseDragged:(NSEvent *)event {
    [self mouseMoved:event];
}

- (void)otherMouseDragged:(NSEvent *)event {
    [self mouseMoved:event];
}

- (void)scrollWheel:(NSEvent *)event {
    if (inputHandler) {
        NSPoint location = [event locationInWindow];
        inputHandler->HandleScrollWheel([event deltaX], [event deltaY], location.x, location.y);
    }
}

@end

// ============================================================================
// Objective-C Window Implementation
// ============================================================================

@implementation GameInputWindow

@synthesize inputHandler;

- (void)setInputHandler:(Engine::Input::CocoaInputHandler*)handler {
    inputHandler = handler;
}

- (BOOL)canBecomeKeyWindow {
    return YES;
}

- (BOOL)canBecomeMainWindow {
    return YES;
}

@end

// ============================================================================
// C Interface Implementation
// ============================================================================

extern "C" {

void* CreateCocoaInputHandler() {
    return new Engine::Input::CocoaInputHandler();
}

void DestroyCocoaInputHandler(void* handler) {
    if (handler) {
        delete static_cast<Engine::Input::CocoaInputHandler*>(handler);
    }
}

bool InitializeCocoaInputHandler(void* handler, void* inputSystem) {
    if (!handler || !inputSystem) {
        return false;
    }

    auto* cocoaHandler = static_cast<Engine::Input::CocoaInputHandler*>(handler);
    auto* inputSys = static_cast<Engine::Input::InputSystem*>(inputSystem);

    return cocoaHandler->Initialize(inputSys);
}

void ShutdownCocoaInputHandler(void* handler) {
    if (handler) {
        static_cast<Engine::Input::CocoaInputHandler*>(handler)->Shutdown();
    }
}

void SetCocoaInputWindow(void* handler, void* window) {
    if (handler) {
        static_cast<Engine::Input::CocoaInputHandler*>(handler)->SetWindow((__bridge id)window);
    }
}

void SetCocoaInputView(void* handler, void* view) {
    if (handler) {
        static_cast<Engine::Input::CocoaInputHandler*>(handler)->SetView((__bridge id)view);
    }
}

void CaptureCocoaMouse(void* handler, bool capture) {
    if (handler) {
        static_cast<Engine::Input::CocoaInputHandler*>(handler)->CaptureMouse(capture);
    }
}

void ShowCocoaCursor(void* handler, bool show) {
    if (handler) {
        static_cast<Engine::Input::CocoaInputHandler*>(handler)->ShowCursor(show);
    }
}

void SetCocoaCursorPosition(void* handler, float x, float y) {
    if (handler) {
        static_cast<Engine::Input::CocoaInputHandler*>(handler)->SetCursorPosition(x, y);
    }
}

void InjectCocoaKeyEvent(void* handler, unsigned short keyCode, bool isDown, bool isRepeat) {
    if (handler) {
        auto* cocoaHandler = static_cast<Engine::Input::CocoaInputHandler*>(handler);
        if (isDown) {
            cocoaHandler->HandleKeyDown(keyCode, isRepeat);
        } else {
            cocoaHandler->HandleKeyUp(keyCode);
        }
    }
}

void InjectCocoaMouseEvent(void* handler, int buttonNumber, bool isDown, float x, float y) {
    if (handler) {
        auto* cocoaHandler = static_cast<Engine::Input::CocoaInputHandler*>(handler);
        if (isDown) {
            cocoaHandler->HandleMouseDown(buttonNumber, x, y);
        } else {
            cocoaHandler->HandleMouseUp(buttonNumber, x, y);
        }
    }
}

void InjectCocoaMouseMove(void* handler, float x, float y) {
    if (handler) {
        static_cast<Engine::Input::CocoaInputHandler*>(handler)->HandleMouseMoved(x, y);
    }
}

void InjectCocoaScrollEvent(void* handler, float deltaX, float deltaY, float x, float y) {
    if (handler) {
        static_cast<Engine::Input::CocoaInputHandler*>(handler)->HandleScrollWheel(deltaX, deltaY, x, y);
    }
}

} // extern "C"
