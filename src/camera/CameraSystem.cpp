//
// CameraSystem.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Enhanced camera system implementation
//

#include "CameraSystem.h"
#include "engine/Engine.h"
#include "engine/ECS.h"
#include <iostream>
#include <algorithm>
#include <cmath>

namespace Engine {
namespace Camera {

// ============================================================================
// CameraComponent Helper Methods
// ============================================================================

Vector3 CameraComponent::GetForward() const {
    Vector3 forward;
    float yawRad = yaw * M_PI / 180.0f;
    float pitchRad = pitch * M_PI / 180.0f;
    
    forward.x = cos(pitchRad) * cos(yawRad);
    forward.y = sin(pitchRad);
    forward.z = cos(pitchRad) * sin(yawRad);
    
    forward.Normalize();
    return forward;
}

Vector3 CameraComponent::GetRight() const {
    Vector3 forward = GetForward();
    Vector3 worldUp = {0.0f, 1.0f, 0.0f};
    Vector3 right = forward.Cross(worldUp);
    right.Normalize();
    return right;
}

Vector3 CameraComponent::GetUp() const {
    Vector3 forward = GetForward();
    Vector3 right = GetRight();
    Vector3 up = right.Cross(forward);
    up.Normalize();
    return up;
}

void CameraComponent::UpdateFromEulerAngles() {
    Vector3 forward = GetForward();
    target = position + forward;
    up = GetUp();
}

void CameraComponent::UpdateEulerAnglesFromDirection() {
    Vector3 direction = target - position;
    direction.Normalize();
    
    yaw = atan2(direction.z, direction.x) * 180.0f / M_PI;
    pitch = asin(direction.y) * 180.0f / M_PI;
}

// ============================================================================
// CameraSystem Implementation
// ============================================================================

CameraSystem::CameraSystem()
    : m_activeCamera()  // Default constructor creates invalid entity
    , m_inputSystem(nullptr)
    , m_inputActionsRegistered(false)
    , m_initialized(false) {
}

CameraSystem::~CameraSystem() {
    if (m_initialized) {
        Shutdown();
    }
}

void CameraSystem::Initialize() {
    if (m_initialized) {
        return;
    }
    
    std::cout << "📷 Initializing CameraSystem..." << std::endl;
    
    // Register camera components with ECS
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    ecs.RegisterComponent<CameraComponent>();
    ecs.RegisterComponent<CameraController>();
    
    m_initialized = true;
    std::cout << "✅ CameraSystem initialized" << std::endl;
}

void CameraSystem::Update(DeltaTime deltaTime) {
    if (!m_initialized) {
        return;
    }
    
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    
    // Update all cameras with controllers
    for (Entity entity : m_entities) {
        if (!ecs.HasComponent<CameraController>(entity)) {
            continue;
        }
        
        auto& camera = ecs.GetComponent<CameraComponent>(entity);
        auto& controller = ecs.GetComponent<CameraController>(entity);
        
        // Process input if this camera has input enabled
        if (camera.inputEnabled && m_inputSystem) {
            ProcessInput(entity, controller);
        }
        
        // Update camera transition
        UpdateCameraTransition(camera, controller, deltaTime);
        
        // Update camera based on mode
        switch (camera.mode) {
            case CameraMode::FirstPerson:
                UpdateFirstPersonCamera(entity, camera, controller, deltaTime);
                break;
            case CameraMode::ThirdPerson:
                UpdateThirdPersonCamera(entity, camera, controller, deltaTime);
                break;
            case CameraMode::Orbital:
                UpdateOrbitalCamera(entity, camera, controller, deltaTime);
                break;
            case CameraMode::Fixed:
                UpdateFixedCamera(entity, camera, controller, deltaTime);
                break;
        }
        
        // Apply constraints
        ApplyConstraints(camera, controller.config);
        
        // Apply smoothing
        SmoothCameraMovement(camera, deltaTime);
        
        // Update camera vectors from Euler angles
        camera.UpdateFromEulerAngles();
    }
}

void CameraSystem::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "📷 Shutting down CameraSystem..." << std::endl;
    
    m_activeCamera = Entity();  // Invalid entity
    m_inputSystem = nullptr;
    m_inputActionsRegistered = false;
    
    m_initialized = false;
    std::cout << "✅ CameraSystem shutdown complete" << std::endl;
}

// ============================================================================
// Camera Management
// ============================================================================

Entity CameraSystem::CreateCamera(const Vector3& position, const Vector3& target, CameraMode mode) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    
    Entity camera = ecs.CreateEntity();
    ecs.AddComponent(camera, Name("Camera"));
    ecs.AddComponent(camera, Transform(position));
    
    CameraComponent cameraComp(position, target);
    cameraComp.mode = mode;
    cameraComp.UpdateEulerAnglesFromDirection();
    ecs.AddComponent(camera, cameraComp);
    
    CameraController controller;
    ecs.AddComponent(camera, controller);
    
    std::cout << "📷 Created camera entity " << camera.GetID() << " in " 
              << (mode == CameraMode::FirstPerson ? "first-person" :
                  mode == CameraMode::ThirdPerson ? "third-person" :
                  mode == CameraMode::Orbital ? "orbital" : "fixed") << " mode" << std::endl;
    
    return camera;
}

void CameraSystem::SetActiveCamera(Entity cameraEntity) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    
    // Deactivate previous camera
    if (m_activeCamera.IsValid() && ecs.HasComponent<CameraComponent>(m_activeCamera)) {
        auto& prevCamera = ecs.GetComponent<CameraComponent>(m_activeCamera);
        prevCamera.isActive = false;
    }

    // Activate new camera
    m_activeCamera = cameraEntity;
    if (cameraEntity.IsValid() && ecs.HasComponent<CameraComponent>(cameraEntity)) {
        auto& camera = ecs.GetComponent<CameraComponent>(cameraEntity);
        camera.isActive = true;
        
        if (ecs.HasComponent<Name>(cameraEntity)) {
            auto& name = ecs.GetComponent<Name>(cameraEntity);
            std::cout << "📷 Active camera set to: " << name.name << " (Entity " << cameraEntity.GetID() << ")" << std::endl;
        } else {
            std::cout << "📷 Active camera set to Entity " << cameraEntity.GetID() << std::endl;
        }
    }
}

void CameraSystem::SetCameraMode(Entity cameraEntity, CameraMode mode) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    if (!ecs.HasComponent<CameraComponent>(cameraEntity)) {
        return;
    }
    
    auto& camera = ecs.GetComponent<CameraComponent>(cameraEntity);
    camera.mode = mode;
    
    std::cout << "📷 Camera " << cameraEntity.GetID() << " mode changed to " 
              << (mode == CameraMode::FirstPerson ? "first-person" :
                  mode == CameraMode::ThirdPerson ? "third-person" :
                  mode == CameraMode::Orbital ? "orbital" : "fixed") << std::endl;
}

void CameraSystem::SetCameraTarget(Entity cameraEntity, Entity targetEntity) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    if (!ecs.HasComponent<CameraComponent>(cameraEntity)) {
        return;
    }
    
    auto& camera = ecs.GetComponent<CameraComponent>(cameraEntity);
    camera.targetEntity = targetEntity;
    
    std::cout << "📷 Camera " << cameraEntity.GetID() << " target set to Entity " << targetEntity.GetID() << std::endl;
}

void CameraSystem::SetCameraPosition(Entity cameraEntity, const Vector3& position) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    if (!ecs.HasComponent<CameraComponent>(cameraEntity)) {
        return;
    }
    
    auto& camera = ecs.GetComponent<CameraComponent>(cameraEntity);
    camera.position = position;
    camera.targetPosition = position;
}

void CameraSystem::SetCameraRotation(Entity cameraEntity, float yaw, float pitch, float roll) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    if (!ecs.HasComponent<CameraComponent>(cameraEntity)) {
        return;
    }
    
    auto& camera = ecs.GetComponent<CameraComponent>(cameraEntity);
    camera.yaw = yaw;
    camera.pitch = pitch;
    camera.roll = roll;
    camera.UpdateFromEulerAngles();
}

// ============================================================================
// Input Integration
// ============================================================================

void CameraSystem::SetInputSystem(Input::InputSystem* inputSystem) {
    m_inputSystem = inputSystem;
    
    if (m_inputSystem && !m_inputActionsRegistered) {
        // Register camera input actions
        Input::InputAction moveForward;
        moveForward.name = "CameraMoveForward";
        moveForward.type = Input::ActionType::Digital;
        moveForward.keys = {Input::KeyCode::W};
        m_inputSystem->RegisterAction(moveForward);
        
        Input::InputAction moveBackward;
        moveBackward.name = "CameraMoveBackward";
        moveBackward.type = Input::ActionType::Digital;
        moveBackward.keys = {Input::KeyCode::S};
        m_inputSystem->RegisterAction(moveBackward);
        
        Input::InputAction moveLeft;
        moveLeft.name = "CameraMoveLeft";
        moveLeft.type = Input::ActionType::Digital;
        moveLeft.keys = {Input::KeyCode::A};
        m_inputSystem->RegisterAction(moveLeft);
        
        Input::InputAction moveRight;
        moveRight.name = "CameraMoveRight";
        moveRight.type = Input::ActionType::Digital;
        moveRight.keys = {Input::KeyCode::D};
        m_inputSystem->RegisterAction(moveRight);
        
        Input::InputAction moveUp;
        moveUp.name = "CameraMoveUp";
        moveUp.type = Input::ActionType::Digital;
        moveUp.keys = {Input::KeyCode::Space};
        m_inputSystem->RegisterAction(moveUp);
        
        Input::InputAction moveDown;
        moveDown.name = "CameraMoveDown";
        moveDown.type = Input::ActionType::Digital;
        moveDown.keys = {Input::KeyCode::Shift};
        m_inputSystem->RegisterAction(moveDown);
        
        Input::InputAction sprint;
        sprint.name = "CameraSprint";
        sprint.type = Input::ActionType::Digital;
        sprint.keys = {Input::KeyCode::Shift};
        m_inputSystem->RegisterAction(sprint);
        
        m_inputActionsRegistered = true;
        std::cout << "📷 Camera input actions registered" << std::endl;
    }
}

void CameraSystem::EnableCameraInput(Entity cameraEntity, bool enabled) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();
    if (!ecs.HasComponent<CameraComponent>(cameraEntity)) {
        return;
    }
    
    auto& camera = ecs.GetComponent<CameraComponent>(cameraEntity);
    camera.inputEnabled = enabled;
}

void CameraSystem::ProcessInput(Entity entity, CameraController& controller) {
    if (!m_inputSystem) {
        return;
    }
    
    // Update movement input
    controller.moveForward = m_inputSystem->IsActionActive("CameraMoveForward");
    controller.moveBackward = m_inputSystem->IsActionActive("CameraMoveBackward");
    controller.moveLeft = m_inputSystem->IsActionActive("CameraMoveLeft");
    controller.moveRight = m_inputSystem->IsActionActive("CameraMoveRight");
    controller.moveUp = m_inputSystem->IsActionActive("CameraMoveUp");
    controller.moveDown = m_inputSystem->IsActionActive("CameraMoveDown");
    controller.sprint = m_inputSystem->IsActionActive("CameraSprint");
    
    // Update mouse input
    Input::MouseDelta mouseDelta = m_inputSystem->GetMouseDelta();
    controller.mouseDelta.x = mouseDelta.deltaX * controller.config.mouseSensitivity;
    controller.mouseDelta.y = mouseDelta.deltaY * controller.config.mouseSensitivity;
    
    if (controller.config.invertMouseY) {
        controller.mouseDelta.y = -controller.mouseDelta.y;
    }
    
    // Update scroll input
    controller.scrollDelta = mouseDelta.scrollY * controller.config.scrollSensitivity;
}

// ============================================================================
// Camera Update Methods
// ============================================================================

void CameraSystem::UpdateFirstPersonCamera(Entity entity, CameraComponent& camera, CameraController& controller, DeltaTime deltaTime) {
    // Update rotation from mouse input
    camera.yaw += controller.mouseDelta.x;
    camera.pitch -= controller.mouseDelta.y;

    // Calculate movement
    Vector3 forward = camera.GetForward();
    Vector3 right = camera.GetRight();
    Vector3 up = {0.0f, 1.0f, 0.0f};

    Vector3 movement = {0.0f, 0.0f, 0.0f};

    if (controller.moveForward) movement = movement + forward;
    if (controller.moveBackward) movement = movement - forward;
    if (controller.moveRight) movement = movement + right;
    if (controller.moveLeft) movement = movement - right;
    if (controller.moveUp) movement = movement + up;
    if (controller.moveDown) movement = movement - up;

    // Normalize movement vector
    if (movement.Length() > 0.0f) {
        movement.Normalize();

        float speed = controller.config.moveSpeed;
        if (controller.sprint) {
            speed *= controller.config.sprintMultiplier;
        }

        movement = movement * speed * deltaTime;
        camera.targetPosition = camera.position + movement;
    }

    // Handle FOV zoom with scroll
    if (controller.config.enableFovZoom && controller.scrollDelta != 0.0f) {
        camera.fov -= controller.scrollDelta * 5.0f;
        camera.fov = std::clamp(camera.fov, controller.config.minFov, controller.config.maxFov);
    }
}

void CameraSystem::UpdateThirdPersonCamera(Entity entity, CameraComponent& camera, CameraController& controller, DeltaTime deltaTime) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();

    // Update rotation from mouse input
    camera.yaw += controller.mouseDelta.x;
    camera.pitch -= controller.mouseDelta.y;

    // Handle distance adjustment with scroll
    if (controller.scrollDelta != 0.0f) {
        controller.config.thirdPersonDistance -= controller.scrollDelta;
        controller.config.thirdPersonDistance = std::clamp(
            controller.config.thirdPersonDistance,
            controller.config.thirdPersonMinDistance,
            controller.config.thirdPersonMaxDistance
        );
    }

    // Calculate camera position based on target entity
    Vector3 targetPos = {0.0f, 0.0f, 0.0f};
    if (camera.targetEntity.IsValid() && ecs.HasComponent<Transform>(camera.targetEntity)) {
        auto& targetTransform = ecs.GetComponent<Transform>(camera.targetEntity);
        targetPos = targetTransform.position;
        targetPos.y += controller.config.thirdPersonHeight;
    }

    // Calculate camera position
    float yawRad = camera.yaw * M_PI / 180.0f;
    float pitchRad = camera.pitch * M_PI / 180.0f;

    Vector3 offset;
    offset.x = controller.config.thirdPersonDistance * cos(pitchRad) * cos(yawRad);
    offset.y = controller.config.thirdPersonDistance * sin(pitchRad);
    offset.z = controller.config.thirdPersonDistance * cos(pitchRad) * sin(yawRad);

    camera.targetPosition = targetPos - offset;
    camera.target = targetPos;
}

void CameraSystem::UpdateOrbitalCamera(Entity entity, CameraComponent& camera, CameraController& controller, DeltaTime deltaTime) {
    auto& ecs = Engine::Engine::GetInstance()->GetECS();

    // Update rotation from mouse input
    camera.yaw += controller.mouseDelta.x * controller.config.orbitalSpeed;
    camera.pitch -= controller.mouseDelta.y * controller.config.orbitalSpeed;

    // Handle radius adjustment with scroll
    if (controller.scrollDelta != 0.0f) {
        controller.config.orbitalRadius -= controller.scrollDelta;
        controller.config.orbitalRadius = std::clamp(
            controller.config.orbitalRadius,
            controller.config.orbitalMinRadius,
            controller.config.orbitalMaxRadius
        );
    }

    // Calculate orbital position
    Vector3 center = {0.0f, 0.0f, 0.0f};
    if (camera.targetEntity.IsValid() && ecs.HasComponent<Transform>(camera.targetEntity)) {
        auto& targetTransform = ecs.GetComponent<Transform>(camera.targetEntity);
        center = targetTransform.position;
    }

    float yawRad = camera.yaw * M_PI / 180.0f;
    float pitchRad = camera.pitch * M_PI / 180.0f;

    Vector3 offset;
    offset.x = controller.config.orbitalRadius * cos(pitchRad) * cos(yawRad);
    offset.y = controller.config.orbitalRadius * sin(pitchRad);
    offset.z = controller.config.orbitalRadius * cos(pitchRad) * sin(yawRad);

    camera.targetPosition = center + offset;
    camera.target = center;
}

void CameraSystem::UpdateFixedCamera(Entity entity, CameraComponent& camera, CameraController& controller, DeltaTime deltaTime) {
    // Fixed cameras don't move, but can still rotate if input is enabled
    if (camera.inputEnabled) {
        camera.yaw += controller.mouseDelta.x * 0.5f;
        camera.pitch -= controller.mouseDelta.y * 0.5f;
    }
}

void CameraSystem::UpdateCameraTransition(CameraComponent& camera, CameraController& controller, DeltaTime deltaTime) {
    if (controller.currentTransition == CameraTransition::Instant) {
        return;
    }

    controller.transitionTime += deltaTime;
    float t = controller.transitionTime / controller.transitionDuration;

    if (t >= 1.0f) {
        // Transition complete
        t = 1.0f;
        controller.currentTransition = CameraTransition::Instant;
    }

    // Apply easing based on transition type
    float easedT = t;
    switch (controller.currentTransition) {
        case CameraTransition::Linear:
            easedT = t;
            break;
        case CameraTransition::Smooth:
            easedT = t * t * (3.0f - 2.0f * t);
            break;
        case CameraTransition::EaseInOut:
            easedT = EaseInOut(t);
            break;
        default:
            break;
    }

    // Interpolate position
    camera.position.x = controller.transitionStartPos.x + (controller.transitionEndPos.x - controller.transitionStartPos.x) * easedT;
    camera.position.y = controller.transitionStartPos.y + (controller.transitionEndPos.y - controller.transitionStartPos.y) * easedT;
    camera.position.z = controller.transitionStartPos.z + (controller.transitionEndPos.z - controller.transitionStartPos.z) * easedT;

    // Interpolate rotation
    camera.yaw = LerpAngle(controller.transitionStartYaw, controller.transitionEndYaw, easedT);
    camera.pitch = LerpAngle(controller.transitionStartPitch, controller.transitionEndPitch, easedT);
}

void CameraSystem::ApplyConstraints(CameraComponent& camera, const CameraConfig& config) {
    // Constrain pitch
    if (config.constrainPitch) {
        camera.pitch = std::clamp(camera.pitch, config.minPitch, config.maxPitch);
    }

    // Normalize yaw to [-180, 180]
    while (camera.yaw > 180.0f) camera.yaw -= 360.0f;
    while (camera.yaw < -180.0f) camera.yaw += 360.0f;

    // Constrain FOV
    camera.fov = std::clamp(camera.fov, config.minFov, config.maxFov);

    // Check collision if enabled
    if (config.enableCollision) {
        camera.targetPosition = ResolveCameraCollision(camera.targetPosition, camera.position, config.collisionRadius);
    }
}

void CameraSystem::SmoothCameraMovement(CameraComponent& camera, DeltaTime deltaTime) {
    // Smooth position movement
    camera.position = SmoothDamp(camera.position, camera.targetPosition, camera.velocity, 0.1f, deltaTime);
}

// ============================================================================
// Utility Methods
// ============================================================================

float CameraSystem::LerpAngle(float from, float to, float t) {
    float delta = to - from;
    while (delta > 180.0f) delta -= 360.0f;
    while (delta < -180.0f) delta += 360.0f;
    return from + delta * t;
}

Vector3 CameraSystem::SmoothDamp(const Vector3& current, const Vector3& target, Vector3& velocity, float smoothTime, DeltaTime deltaTime) {
    float omega = 2.0f / smoothTime;
    float x = omega * deltaTime;
    float exp = 1.0f / (1.0f + x + 0.48f * x * x + 0.235f * x * x * x);

    Vector3 change = current - target;
    Vector3 originalTo = target;

    Vector3 temp = (velocity + omega * change) * deltaTime;
    velocity = (velocity - omega * temp) * exp;
    Vector3 result = target + (change + temp) * exp;

    return result;
}

float CameraSystem::EaseInOut(float t) {
    return t * t * t * (t * (6.0f * t - 15.0f) + 10.0f);
}

bool CameraSystem::CheckCameraCollision(const Vector3& position, float radius) {
    // TODO: Implement collision detection with world geometry
    return false;
}

Vector3 CameraSystem::ResolveCameraCollision(const Vector3& desiredPosition, const Vector3& currentPosition, float radius) {
    // TODO: Implement collision resolution
    return desiredPosition;
}

// ============================================================================
// Camera Manager Implementation
// ============================================================================

CameraManager& CameraManager::GetInstance() {
    static CameraManager instance;
    return instance;
}

void CameraManager::Initialize() {
    if (m_cameraSystem) {
        return;
    }

    m_cameraSystem = new CameraSystem();
    m_cameraSystem->Initialize();

    std::cout << "📷 CameraManager initialized" << std::endl;
}

void CameraManager::Shutdown() {
    if (m_cameraSystem) {
        m_cameraSystem->Shutdown();
        delete m_cameraSystem;
        m_cameraSystem = nullptr;
    }

    std::cout << "📷 CameraManager shutdown" << std::endl;
}

Entity CameraManager::CreateCamera(const Vector3& position, const Vector3& target, CameraMode mode) {
    return m_cameraSystem ? m_cameraSystem->CreateCamera(position, target, mode) : Entity();
}

void CameraManager::SetActiveCamera(Entity cameraEntity) {
    if (m_cameraSystem) {
        m_cameraSystem->SetActiveCamera(cameraEntity);
    }
}

Entity CameraManager::GetActiveCamera() const {
    return m_cameraSystem ? m_cameraSystem->GetActiveCamera() : Entity();
}

} // namespace Camera
} // namespace Engine
