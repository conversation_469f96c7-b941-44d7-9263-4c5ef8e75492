//
// CameraSystem.h
// macOS 3D Looter-Shooter Game Engine
//
// Enhanced camera system with input integration and multiple camera modes
//

#pragma once

#include "engine/System.h"
#include "engine/Types.h"
#include "engine/Components.h"
#include "input/InputSystem.h"
#include <memory>
#include <unordered_map>

namespace Engine {
namespace Camera {

// ============================================================================
// Camera Types and Enums
// ============================================================================

enum class CameraMode {
    FirstPerson,
    ThirdPerson,
    Orbital,
    Fixed
};

enum class CameraTransition {
    Instant,
    Linear,
    Smooth,
    EaseInOut
};

// ============================================================================
// Camera Configuration
// ============================================================================

struct CameraConfig {
    // Movement settings
    float moveSpeed = 5.0f;
    float sprintMultiplier = 2.0f;
    float mouseSensitivity = 2.0f;
    float scrollSensitivity = 1.0f;
    
    // Mouse settings
    bool invertMouseY = false;
    bool smoothMouseMovement = true;
    float mouseSmoothingFactor = 0.1f;
    
    // Third-person settings
    float thirdPersonDistance = 5.0f;
    float thirdPersonHeight = 2.0f;
    float thirdPersonMinDistance = 1.0f;
    float thirdPersonMaxDistance = 20.0f;
    
    // Orbital camera settings
    float orbitalRadius = 10.0f;
    float orbitalMinRadius = 2.0f;
    float orbitalMaxRadius = 50.0f;
    float orbitalSpeed = 1.0f;
    
    // Constraints
    float minPitch = -89.0f;
    float maxPitch = 89.0f;
    bool constrainPitch = true;
    
    // Collision
    bool enableCollision = true;
    float collisionRadius = 0.5f;
    
    // Smoothing
    float positionSmoothingFactor = 0.1f;
    float rotationSmoothingFactor = 0.1f;
    
    // Field of view
    float fov = 60.0f;
    float minFov = 10.0f;
    float maxFov = 120.0f;
    bool enableFovZoom = true;
};

// ============================================================================
// Camera Component
// ============================================================================

struct CameraComponent {
    // Basic camera properties
    Vector3 position = {0.0f, 0.0f, 5.0f};
    Vector3 target = {0.0f, 0.0f, 0.0f};
    Vector3 up = {0.0f, 1.0f, 0.0f};
    
    // Rotation (Euler angles in degrees)
    float yaw = 0.0f;      // Y-axis rotation
    float pitch = 0.0f;    // X-axis rotation
    float roll = 0.0f;     // Z-axis rotation
    
    // Camera settings
    float fov = 60.0f;
    float nearPlane = 0.1f;
    float farPlane = 1000.0f;
    bool isPerspective = true;
    
    // Camera mode and behavior
    CameraMode mode = CameraMode::FirstPerson;
    Entity targetEntity;  // For third-person and orbital modes (defaults to invalid)
    
    // State
    bool isActive = false;
    bool inputEnabled = true;
    
    // Smoothing state
    Vector3 targetPosition = {0.0f, 0.0f, 5.0f};
    Vector3 targetDirection = {0.0f, 0.0f, -1.0f};
    Vector3 velocity = {0.0f, 0.0f, 0.0f};
    
    CameraComponent() = default;
    CameraComponent(const Vector3& pos, const Vector3& tgt) : position(pos), target(tgt) {}
    
    // Helper methods
    Vector3 GetForward() const;
    Vector3 GetRight() const;
    Vector3 GetUp() const;
    void UpdateFromEulerAngles();
    void UpdateEulerAnglesFromDirection();
};

// ============================================================================
// Camera Controller Component
// ============================================================================

struct CameraController {
    CameraConfig config;
    
    // Input state
    bool moveForward = false;
    bool moveBackward = false;
    bool moveLeft = false;
    bool moveRight = false;
    bool moveUp = false;
    bool moveDown = false;
    bool sprint = false;
    
    // Mouse state
    Vector2 mouseDelta = {0.0f, 0.0f};
    float scrollDelta = 0.0f;
    
    // Transition state
    CameraTransition currentTransition = CameraTransition::Instant;
    float transitionTime = 0.0f;
    float transitionDuration = 1.0f;
    Vector3 transitionStartPos = {0.0f, 0.0f, 0.0f};
    Vector3 transitionEndPos = {0.0f, 0.0f, 0.0f};
    float transitionStartYaw = 0.0f;
    float transitionEndYaw = 0.0f;
    float transitionStartPitch = 0.0f;
    float transitionEndPitch = 0.0f;
    
    CameraController() = default;
    CameraController(const CameraConfig& cfg) : config(cfg) {}
};

// ============================================================================
// Camera System
// ============================================================================

class CameraSystem : public System {
public:
    CameraSystem();
    ~CameraSystem();
    
    // System interface
    void Initialize() override;
    void Update(DeltaTime deltaTime) override;
    void Shutdown() override;
    const char* GetName() const override { return "CameraSystem"; }
    
    // Camera management
    Entity CreateCamera(const Vector3& position = {0.0f, 0.0f, 5.0f}, 
                       const Vector3& target = {0.0f, 0.0f, 0.0f},
                       CameraMode mode = CameraMode::FirstPerson);
    
    void SetActiveCamera(Entity cameraEntity);
    Entity GetActiveCamera() const { return m_activeCamera; }
    
    // Camera control
    void SetCameraMode(Entity cameraEntity, CameraMode mode);
    void SetCameraTarget(Entity cameraEntity, Entity targetEntity);
    void SetCameraPosition(Entity cameraEntity, const Vector3& position);
    void SetCameraRotation(Entity cameraEntity, float yaw, float pitch, float roll = 0.0f);
    
    // Camera transitions
    void TransitionToPosition(Entity cameraEntity, const Vector3& position, 
                            CameraTransition transition = CameraTransition::Smooth, 
                            float duration = 1.0f);
    void TransitionToTarget(Entity cameraEntity, const Vector3& target,
                          CameraTransition transition = CameraTransition::Smooth,
                          float duration = 1.0f);
    void TransitionToMode(Entity cameraEntity, CameraMode newMode,
                         CameraTransition transition = CameraTransition::Smooth,
                         float duration = 1.0f);
    
    // Configuration
    void SetCameraConfig(Entity cameraEntity, const CameraConfig& config);
    CameraConfig GetCameraConfig(Entity cameraEntity) const;
    
    // Input integration
    void SetInputSystem(Input::InputSystem* inputSystem);
    void EnableCameraInput(Entity cameraEntity, bool enabled);
    
    // Utility functions
    Vector3 ScreenToWorld(Entity cameraEntity, const Vector2& screenPos, float depth = 1.0f);
    Vector2 WorldToScreen(Entity cameraEntity, const Vector3& worldPos);
    bool IsPointVisible(Entity cameraEntity, const Vector3& point);

private:
    // Active camera
    Entity m_activeCamera;
    
    // Input system integration
    Input::InputSystem* m_inputSystem;
    
    // Registered input actions
    bool m_inputActionsRegistered;
    
    // Internal methods
    void UpdateFirstPersonCamera(Entity entity, CameraComponent& camera, CameraController& controller, DeltaTime deltaTime);
    void UpdateThirdPersonCamera(Entity entity, CameraComponent& camera, CameraController& controller, DeltaTime deltaTime);
    void UpdateOrbitalCamera(Entity entity, CameraComponent& camera, CameraController& controller, DeltaTime deltaTime);
    void UpdateFixedCamera(Entity entity, CameraComponent& camera, CameraController& controller, DeltaTime deltaTime);
    
    void ProcessInput(Entity entity, CameraController& controller);
    void UpdateCameraTransition(CameraComponent& camera, CameraController& controller, DeltaTime deltaTime);
    void ApplyConstraints(CameraComponent& camera, const CameraConfig& config);
    void SmoothCameraMovement(CameraComponent& camera, DeltaTime deltaTime);
    
    // Collision detection
    bool CheckCameraCollision(const Vector3& position, float radius);
    Vector3 ResolveCameraCollision(const Vector3& desiredPosition, const Vector3& currentPosition, float radius);
    
    // Math utilities
    float LerpAngle(float from, float to, float t);
    Vector3 SmoothDamp(const Vector3& current, const Vector3& target, Vector3& velocity, float smoothTime, DeltaTime deltaTime);
    float EaseInOut(float t);
    
    bool m_initialized = false;
};

// ============================================================================
// Camera Manager (Singleton)
// ============================================================================

class CameraManager {
public:
    static CameraManager& GetInstance();
    
    void Initialize();
    void Shutdown();
    
    CameraSystem* GetCameraSystem() { return m_cameraSystem; }
    
    // Convenience methods
    Entity CreateCamera(const Vector3& position = {0.0f, 0.0f, 5.0f}, 
                       const Vector3& target = {0.0f, 0.0f, 0.0f},
                       CameraMode mode = CameraMode::FirstPerson);
    void SetActiveCamera(Entity cameraEntity);
    Entity GetActiveCamera() const;

private:
    CameraManager() = default;
    ~CameraManager() = default;
    CameraManager(const CameraManager&) = delete;
    CameraManager& operator=(const CameraManager&) = delete;
    
    CameraSystem* m_cameraSystem = nullptr;
};

} // namespace Camera
} // namespace Engine
