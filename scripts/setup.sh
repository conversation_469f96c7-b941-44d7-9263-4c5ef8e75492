#!/bin/bash

# macOS 3D Looter-Shooter Game Setup Script
# This script configures the build environment and dependencies

set -e

echo "🎮 Setting up macOS 3D Looter-Shooter Game Development Environment"
echo "=================================================================="

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ Error: This project requires macOS"
    exit 1
fi

# Check macOS version
macos_version=$(sw_vers -productVersion)
echo "📱 macOS Version: $macos_version"

# Check if Xcode is installed
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ Error: Xcode is required but not installed"
    echo "Please install Xcode from the App Store"
    exit 1
fi

xcode_version=$(xcodebuild -version | head -n1)
echo "🔨 $xcode_version"

# Check if CMake is installed
if ! command -v cmake &> /dev/null; then
    echo "⚠️  CMake not found. Installing via Homebrew..."
    if ! command -v brew &> /dev/null; then
        echo "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    brew install cmake
fi

cmake_version=$(cmake --version | head -n1)
echo "⚙️  $cmake_version"

# Check Metal support
echo "🔍 Checking Metal support..."
if system_profiler SPDisplaysDataType | grep -q "Metal"; then
    echo "✅ Metal support detected"
else
    echo "⚠️  Metal support not clearly detected - proceeding anyway"
fi

# Create build directory
echo "📁 Creating build directory..."
mkdir -p build
cd build

# Configure CMake
echo "⚙️  Configuring CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Debug

echo ""
echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. cd build"
echo "2. make -j$(sysctl -n hw.ncpu)"
echo "3. ./MacOS3DLooterShooter"
echo ""
echo "Or open the project in Xcode:"
echo "1. cmake .. -G Xcode"
echo "2. open MacOS3DLooterShooter.xcodeproj"
