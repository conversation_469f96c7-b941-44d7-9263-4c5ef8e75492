# macOS 3D Looter-Shooter Game Makefile

.PHONY: all clean setup debug release run test docs help

# Default target
all: debug

# Setup the build environment
setup:
	@echo "Setting up build environment..."
	@./scripts/setup.sh

# Debug build
debug:
	@echo "Building debug version..."
	@mkdir -p build
	@cd build && cmake .. -DCMAKE_BUILD_TYPE=Debug
	@cd build && make -j$$(sysctl -n hw.ncpu)

# Release build
release:
	@echo "Building release version..."
	@mkdir -p build
	@cd build && cmake .. -DCMAKE_BUILD_TYPE=Release
	@cd build && make -j$$(sysctl -n hw.ncpu)

# Release with debug info
relwithdebinfo:
	@echo "Building release with debug info..."
	@mkdir -p build
	@cd build && cmake .. -DCMAKE_BUILD_TYPE=RelWithDebInfo
	@cd build && make -j$$(sysctl -n hw.ncpu)

# Run the game
run: debug
	@echo "Running the game..."
	@cd build && ./MacOS3DLooterShooter

# Run tests
test: debug
	@echo "Running tests..."
	@cd build && make test

# Generate documentation
docs:
	@echo "Generating documentation..."
	@mkdir -p build
	@cd build && cmake ..
	@cd build && make docs

# Clean build files
clean:
	@echo "Cleaning build files..."
	@rm -rf build

# Xcode project generation
xcode:
	@echo "Generating Xcode project..."
	@mkdir -p build
	@cd build && cmake .. -G Xcode
	@echo "Open build/MacOS3DLooterShooter.xcodeproj in Xcode"

# Install the game
install: release
	@echo "Installing the game..."
	@cd build && make install

# Help target
help:
	@echo "Available targets:"
	@echo "  setup          - Set up the build environment"
	@echo "  debug          - Build debug version (default)"
	@echo "  release        - Build optimized release version"
	@echo "  relwithdebinfo - Build release with debug symbols"
	@echo "  run            - Build and run the game"
	@echo "  test           - Run unit tests"
	@echo "  docs           - Generate documentation"
	@echo "  clean          - Clean build files"
	@echo "  xcode          - Generate Xcode project"
	@echo "  install        - Install the game system-wide"
	@echo "  help           - Show this help message"
