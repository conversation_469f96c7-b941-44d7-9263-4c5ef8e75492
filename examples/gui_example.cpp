//
// gui_example.cpp
// macOS 3D Looter-Shooter Game Engine
//
// Example demonstrating how to use the GUI system
//

#include "gui/GUI.h"
#include "engine/Engine.h"
#include "input/InputSystem.h"
#include <iostream>

using namespace Engine;
using namespace Engine::GUI;

class GUIExample {
public:
    bool Initialize() {
        std::cout << "🎨 Initializing GUI Example..." << std::endl;
        
        // Initialize engine
        if (!m_engine.Initialize()) {
            std::cerr << "❌ Failed to initialize engine" << std::endl;
            return false;
        }
        
        // Initialize input system
        m_inputManager.Initialize();
        auto* inputSystem = m_inputManager.GetInputSystem();
        
        // Initialize GUI system
        if (!InitializeGUISystem(&m_engine.GetECS(), inputSystem, nullptr, nullptr, nullptr)) {
            std::cerr << "❌ Failed to initialize GUI system" << std::endl;
            return false;
        }
        
        // Set up GUI configuration
        UIConfig config;
        config.canvasSize = Vector2(1280, 720);
        config.enableDebugRendering = true;
        SetGUIConfig(config);
        
        // Create the UI
        CreateUI();
        
        std::cout << "✅ GUI Example initialized" << std::endl;
        return true;
    }
    
    void CreateUI() {
        std::cout << "🎯 Creating example UI..." << std::endl;
        
        // Create main menu
        CreateMainMenu();
        
        // Create game HUD
        CreateGameHUD();
        
        // Create settings panel
        CreateSettingsPanel();
        
        // Show main menu initially
        ShowMainMenu();
    }
    
    void CreateMainMenu() {
        // Background panel
        m_mainMenuBackground = CreatePanel(0, 0, 1280, 720, Color(0.1f, 0.1f, 0.2f, 1.0f));
        
        // Title
        m_titleLabel = CreateLabel("GUI System Demo", 640, 150, 48);
        
        // Menu buttons
        m_newGameButton = CreateButton("New Game", 540, 300, 200, 60, [this]() {
            std::cout << "🎮 New Game clicked" << std::endl;
            ShowGameHUD();
        });
        
        m_settingsButton = CreateButton("Settings", 540, 380, 200, 60, [this]() {
            std::cout << "⚙️ Settings clicked" << std::endl;
            ShowSettings();
        });
        
        m_exitButton = CreateButton("Exit", 540, 460, 200, 60, [this]() {
            std::cout << "🚪 Exit clicked" << std::endl;
            m_running = false;
        });
        
        // Add all menu entities to a list for easy management
        m_mainMenuEntities = {m_mainMenuBackground, m_titleLabel, m_newGameButton, m_settingsButton, m_exitButton};
    }
    
    void CreateGameHUD() {
        // Health bar
        m_healthBar = CreateProgressBarWithLabel("Health", 100.0f, Vector2(50, 50), Vector2(200, 30));
        
        // Mana bar
        m_manaBar = CreateProgressBarWithLabel("Mana", 75.0f, Vector2(50, 90), Vector2(200, 30));
        
        // Score label
        m_scoreLabel = CreateLabel("Score: 0", 50, 130, 18);
        
        // Back to menu button
        m_backButton = CreateButton("Menu", 1150, 50, 100, 40, [this]() {
            std::cout << "🔙 Back to menu" << std::endl;
            ShowMainMenu();
        });
        
        // Add HUD entities to list
        m_hudEntities = {m_healthBar, m_manaBar, m_scoreLabel, m_backButton};
        
        // Hide HUD initially
        SetEntitiesVisible(m_hudEntities, false);
    }
    
    void CreateSettingsPanel() {
        // Settings background
        m_settingsBackground = CreatePanel(300, 150, 680, 420, Color(0.2f, 0.2f, 0.2f, 0.9f));
        
        // Settings title
        m_settingsTitle = CreateLabel("Settings", 640, 200, 32);
        
        // Volume slider
        m_volumeSlider = CreateSlider(400, 280, 300, 30, 0.0f, 100.0f, 75.0f);
        m_volumeLabel = CreateLabel("Volume: 75%", 400, 250, 16);
        
        // Fullscreen toggle
        m_fullscreenButton = CreateButton("Fullscreen: OFF", 400, 340, 200, 40, [this]() {
            m_fullscreen = !m_fullscreen;
            std::cout << "🖥️ Fullscreen: " << (m_fullscreen ? "ON" : "OFF") << std::endl;
            // Update button text would require text component access
        });
        
        // Close settings button
        m_closeSettingsButton = CreateButton("Close", 720, 340, 100, 40, [this]() {
            std::cout << "❌ Close settings" << std::endl;
            ShowMainMenu();
        });
        
        // Add settings entities to list
        m_settingsEntities = {m_settingsBackground, m_settingsTitle, m_volumeSlider, 
                             m_volumeLabel, m_fullscreenButton, m_closeSettingsButton};
        
        // Hide settings initially
        SetEntitiesVisible(m_settingsEntities, false);
    }
    
    Entity CreateProgressBarWithLabel(const std::string& label, float value, const Vector2& position, const Vector2& size) {
        // Create label
        CreateLabel(label, position.x, position.y - 25, 14);
        
        // Create progress bar background
        Entity background = CreatePanel(position.x, position.y, size.x, size.y, Color(0.3f, 0.3f, 0.3f, 1.0f));
        
        // Create progress bar fill
        Entity fill = CreatePanel(position.x, position.y, size.x * (value / 100.0f), size.y, Color::Green());
        
        return background; // Return the main entity
    }
    
    void ShowMainMenu() {
        SetEntitiesVisible(m_mainMenuEntities, true);
        SetEntitiesVisible(m_hudEntities, false);
        SetEntitiesVisible(m_settingsEntities, false);
        m_currentState = State::MainMenu;
    }
    
    void ShowGameHUD() {
        SetEntitiesVisible(m_mainMenuEntities, false);
        SetEntitiesVisible(m_hudEntities, true);
        SetEntitiesVisible(m_settingsEntities, false);
        m_currentState = State::Game;
    }
    
    void ShowSettings() {
        SetEntitiesVisible(m_mainMenuEntities, false);
        SetEntitiesVisible(m_hudEntities, false);
        SetEntitiesVisible(m_settingsEntities, true);
        m_currentState = State::Settings;
    }
    
    void SetEntitiesVisible(const std::vector<Entity>& entities, bool visible) {
        // In a real implementation, this would set the visibility of renderer components
        // For now, we'll just print the action
        std::cout << "🔍 Setting " << entities.size() << " entities " 
                  << (visible ? "visible" : "hidden") << std::endl;
    }
    
    void Run() {
        std::cout << "🎮 Starting GUI Example..." << std::endl;
        
        auto lastTime = std::chrono::high_resolution_clock::now();
        
        while (m_running) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            float deltaTime = std::chrono::duration<float>(currentTime - lastTime).count();
            lastTime = currentTime;
            
            // Update engine
            m_engine.Update(deltaTime);
            
            // Update GUI system
            UpdateGUISystem(deltaTime);
            
            // Update game logic based on current state
            UpdateGameLogic(deltaTime);
            
            // Simple exit condition for demo
            static float totalTime = 0.0f;
            totalTime += deltaTime;
            if (totalTime > 60.0f) {  // Run for 1 minute
                std::cout << "⏰ Demo time completed" << std::endl;
                m_running = false;
            }
            
            // Simulate frame rate
            std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
        }
    }
    
    void UpdateGameLogic(float deltaTime) {
        if (m_currentState == State::Game) {
            // Simulate game updates
            static float gameTime = 0.0f;
            gameTime += deltaTime;
            
            // Update score every second
            if (static_cast<int>(gameTime) != m_lastScoreUpdate) {
                m_score += 10;
                m_lastScoreUpdate = static_cast<int>(gameTime);
                std::cout << "📊 Score updated: " << m_score << std::endl;
            }
            
            // Simulate health/mana changes
            static float healthTime = 0.0f;
            healthTime += deltaTime;
            if (healthTime > 3.0f) {
                m_health = std::max(0.0f, m_health - 5.0f);
                m_mana = std::min(100.0f, m_mana + 2.0f);
                healthTime = 0.0f;
                std::cout << "❤️ Health: " << m_health << ", 💙 Mana: " << m_mana << std::endl;
            }
        }
    }
    
    void Shutdown() {
        std::cout << "🔧 Shutting down GUI Example..." << std::endl;
        
        // Shutdown GUI system
        ShutdownGUISystem();
        
        // Shutdown input system
        m_inputManager.Shutdown();
        
        // Shutdown engine
        m_engine.Shutdown();
        
        std::cout << "✅ GUI Example shutdown complete" << std::endl;
    }

private:
    enum class State {
        MainMenu,
        Game,
        Settings
    };
    
    // Core systems
    Engine::Engine m_engine;
    Engine::Input::InputManager m_inputManager;
    
    // UI entities
    std::vector<Entity> m_mainMenuEntities;
    std::vector<Entity> m_hudEntities;
    std::vector<Entity> m_settingsEntities;
    
    // Specific entities
    Entity m_mainMenuBackground, m_titleLabel, m_newGameButton, m_settingsButton, m_exitButton;
    Entity m_healthBar, m_manaBar, m_scoreLabel, m_backButton;
    Entity m_settingsBackground, m_settingsTitle, m_volumeSlider, m_volumeLabel;
    Entity m_fullscreenButton, m_closeSettingsButton;
    
    // State
    State m_currentState = State::MainMenu;
    bool m_running = true;
    bool m_fullscreen = false;
    
    // Game data
    float m_health = 100.0f;
    float m_mana = 75.0f;
    int m_score = 0;
    int m_lastScoreUpdate = -1;
};

int main(int argc, char* argv[]) {
    try {
        GUIExample example;
        
        if (!example.Initialize()) {
            std::cerr << "❌ Failed to initialize GUI example" << std::endl;
            return 1;
        }
        
        example.Run();
        example.Shutdown();
        
        std::cout << "🎉 GUI Example completed successfully!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Fatal error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ Unknown fatal error occurred" << std::endl;
        return 1;
    }
}
