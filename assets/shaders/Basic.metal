//
// Basic.metal
// macOS 3D Looter-Shooter Game Engine
//
// Basic Metal shaders for rendering
//

#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

// ============================================================================
// Vertex Structures
// ============================================================================

struct Vertex {
    float3 position [[attribute(0)]];
    float3 normal [[attribute(1)]];
    float2 texCoord [[attribute(2)]];
};

struct PBRVertex {
    float3 position [[attribute(0)]];
    float3 normal [[attribute(1)]];
    float3 tangent [[attribute(2)]];
    float2 texCoord [[attribute(3)]];
    float4 color [[attribute(4)]];
};

// ============================================================================
// Uniform Structures
// ============================================================================

struct FrameUniforms {
    float4x4 viewMatrix;
    float4x4 projectionMatrix;
    float4x4 viewProjectionMatrix;
    float3 cameraPosition;
    float time;
    float3 lightDirection;
    float deltaTime;
    float3 lightColor;
    float ambientIntensity;
};

struct ObjectUniforms {
    float4x4 modelMatrix;
    float4x4 normalMatrix;
    float4x4 mvpMatrix;
    float4 color;
    float metallic;
    float roughness;
    float ao;
    float _padding;
};

struct MaterialUniforms {
    float3 albedo;
    float metallic;
    float3 emissive;
    float roughness;
    float ao;
    float normalScale;
    float emissiveStrength;
    float _padding;
};

// ============================================================================
// Vertex Shader Outputs
// ============================================================================

struct VertexOut {
    float4 position [[position]];
    float3 worldPosition;
    float3 worldNormal;
    float2 texCoord;
    float4 color;
};

struct PBRVertexOut {
    float4 position [[position]];
    float3 worldPosition;
    float3 worldNormal;
    float3 worldTangent;
    float2 texCoord;
    float4 color;
    float3 viewDirection;
};

// ============================================================================
// Basic Vertex Shader
// ============================================================================

vertex VertexOut basic_vertex_shader(Vertex in [[stage_in]],
                                     constant FrameUniforms& frameUniforms [[buffer(1)]],
                                     constant ObjectUniforms& objectUniforms [[buffer(2)]]) {
    VertexOut out;
    
    // Transform position to world space
    float4 worldPosition = objectUniforms.modelMatrix * float4(in.position, 1.0);
    out.worldPosition = worldPosition.xyz;
    
    // Transform position to clip space
    out.position = frameUniforms.viewProjectionMatrix * worldPosition;
    
    // Transform normal to world space
    out.worldNormal = normalize((objectUniforms.normalMatrix * float4(in.normal, 0.0)).xyz);
    
    // Pass through texture coordinates
    out.texCoord = in.texCoord;
    
    // Use object color
    out.color = objectUniforms.color;
    
    return out;
}

// ============================================================================
// Basic Fragment Shader
// ============================================================================

fragment float4 basic_fragment_shader(VertexOut in [[stage_in]],
                                      constant FrameUniforms& frameUniforms [[buffer(0)]],
                                      texture2d<float> albedoTexture [[texture(0)]],
                                      sampler textureSampler [[sampler(0)]]) {
    
    // Sample albedo texture
    float4 albedo = albedoTexture.sample(textureSampler, in.texCoord);
    albedo *= in.color;
    
    // Simple Lambertian lighting
    float3 lightDir = normalize(-frameUniforms.lightDirection);
    float3 normal = normalize(in.worldNormal);
    float NdotL = max(dot(normal, lightDir), 0.0);
    
    // Calculate lighting
    float3 ambient = frameUniforms.lightColor * frameUniforms.ambientIntensity;
    float3 diffuse = frameUniforms.lightColor * NdotL;
    
    float3 finalColor = albedo.rgb * (ambient + diffuse);
    
    return float4(finalColor, albedo.a);
}

// ============================================================================
// PBR Vertex Shader
// ============================================================================

vertex PBRVertexOut pbr_vertex_shader(PBRVertex in [[stage_in]],
                                      constant FrameUniforms& frameUniforms [[buffer(1)]],
                                      constant ObjectUniforms& objectUniforms [[buffer(2)]]) {
    PBRVertexOut out;
    
    // Transform position to world space
    float4 worldPosition = objectUniforms.modelMatrix * float4(in.position, 1.0);
    out.worldPosition = worldPosition.xyz;
    
    // Transform position to clip space
    out.position = frameUniforms.viewProjectionMatrix * worldPosition;
    
    // Transform normal and tangent to world space
    out.worldNormal = normalize((objectUniforms.normalMatrix * float4(in.normal, 0.0)).xyz);
    out.worldTangent = normalize((objectUniforms.modelMatrix * float4(in.tangent, 0.0)).xyz);
    
    // Calculate view direction
    out.viewDirection = normalize(frameUniforms.cameraPosition - worldPosition.xyz);
    
    // Pass through texture coordinates and color
    out.texCoord = in.texCoord;
    out.color = in.color;
    
    return out;
}

// ============================================================================
// PBR Fragment Shader
// ============================================================================

// Fresnel-Schlick approximation
float3 fresnelSchlick(float cosTheta, float3 F0) {
    return F0 + (1.0 - F0) * pow(1.0 - cosTheta, 5.0);
}

// Distribution function (GGX/Trowbridge-Reitz)
float distributionGGX(float3 N, float3 H, float roughness) {
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH * NdotH;
    
    float num = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = M_PI_F * denom * denom;
    
    return num / denom;
}

// Geometry function (Smith's method)
float geometrySchlickGGX(float NdotV, float roughness) {
    float r = (roughness + 1.0);
    float k = (r * r) / 8.0;
    
    float num = NdotV;
    float denom = NdotV * (1.0 - k) + k;
    
    return num / denom;
}

float geometrySmith(float3 N, float3 V, float3 L, float roughness) {
    float NdotV = max(dot(N, V), 0.0);
    float NdotL = max(dot(N, L), 0.0);
    float ggx2 = geometrySchlickGGX(NdotV, roughness);
    float ggx1 = geometrySchlickGGX(NdotL, roughness);
    
    return ggx1 * ggx2;
}

fragment float4 pbr_fragment_shader(PBRVertexOut in [[stage_in]],
                                   constant FrameUniforms& frameUniforms [[buffer(0)]],
                                   constant MaterialUniforms& materialUniforms [[buffer(1)]],
                                   texture2d<float> albedoTexture [[texture(0)]],
                                   texture2d<float> normalTexture [[texture(1)]],
                                   texture2d<float> metallicTexture [[texture(2)]],
                                   texture2d<float> roughnessTexture [[texture(3)]],
                                   texture2d<float> aoTexture [[texture(4)]],
                                   sampler textureSampler [[sampler(0)]]) {
    
    // Sample textures
    float3 albedo = albedoTexture.sample(textureSampler, in.texCoord).rgb * materialUniforms.albedo;
    float metallic = metallicTexture.sample(textureSampler, in.texCoord).r * materialUniforms.metallic;
    float roughness = roughnessTexture.sample(textureSampler, in.texCoord).r * materialUniforms.roughness;
    float ao = aoTexture.sample(textureSampler, in.texCoord).r * materialUniforms.ao;
    
    // Sample and apply normal map
    float3 normalMap = normalTexture.sample(textureSampler, in.texCoord).rgb * 2.0 - 1.0;
    normalMap.xy *= materialUniforms.normalScale;
    
    // Calculate TBN matrix for normal mapping
    float3 N = normalize(in.worldNormal);
    float3 T = normalize(in.worldTangent);
    float3 B = cross(N, T);
    float3x3 TBN = float3x3(T, B, N);
    
    // Apply normal map
    N = normalize(TBN * normalMap);
    
    // Lighting vectors
    float3 V = normalize(in.viewDirection);
    float3 L = normalize(-frameUniforms.lightDirection);
    float3 H = normalize(V + L);
    
    // Calculate reflectance at normal incidence
    float3 F0 = mix(float3(0.04), albedo, metallic);
    
    // Cook-Torrance BRDF
    float NDF = distributionGGX(N, H, roughness);
    float G = geometrySmith(N, V, L, roughness);
    float3 F = fresnelSchlick(max(dot(H, V), 0.0), F0);
    
    float3 kS = F;
    float3 kD = float3(1.0) - kS;
    kD *= 1.0 - metallic;
    
    float3 numerator = NDF * G * F;
    float denominator = 4.0 * max(dot(N, V), 0.0) * max(dot(N, L), 0.0) + 0.001;
    float3 specular = numerator / denominator;
    
    // Add to outgoing radiance Lo
    float NdotL = max(dot(N, L), 0.0);
    float3 Lo = (kD * albedo / M_PI_F + specular) * frameUniforms.lightColor * NdotL;
    
    // Ambient lighting
    float3 ambient = frameUniforms.lightColor * frameUniforms.ambientIntensity * albedo * ao;
    
    // Add emissive
    float3 emissive = materialUniforms.emissive * materialUniforms.emissiveStrength;
    
    float3 color = ambient + Lo + emissive;
    
    // HDR tonemapping (simple Reinhard)
    color = color / (color + float3(1.0));
    
    // Gamma correction
    color = pow(color, float3(1.0/2.2));
    
    return float4(color, 1.0);
}

// ============================================================================
// Shadow Mapping Shaders
// ============================================================================

vertex float4 shadow_vertex_shader(Vertex in [[stage_in]],
                                   constant ObjectUniforms& objectUniforms [[buffer(2)]]) {
    float4 worldPosition = objectUniforms.modelMatrix * float4(in.position, 1.0);
    return objectUniforms.mvpMatrix * float4(in.position, 1.0);
}

fragment void shadow_fragment_shader() {
    // Depth is automatically written
}
